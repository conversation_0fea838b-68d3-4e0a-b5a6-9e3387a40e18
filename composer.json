{"name": "vertigolabs/mockingbird", "require": {"cache/predis-adapter": "^1.1", "cboden/ratchet": "^0.4.3", "clue/redis-react": "^2.5", "doctrine/dbal": "^3.1", "monolog/monolog": "2.2.0", "nesbot/carbon": "^2.53", "ramsey/uuid": "^4.2", "react/http": "1.5.0", "react/zmq": "^0.4.0", "symfony/config": "^5.3", "symfony/console": "^5.3", "symfony/dependency-injection": "^5.3", "symfony/dotenv": "^5.3", "symfony/finder": "^5.3", "symfony/monolog-bridge": "^5.3", "symfony/property-access": "v5.3.0", "vertigolabs/data-aware": "1.0.1", "vertigolabs/logger-aware": "1.0.3", "vertigolabs/validation-aware": "1.0.4", "voryx/thruway": "v0.6.0", "wyrihaximus/react-cron": "^3.1", "rollbar/rollbar": "^3.0", "clue/block-react": "^1.5", "drift/dbal": "^0.1.4", "mongodb/mongodb": "^1.6", "guzzlehttp/guzzle": "^7.5"}, "autoload": {"psr-4": {"Mockingbird\\": "src"}}}