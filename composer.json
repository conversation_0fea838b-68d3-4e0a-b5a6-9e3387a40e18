{"name": "clubster/peregrine", "description": "", "minimum-stability": "dev", "prefer-stable": true, "license": "proprietary", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.3", "cycle/database": "^2.12", "guzzlehttp/guzzle": "^7.9", "nesbot/carbon": "^3.9", "nyholm/psr7": "^1.8", "open-telemetry/exporter-otlp": "*", "open-telemetry/transport-grpc": "*", "ramsey/uuid": "^4.7", "roadrunner-php/app-logger": "^1.2", "roadrunner-php/centrifugo": "^2.2", "spiral/roadrunner-cli": "^2.7", "spiral/roadrunner-http": "^3.5", "spiral/roadrunner-kv": "^4.3", "spiral/tokenizer": "^3.15", "symfony/config": "^7.2", "symfony/dependency-injection": "^7.2", "symfony/dotenv": "^7.2", "temporal/open-telemetry-interceptors": "dev-master", "temporal/sdk": "^2.13", "vertigolabs/data-aware": "1.0.3", "thecodingmachine/safe": "^3.1", "mongodb/mongodb": "^1.20", "aws/aws-sdk-php": "^3.343"}, "autoload": {"psr-4": {"Clubster\\Peregrine\\": "src"}}, "config": {"allow-plugins": {"php-http/discovery": true, "tbachert/spi": true}}, "scripts": {"pre-autoload-dump": "Aws\\Script\\Composer\\Composer::removeUnusedServices"}, "extra": {"aws/aws-sdk-php": ["S3", "CloudFront"]}}