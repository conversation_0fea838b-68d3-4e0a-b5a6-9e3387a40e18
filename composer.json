{"name": "clubster/falcon", "description": "a high performance api", "minimum-stability": "stable", "license": "proprietary", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"ext-dom": "*", "aws/aws-sdk-php": "^3.158", "cboden/ratchet": "^0.4.3", "doctrine/dbal": "^3.0", "mongodb/mongodb": "^1.6", "monolog/monolog": "^2.1", "nesbot/carbon": "2.70.0", "phpoffice/phpspreadsheet": "^1.16", "ramsey/uuid": "^4.1", "ratchet/pawl": "^0.3.5", "react/http": "1.5", "symfony/config": "^5.1", "symfony/dependency-injection": "^5.1", "symfony/dotenv": "^5.1", "symfony/finder": "^5.1", "symfony/property-access": "^5.1", "symfony/validator": "^5.1", "twig/twig": "^3.1", "vanilla/garden-cli": "^3.1", "voryx/thruway": "^0.6.0", "wyrihaximus/react-cron": "^3.1", "dusterio/link-preview": "^1.2", "tgalopin/html-sanitizer": "^1.5", "simshaun/recurr": "^5.0", "evenement/evenement": "^3.0", "spatie/calendar-links": "^1.6", "async-aws/sqs": "^1.8", "promphp/prometheus_client_php": "^2.10", "sentry/sentry": "4.14.1", "http-interop/http-factory-guzzle": "^1.2", "somnambulist/cte-builder": "^3.4", "particle/filter": "^1.5"}, "require-dev": {"phpunit/phpunit": "^12.2"}, "autoload": {"psr-4": {"Clubster\\Falcon\\": "falcon/"}}, "config": {"allow-plugins": {"php-http/discovery": true}}}