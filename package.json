{"devDependencies": {"@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.15.0", "@symfony/webpack-encore": "^0.33.0", "@types/react": "^17.0.38", "@types/react-dom": "^17.0.11", "autoprefixer": "^10.4.7", "babel-preset-react": "^6.24.1", "babel-preset-stage-1": "^6.24.1", "postcss": "^8.2.1", "postcss-import": "^14.0.0", "postcss-loader": "^4.1.0", "postcss-purgecss": "^2.0.3", "raw-loader": "^4.0.2", "tailwindcss": "^3.4.1", "ts-loader": "^8.0.1", "typescript": "^4.5.5"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production"}, "dependencies": {"@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-runtime": "^7.16.10", "axios": "^0.24.0", "caniuse-lite": "^1.0.30001363", "centrifuge": "^5.3.5", "classnames": "^2.2.6", "cropperjs": "^1.5.6", "daisyui": "4.6.2", "expose-loader": "^0.7.5", "fullcalendar": "3.10.1", "jquery": "^3.4.1", "lodash": "^4.17.15", "medium-editor": "^5.23.3", "mime": "^3.0.0", "mixing": "^1.3.0", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "next": "^15.3.4", "nodelist": "^2.0.1", "prop-types": "^15.7.2", "react": "^17.0.2", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^17.0.2", "react-to-webcomponent": "^1.5.1", "rrule": "^2.6.8", "sass": "^1.51.0", "sass-loader": "10.0.0", "tinymce": "6.7.1", "velocity-animate": "^1.5.2"}}