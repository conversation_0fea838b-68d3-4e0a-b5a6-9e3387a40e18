{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react", "declaration": false, "baseUrl": "./", "paths": {"@/*": ["assets/*"]}}, "include": ["assets/**/*.ts", "assets/**/*.tsx", "assets/**/*.js", "assets/**/*.jsx"], "exclude": ["node_modules", "public", "var", "vendor"]}