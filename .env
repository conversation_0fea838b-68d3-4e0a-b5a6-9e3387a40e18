TASK_ID=c36b1d38-e9b6-4ef5-918d-ac3c48785d62
API_ENV=cardinal
WWW_URL=${SERVICE_WEB_ENDPOINT}

MONGO_DB_URI=${SERVICE_MONGODB_ENDPOINT}
MONGO_DB_NAME=${SERVICE_MONGODB_DATABASE}
MONGO_COLLECTION_NAME=notification_queue

INTERVAL_SLEEP_TIME=1
EMPTY_QUEUE_SLEEP_TIME=10
INTERVAL_MAX_MESSAGES=20

AWS_SES_REGION=us-east-1
AWS_SES_CONFIG_SET=cardinal_tracking

DB_HOST=*************
DB_USERNAME=cardinal
DB_PASSWORD=cardinal
DB_NAME=cardinal
DB_PORT=5432

STATISTICS_DB_HOST=*************
STATISTICS_DB_USERNAME=cardinal
STATISTICS_DB_PASSWORD=WQowQZZRZK-jM4P8JgkM
STATISTICS_DB_NAME=cardinal
STATISTICS_DB_PORT=5432

NOTIFICATION_PROCESSOR_EMAIL=1
NOTIFICATION_PROCESSOR_PUSH=0
NOTIFICATION_PROCESSOR_INTERNAL=1

SOCKET_URL=http://${SERVICE_CENTRIFUGO_ENDPOINT}/api
SOCKET_API_KEY=${SOCKET_API_KEY}
SOCKET_SECRET_KEY=${SOCKET_SECRET_KEY}

CLUSTER_NAME=cardinal
#FORCE_RECIPIENT=<EMAIL>
#KEEP_QUEUE=1
SENTRY_DSN=https://<EMAIL>/****************

DEBUG_CLUBSTER_NIGHTINGALE=${DEBUG_CLUBSTER_NIGHTINGALE:-false}
XDEBUG_CONFIG=${XDEBUG_CONFIG:-false}
PHP_IDE_CONFIG="serverName=clubster-nightingale"

REDIS_HOST=clubster-redis
REDIS_PORT=6379
REDIS_DB=6

AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID:-false}
AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY:-false}
