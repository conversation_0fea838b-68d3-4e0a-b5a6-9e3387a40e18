MONGO_DB_URI=${SERVICE_MONGODB_ENDPOINT}
MONGO_DB_NAME=${SERVICE_MONGODB_DATABASE}

INTERVAL_SLEEP_TIME=3

SOCKET_URL=http://${SERVICE_CENTRIFUGO_ENDPOINT}/api
SOCKET_API_KEY=${SOCKET_API_KEY}
SOCKET_SECRET_KEY=${SOCKET_SECRET_KEY}

AWS_ECS_REGION=us-east-2

FALCON_URL=${SERVICE_FALCON_ENDPOINT}

STATISTICS_DB_ENDPOINT=${SERVICE_TIMESCALE_ENDPOINT}

# Nightingale Queue Service Scaling
MAXIMUM_DESIRED_QUEUE_TIME=300
SCALING_COOLDOWN=600
MAXIMUM_DESIRED_TASKS=16
MINIMUM_DESIRED_TASKS=2
NIGHTINGALE_QUEUE_ECS_CLUSTER=cluster-falcon-prod-off
NIGHTINGALE_QUEUE_ECS_SERVICE=Nightingale

DEBUG_CLUBSTER_PUFFIN=${DEBUG_CLUBSTER_PUFFIN:-false}
XDEBUG_CONFIG=${XDEBUG_CONFIG:-false}
PHP_IDE_CONFIG="serverName=clubster-puffin"

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=6

AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID:-false}
AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY:-false}
