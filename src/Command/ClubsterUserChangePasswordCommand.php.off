<?php

namespace App\Command;

use App\Domain\User\Command\LoadUser;
use App\Domain\User\Command\Security\ResetPassword;
use App\Domain\User\Entity\User;
use Symfony\Bridge\Doctrine\ManagerRegistry;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ContainerInterface;

class ClubsterUserChangePasswordCommand extends Command
{
    protected static $defaultName = 'clubster:user:change-password';
    /**
     * @var ContainerInterface
     */
    private ContainerInterface $container;

    public function __construct(string $name = null, ContainerInterface $container)
    {
        parent::__construct($name);
        $this->container = $container;
    }

    protected function configure()
    {
        $this
            ->setDescription('Change a users password')
            ->addOption('email', null, InputOption::VALUE_REQUIRED, 'The email id')
            ->addOption('password', null, InputOption::VALUE_REQUIRED, 'The users password')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        /** @var ManagerRegistry $doctrine */
        $doctrine = $this->container->get('doctrine');
        $user = $doctrine->getRepository(User::class)->findOneBy(['email'=>$input->getOption('email')]);
        $passwordEncoder = $this->container->get('security.password_encoder');
        $password = $passwordEncoder->encodePassword($user, $input->getOption('password'));
        $user->setPassword($password);
        $doctrine->getManager()->flush();

        $io->success('New password is set');

        return 0;
    }
}
