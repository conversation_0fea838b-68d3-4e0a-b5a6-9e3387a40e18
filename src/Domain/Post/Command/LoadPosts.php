<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 9/23/2019
 * @time: 7:41 AM
 */

namespace App\Domain\Post\Command;

use App\Infrastructure\EventSourcing\Command\Command;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\Constraint;

class LoadPosts extends Command
{
    protected function run()
    {

    }

    public function buildConstraints(): ?Constraint
    {
        return new Constraints\Collection([
            'fields' => [

            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }

    public function getRequiredRoles(): array
    {
        return ['IS_AUTHENTICATED_ANONYMOUSLY'];
    }
}