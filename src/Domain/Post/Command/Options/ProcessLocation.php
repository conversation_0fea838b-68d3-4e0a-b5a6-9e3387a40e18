<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 10/9/2019
 * @time: 1:58 PM
 */

namespace App\Domain\Post\Command\Options;

use App\Domain\Post\Entity\Post;
use App\Infrastructure\EventSourcing\Command\Command;
use Carbon\Carbon;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\Constraint;

class ProcessLocation extends Command
{
    protected function run()
    {
        $post = $this->getData('post');

        if ($this->getData('data', false)) {
            $post->setLocation($this->getData('data.location'));
        }else{
            $post->setLocation(null);
        }

        return $post;
    }

    public function buildConstraints(): ?Constraint
    {
        return new Constraints\Collection([
            'fields' => [
                'post'=>new Constraints\Required(new Constraints\Type(Post::class)),
                'data'=>new Constraints\Optional()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }

    public function getRequiredRoles(): array
    {
        return ['IS_AUTHENTICATED_ANONYMOUSLY'];
    }
}