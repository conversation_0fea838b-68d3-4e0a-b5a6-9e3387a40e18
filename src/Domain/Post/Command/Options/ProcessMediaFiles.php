<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 10/9/2019
 * @time: 2:06 PM
 */

namespace App\Domain\Post\Command\Options;

use App\Domain\Post\Command\CreatePostMedia;
use App\Domain\Post\Entity\Post;
use App\Domain\Video\Command\CreateYoutubeLink;
use App\Infrastructure\EventSourcing\Command\Command;
use Doctrine\ORM\PersistentCollection;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\Constraint;

class ProcessMediaFiles extends Command
{
    protected function run()
    {
        $post = $this->getData('post');

        if ($this->getData('data.mediaFiles', false)) {
            $existingMedia = [];
            foreach ($post->getMedia() as $postMedia) {
                $existingMedia[$postMedia->getFile()->getId()] = $postMedia;
            }
            $inputMedia = [];
            foreach ($this->getData('data.mediaFiles') as $newMediaFile) {
                $inputMedia[$newMediaFile['uuid']] = $newMediaFile;
            }

            $newMedia = array_diff_key($inputMedia, $existingMedia);
            $removedMedia = array_diff_key($existingMedia, $inputMedia);
            $unchangedMedia = array_intersect_key($existingMedia, $inputMedia);

            if (count($newMedia) > 0 ) {
                $createPostMedia = $this->container->get(CreatePostMedia::class);

                foreach ($newMedia as $mediaFile) {
                    $createPostMedia->setData([
                        'fileName' => $mediaFile['fileName'],
                        'file' => strtolower($mediaFile['uuid'] . '.' . pathinfo($mediaFile['fileName'], PATHINFO_EXTENSION)),
                        'uuid'=>$mediaFile['uuid'],
                        'post' => $post
                    ]);
                    $violations = $createPostMedia->validate();
                    if (null !== $violations && $violations->count() === 0) {
                        $attachment = $createPostMedia();
                        $post->addMedia($attachment);
                    }
                }
            }

            if (count($removedMedia)> 0) {
                foreach ($removedMedia as $removed) {
                    $this->getDoctrine()->getManager()->remove($removed);
                }
            }

        }else{
            foreach ($post->getMedia() as $postMedia) {
                $this->getDoctrine()->getManager()->remove($postMedia);
            }
        }

        return $post;
    }

    public function buildConstraints(): ?Constraint
    {
        return new Constraints\Collection([
            'fields' => [
                'post'=>new Constraints\Required(new Constraints\Type(Post::class)),
                'data'=>new Constraints\Optional()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }

    public function getRequiredRoles(): array
    {
        return ['IS_AUTHENTICATED_ANONYMOUSLY'];
    }
}
