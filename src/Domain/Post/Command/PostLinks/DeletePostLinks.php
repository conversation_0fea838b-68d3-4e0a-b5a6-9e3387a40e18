<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 3/10/2020
 * @time: 3:15 PM
 */

namespace App\Domain\Post\Command\PostLinks;

use App\Domain\Post\Entity\PostLink;
use App\Infrastructure\EventSourcing\Command\Command;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\Constraint;

class DeletePostLinks extends Command
{
    protected function run()
    {
        /** @var PostLink[] $links */
        $links = $this->getDoctrine()->getRepository(PostLink::class)->findBy(['post'=> $this->getData('post')]);

        foreach ($links as $link) {
            $this->getDoctrine()->getManager()->remove($link);
        }

        return $links;
    }

    public function buildConstraints(): ?Constraint
    {
        return new Constraints\Collection([
            'fields' => [
                'post'=>new Constraints\Required()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }

    public function getRequiredRoles(): array
    {
        return ['IS_AUTHENTICATED_ANONYMOUSLY'];
    }
}
