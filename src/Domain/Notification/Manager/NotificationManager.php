<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 12/6/2019
 * @time: 9:18 AM
 */

namespace App\Domain\Notification\Manager;

use App\Domain\Notification\Command\SendNotification;
use App\Domain\Notification\Entity\Notification;
use App\Domain\Notification\Entity\NotificationTemplate;
use App\Domain\Organization\Command\Members\LoadOrganizationMember;
use App\Domain\Organization\Entity\Organization;
use App\Domain\Organization\Entity\OrganizationMember;
use App\Domain\Post\Entity\PostNotification;
use App\Domain\User\Command\Mobile\LoadPushTokens;
use App\Domain\User\Entity\User;
use App\Infrastructure\Doctrine\Transaction;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Swoole\Coroutine;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;
use function Co\run;

class NotificationManager implements ContainerAwareInterface, LoggerAwareInterface
{
    use ContainerAwareTrait;
    use LoggerAwareTrait;

    public const DELIVERY_METHOD_EMAIL = 'email';
    public const DELIVERY_METHOD_SMS = 'sms';
    public const DELIVERY_METHOD_PUSH = 'push';
    public const DELIVERY_METHOD_INTERNAL = 'internal';

    public const DELIVERY_PRIORITY_FORCE = 'force';
    public const DELIVERY_PRIORITY_EXCLUDE = 'exclude';
    public const DELIVERY_PRIORITY_ALLOW = 'allow';

    public function handleNotificationTrigger(NotificationTriggerInterface $notificationTrigger)
    {
        if ($notificationTrigger->isNotificationsDisabled()) {
            return;
        }

        $deliveryMethods = $notificationTrigger->getNotificationDeliveryMethods();
        $template = $notificationTrigger->getNotificationTemplate();

        if (count(array_filter($deliveryMethods, fn($method) => !($method === 'exclude'))) === 0) {
            return false;
        }

        if ($template == 'event:published') {
            if (count(array_filter(array_filter($deliveryMethods, fn($type) => $type != 'internal', ARRAY_FILTER_USE_KEY), fn($method) => !($method === 'exclude'))) === 0) {
                return false;
            }
            // this needs to be sent to the task stack

            $client = new Client(['base_uri'=>$_ENV['FALCON_URL']]);
            $data = $notificationTrigger->getNotificationData();
            $resp = $client->request('POST','/', [
                'form_params' => [
                    'task'=>'Event:Notifications:GenerateEventNotifications',
                    'payload'=>[
                        'postId'=>$data['metadata']['id'],
                        'excludedRecipients'=>array_keys($notificationTrigger->getExcludedUsers())
                    ]
                ],
                'verify'=>false
            ]);

            $resp = (string)$resp->getBody()->getContents();

            return;
        }

        if ($notificationTrigger->getNotificationType('') instanceof PostNotification) {
            if (count(array_filter(array_filter($deliveryMethods, fn($type) => $type != 'internal', ARRAY_FILTER_USE_KEY), fn($method) => !($method === 'exclude'))) === 0) {
                return false;
            }
            // this needs to be sent to the task stack

            $client = new Client(['base_uri'=>$_ENV['FALCON_URL']]);
            $data = $notificationTrigger->getNotificationData();
            $resp = $client->request('POST','/', [
                'form_params' => [
                    'task'=>'Post:Notifications:GeneratePostNotifications',
                    'payload'=>[
                        'postId'=>$data['metadata']['id']
                    ]
                ],
                'verify'=>false
            ]);

            $resp = (string)$resp->getBody()->getContents();

            return;
        }

        $data = $notificationTrigger->getNotificationData();
        $recipients = $notificationTrigger->getNotificationRecipients();
        $sender = $notificationTrigger->getNotificationSender();
        $senderOrganization = $notificationTrigger->getNotificationSenderOrganization();
        $replyTo = $notificationTrigger->getNotificationReplyTo();
        $notificationTypeSelector = [$notificationTrigger,'getNotificationType'];

        $this->notify($notificationTypeSelector, $template, $data, $recipients, $deliveryMethods, $sender, $senderOrganization, $replyTo);
    }

    public function notify($notificationTypeSelector, $templateSlug, $data, $recipients, array $deliveryMethods = [], $sender = null, $senderOrganization = null, $replyTo = null)
    {
        if (getenv('DISABLE_NOTIFICATIONS')) {
            return;
        }
        if (!is_array($deliveryMethods)) {
            $deliveryMethods = [$deliveryMethods];
        }

        if (!is_array($recipients)) {
            $recipients = [$recipients];
        }

        if (empty($recipients)) {
            return false;
        }

        if (empty($deliveryMethods)) {
            $deliveryMethods = [
                self::DELIVERY_METHOD_INTERNAL=>self::DELIVERY_PRIORITY_ALLOW,
                self::DELIVERY_METHOD_EMAIL=>self::DELIVERY_PRIORITY_ALLOW,
                self::DELIVERY_METHOD_PUSH=>self::DELIVERY_PRIORITY_ALLOW,
                self::DELIVERY_METHOD_SMS=>self::DELIVERY_PRIORITY_ALLOW,
            ];
        }

        if (count(array_filter($deliveryMethods , function($method) {
            if ($method==='exclude') {
                return false;
            }
            return true;
            })) === 0) {

            return false;
        }

        /** @var NotificationTemplate[] $templates */
        $templates = $this->container->get('doctrine')->getRepository(NotificationTemplate::class)->findBy(['slug'=>$templateSlug]);

        if (empty($templates)) {
            return;
        }

        $data['API_ENV'] = getenv('API_ENV', null);
        $data['WWW_URL'] = getenv('WWW_URL', null);
        if (empty($data['logoLink'])) {
            $data['logoLink'] = getenv('WWW_URL', null);
        }
        if (empty($data['clubsterLink'])) {
            $data['clubsterLink'] = getenv('WWW_URL', null);
        }

        foreach ($templates as $k=> $template) {
            $templates[$template->getDeliveryMethod()] = $template;
            unset($templates[$k]);
        }

        foreach ($recipients as $recipient) {
            try {
                $realizedDeliveryMethods = $this->determineDeliveryMethod($deliveryMethods, $templates, $recipient, $sender, $senderOrganization);
                $this->logger->info('found delivery methods: ' . json_encode($realizedDeliveryMethods));
                foreach ($realizedDeliveryMethods as $deliveryMethod) {

//                $handler = function() use ($notificationTypeSelector, $deliveryMethod, $templates, $data, $recipient, $sender, $senderOrganization, $replyTo) {
                    $notification = $this->prepareNotification($notificationTypeSelector($deliveryMethod), $deliveryMethod, $templates[$deliveryMethod], $data, $recipient, $sender, $senderOrganization, $replyTo);
                    $this->sendNotification($notification);
//                };

//                if (php_sapi_name() === 'cli') {
//                    go(function () use ($handler) {
//                        $this->logger->info('Running Notification CoRoutine');
//                        $handler();
//                    });
//                }else{
//                    $this->logger->info('Running Notification');
//                    $handler();
//                }
                }
            }catch (\Exception $exception) {
                $this->logger->error('[NOT-001] '.$exception->getMessage());
            }
        }
    }

    private function sendNotification(Notification $notification)
    {
        $sendNotifcation = $this->container->get(SendNotification::class)->setData([
            'notification'=>$notification
        ]);

        $dbTransaction = $this->container->get(Transaction::class);
        $dbTransaction($sendNotifcation);
    }

    /**
     * @param Notification $notificationType
     * @param string $deliveryMethod
     * @param NotificationTemplate $template
     * @param array $data
     * @param User $recipient
     * @param User|null $sender
     * @param Organization|null $senderOrganization
     * @param string|null $replyTo
     * @return Notification
     */
    private function prepareNotification(Notification $notificationType, $deliveryMethod, $template,array $data, User $recipient, ?User $sender = null, ?Organization $senderOrganization = null, ?string $replyTo = null): Notification
    {
        $notification = clone $notificationType;
        $notification->setRecipient($recipient)
            ->setDeliveryMethod($deliveryMethod)
            ->setStatus(Notification::STATUS_PENDING)
            ->setSendAt(new Carbon())
            ->setTemplate($template)
            ->setTemplateData($data)
            ->setSender($sender)
            ->setReplyTo($replyTo)
            ->setSenderOrganization($senderOrganization);

//        if ($this->getData('replyTo', false)) {
//            $notification->setReplyTo($this->getData('replyTo'));
//        }

        $this->container->get('doctrine')->getManager()->persist($notification);
        $this->container->get('doctrine')->getManager()->flush();
        return $notification;
    }

    /**
     * @param array $requestedDeliveryMethods
     * @param NotificationTemplate[] $templates
     * @param User $recipient
     * @param User|null $sender
     * @param Organization|null $senderOrganization
     * @return array
     */
    private function determineDeliveryMethod(array $requestedDeliveryMethods, $templates, User $recipient, ?User $sender = null, ?Organization $senderOrganization = null)
    {
        $templateDeliveryMethods = array_fill_keys(array_map(function(NotificationTemplate $template){
            return $template->getDeliveryMethod();
        }, $templates), self::DELIVERY_PRIORITY_ALLOW);


        $membershipDeliveryMethods = [
            self::DELIVERY_METHOD_INTERNAL=>self::DELIVERY_PRIORITY_ALLOW,
            self::DELIVERY_METHOD_EMAIL=>self::DELIVERY_PRIORITY_ALLOW,
        ];

        if (null !== $senderOrganization) {
            $membership = $this->container->get(LoadOrganizationMember::class)->setData([
                'user'=>$recipient,
                'organization'=>$senderOrganization
            ])();

            if ($membership instanceof OrganizationMember) {
                if (!$membership->getAllowEmail()) {
                    $membershipDeliveryMethods[self::DELIVERY_METHOD_EMAIL]=Self::DELIVERY_PRIORITY_EXCLUDE;
                }
                if (!$membership->getAllowPush()) {
                    $membershipDeliveryMethods[self::DELIVERY_METHOD_PUSH]=Self::DELIVERY_PRIORITY_EXCLUDE;
                }
            }
        }

        $userDeliveryMethods = [
            self::DELIVERY_METHOD_INTERNAL=>self::DELIVERY_PRIORITY_ALLOW,
            self::DELIVERY_METHOD_EMAIL=>self::DELIVERY_PRIORITY_ALLOW,
        ];


        try {
//            if ($recipient->hasSetting('enable-email-notifications') && !$recipient->getSetting('enable-email-notifications')) {
//                $userDeliveryMethods[self::DELIVERY_METHOD_EMAIL] = self::DELIVERY_PRIORITY_EXCLUDE;
//            }
//
//            if ($recipient->getSetting('enable-mobile-push-notifications')) {
//                $userDeliveryMethods[self::DELIVERY_METHOD_PUSH] = self::DELIVERY_PRIORITY_ALLOW;
//            }
        }catch (\Exception $e) {
            // squash
        }

        foreach ($requestedDeliveryMethods as $method=>$priority) {
            if (!array_key_exists($method, $templateDeliveryMethods)) {
                $requestedDeliveryMethods[$method] = self::DELIVERY_PRIORITY_EXCLUDE;
                continue;
            }

            if ($priority === self::DELIVERY_PRIORITY_FORCE) {
                continue;
            }

//            if (!array_key_exists($method, $userDeliveryMethods)) {
//                $requestedDeliveryMethods[$method] = self::DELIVERY_PRIORITY_EXCLUDE;
//                continue;
//            }

            if (array_key_exists($method, $userDeliveryMethods) && $userDeliveryMethods[$method] === self::DELIVERY_PRIORITY_EXCLUDE) {
                $requestedDeliveryMethods[$method] = self::DELIVERY_PRIORITY_EXCLUDE;
                continue;
            }

            if (array_key_exists($method, $membershipDeliveryMethods) && $membershipDeliveryMethods[$method] === self::DELIVERY_PRIORITY_EXCLUDE) {
                $requestedDeliveryMethods[$method] = self::DELIVERY_PRIORITY_EXCLUDE;
                continue;
            }
        }

        $pushTokens = $this->container->get(LoadPushTokens::class)->setData(['user'=>$recipient])();
        if (isset($requestedDeliveryMethods[self::DELIVERY_METHOD_PUSH]) && $requestedDeliveryMethods[self::DELIVERY_METHOD_PUSH] != self::DELIVERY_PRIORITY_EXCLUDE) {
            if (!count($pushTokens)) {
                $requestedDeliveryMethods[self::DELIVERY_METHOD_PUSH] = self::DELIVERY_PRIORITY_EXCLUDE;
            }
        }

        if (isset($requestedDeliveryMethods[self::DELIVERY_METHOD_PUSH]) && $requestedDeliveryMethods[self::DELIVERY_METHOD_PUSH] != self::DELIVERY_PRIORITY_EXCLUDE && isset($requestedDeliveryMethods[self::DELIVERY_METHOD_EMAIL]) && $requestedDeliveryMethods[self::DELIVERY_METHOD_EMAIL] != self::DELIVERY_PRIORITY_FORCE) {
            $requestedDeliveryMethods[self::DELIVERY_METHOD_EMAIL] = self::DELIVERY_PRIORITY_EXCLUDE;
        }

        if (count($pushTokens) && $requestedDeliveryMethods[self::DELIVERY_METHOD_EMAIL] != self::DELIVERY_PRIORITY_FORCE) {
            $requestedDeliveryMethods[self::DELIVERY_METHOD_EMAIL] = self::DELIVERY_PRIORITY_EXCLUDE;
        }

        $allowedDeliveryMethods = array_filter($requestedDeliveryMethods, function ($deliveryMethod) {
            return $deliveryMethod !== self::DELIVERY_PRIORITY_EXCLUDE;
        });

        return array_keys($allowedDeliveryMethods);
    }

}
