<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 10/6/2018
 * @time: 7:32 AM
 */

namespace App\Domain\Organization\Entity;


use App\Domain\Organization\Traits\OrganizationAsset;
use App\Domain\User\Model\UserInterface;
use App\Traits\Blameable;
use App\Traits\EntityTrait;
use App\Traits\Timestampable;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Gedmo\SoftDeleteable\Traits\SoftDeleteableEntity;
use J<PERSON>\Serializer\Annotation as Serializer;
use J<PERSON>\Serializer\Annotation\Groups;

/**
 * Class OrganizationContact
 * @package App\Domain\Organization\Entity
 * @ORM\Entity(repositoryClass="App\Domain\Organization\OrganizationContactRepository")
 * @ORM\Table(name="organizations.contacts")
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 */
class OrganizationContact
{
    use EntityTrait;
    use Timestampable;
    use SoftDeleteableEntity;
    use Blameable;
    use OrganizationAsset;

    public const CONTACT_STATUS_PENDING = 'pending';
    public const CONTACT_STATUS_ACCEPTED = 'accepted';
    public const CONTACT_STATUS_DECLINED = 'declined';
    public const CONTACT_STATUS_REVOKED = 'revoked';
    public const CONTACT_STATUS_SUSPENDED = 'suspended';
    public const CONTACT_STATUS_INVITED = 'invited';

    /**
     * @var string
     * @ORM\Column(name="title", type="string", nullable=true)
     * @Groups({"rest","common"})
     */
    private $title;
    /**
     * @var UserInterface
     * @ORM\ManyToOne(targetEntity="App\Domain\User\Entity\User")
     * @Groups({"rest","common"})
     */
    protected $user;
    /**
     * @var \App\Domain\Organization\Entity\ContactPermission[]|\Doctrine\ORM\PersistentCollection
     * @ORM\OneToMany(targetEntity="App\Domain\Organization\Entity\ContactPermission", mappedBy="contact", orphanRemoval=true)
     * @Groups({"rest","common"})
     */
    private $permissions;
    /**
     * @var string
     * @ORM\Column(name="staff_id", type="string", nullable=true)
     * @Groups({"rest","common"})
     */
    private $staffId;
    /**
     * @var string
     * @ORM\Column(name="phone", type="string", nullable=true)
     * @Groups({"rest","common"})
     */
    private $phone;
    /**
     * @var string
     * @ORM\Column(name="phone_extension", type="string", nullable=true)
     * @Groups({"rest","common"})
     */
    private $phoneExtension;
    /**
     * @var string
     * @ORM\Column(name="status", type="string", nullable=true)wdec zqq
     * @Groups({"rest","common"})
     */
    private $status;
    /**
     * @var OrganizationContactInvitation
     * @ORM\OneToMany(targetEntity="App\Domain\Organization\Entity\OrganizationContactInvitation", mappedBy="contact")
     */
    private $invitations;
    /**
     * @var bool
     * @ORM\Column(name="suspended", type="boolean", options={"default":"false"}, nullable=true)
     * @Groups({"rest","common"})
     */
    private $suspended = false;

    /**
     * @return string
     */
    public function getTitle(): ?string
    {
        return $this->title;
    }

    /**
     * @param string $title
     *
     * @return OrganizationContact
     */
    public function setTitle(?string $title): OrganizationContact
    {
        $this->title = $title;
        return $this;
    }

    /**
     * @param UserInterface $user
     *
     * @return self
     */
    public function setUser(UserInterface $user)
    {
        $this->user = $user;
        return $this;
    }

    /**
     * @return UserInterface
     */
    public function getUser(): UserInterface
    {
        return $this->user;
    }

    /**
     * @return \App\Domain\Organization\Entity\ContactPermission[]|\Doctrine\ORM\PersistentCollection
     */
    public function getPermissions()
    {
        return $this->permissions;
    }

    /**
     * @param \App\Domain\Organization\Entity\ContactPermission[]|\Doctrine\ORM\PersistentCollection $permissions
     *
     * @return OrganizationContact
     */
    public function setPermissions($permissions)
    {
        $this->permissions = $permissions;
        return $this;
    }

    /**
     * @return string
     */
    public function getStaffId(): ?string
    {
        return $this->staffId;
    }

    /**
     * @param string $staffId
     * @return OrganizationContact
     */
    public function setStaffId(?string $staffId): OrganizationContact
    {
        $this->staffId = $staffId;
        return $this;
    }

    /**
     * @return string
     */
    public function getPhone(): ?string
    {
        return $this->phone;
    }

    /**
     * @param string $phone
     * @return OrganizationContact
     */
    public function setPhone(?string $phone): OrganizationContact
    {
        $this->phone = $phone;
        return $this;
    }

    /**
     * @return string
     */
    public function getPhoneExtension(): ?string
    {
        return $this->phoneExtension;
    }

    /**
     * @param string $phoneExtension
     * @return OrganizationContact
     */
    public function setPhoneExtension(?string $phoneExtension): OrganizationContact
    {
        $this->phoneExtension = $phoneExtension;
        return $this;
    }

    /**
     * @param string $status
     * @return OrganizationContact
     */
    public function setStatus(string $status): OrganizationContact
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return string
     */
    public function getStatus(): ?string
    {
        return $this->status;
    }

    /**
     * @return bool
     */
    public function isSuspended(): ?bool
    {
        return $this->suspended;
    }

    /**
     * @param bool $suspended
     * @return OrganizationContact
     */
    public function setSuspended(bool $suspended): OrganizationContact
    {
        $this->suspended = $suspended;
        return $this;
    }
}
