<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 5/26/2019
 * @time: 7:54 AM
 */

namespace App\Domain\Organization\Command\ClubLink;

use App\Domain\Organization\Command\LoadOrganization;
use App\Domain\Organization\Entity\ClubLink;
use App\Domain\Organization\Entity\ClubLinkCategory;
use App\Infrastructure\EventSourcing\Command\Command;
use RuntimeException;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\Constraint;

class CreateClubLink extends Command
{
    protected function run()
    {
        $loadOrganization = $this->container->get(LoadOrganization::class);
        $loadOrganization->setData(['organization'=> $this->getData('organization')]);
        $organization = $loadOrganization();

        $category = $this->getData('category', 'General');
        if (!empty($category)) {
            $category = ($this->container->get(LoadClubLinkCategory::class)->setData([
                'category'=>$category,
                'organization'=>$organization->getId()
            ]))();
        }

        if (!($category instanceof ClubLinkCategory)) {
            throw new RuntimeException('Club Link must have a category');
        }

        $clubLink = new ClubLink();
        $clubLink->setName($this->getData('name'))
                ->setDescription($this->getData('description'))
                ->setOrganization($organization)
                ->setSidebar($this->getData('sidebar', false))
                ->setCategory($category)
                ->setPosition($this->getData('position', null));

        $this->getDoctrine()->getManager()->persist($clubLink);

        if ($this->getData('attachment', false)) {
            $createClubLinkAttachment = $this->container->get(CreateClubLinkAttachment::class);
            $createClubLinkAttachment->setData([
                'file'=> $this->getData('attachment.file'),
                'fileName'=> $this->getData('attachment.fileName'),
                'clubLink'=>$clubLink->getId()
            ]);
            $violations = $createClubLinkAttachment->validate();
            if ($violations->count() === 0) {
                $attachment = $createClubLinkAttachment();
                $clubLink->setAttachment($attachment);
            }
        }else{
            $clubLink->setUrl($this->getData('url', null));
        }

        $this->getDoctrine()->getManager()->flush();
        return $clubLink;
    }

    public function buildConstraints(): ?Constraint
    {
        return new Constraints\Collection([
            'fields' => [
                'name'=>new Constraints\Required(),
                'url'=>new Constraints\Optional(),
                'attachment'=>new Constraints\Optional(new Constraints\Collection([
                    'fields' => [
                        'file'=>new Constraints\Optional(),
                        'fileName'=>new Constraints\Optional()
                    ]
                ])),
                'organization'=>new Constraints\Required(),
                'category'=>new Constraints\Optional(),
                'description'=>new Constraints\Optional(),
                'position'=>new Constraints\Optional(),
                'sidebar'=>new Constraints\Optional()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }

    public function getRequiredRoles(): array
    {
        return ['IS_AUTHENTICATED_ANONYMOUSLY'];
    }
}
