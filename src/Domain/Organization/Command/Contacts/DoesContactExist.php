<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 4/30/2020
 * @time: 10:34 AM
 */

namespace App\Domain\Organization\Command\Contacts;

use App\Domain\Organization\Entity\OrganizationContact;
use App\Domain\User\Entity\User;
use App\Infrastructure\EventSourcing\Command\Command;
use Doctrine\ORM\Query\Expr\Join;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\Constraint;
use function Doctrine\ORM\QueryBuilder;

class DoesContactExist extends Command
{
    protected function run()
    {
        $qb = $this->getDoctrine()->getManager()->createQueryBuilder();
        $qb->select('count(c)')
            ->from(OrganizationContact::class, 'c')
            ->innerJoin(User::class, 'u', Join::WITH, 'c.user = u' )
            ->where($qb->expr()->eq('c.organization', ':organization'))
            ->andWhere($qb->expr()->eq('u.email', ':email'))
            ->setMaxResults(1);

        $qb->setParameter('organization', $this->getData('organization'))
            ->setParameter('email', $this->getData('email'));

        $res = $qb->getQuery()->getSingleScalarResult();
        return ['exists'=>$res>0];
    }

    public function buildConstraints(): ?Constraint
    {
        return new Constraints\Collection([
            'fields' => [
                'email'=> new Constraints\Required(),
                'organization'=> new Constraints\Required(),
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }

    public function getRequiredRoles(): array
    {
        return ['IS_AUTHENTICATED_ANONYMOUSLY'];
    }
}
