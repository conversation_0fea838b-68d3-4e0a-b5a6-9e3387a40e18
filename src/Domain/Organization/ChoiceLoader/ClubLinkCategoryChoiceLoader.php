<?php


namespace App\Domain\Organization\ChoiceLoader;

use Symfony\Component\Form\ChoiceList\ArrayChoiceList;
use Symfony\Component\Form\ChoiceList\Loader\ChoiceLoaderInterface;

class ClubLinkCategoryChoiceLoader implements ChoiceLoaderInterface
{
    private $callback;

    /**
     * The loaded choice list.
     *
     * @var ArrayChoiceList
     */
    private $choiceList;

    /**
     * @param callable $callback The callable returning an array of choices
     */
    public function __construct(callable $callback)
    {
        $this->callback = $callback;
    }

    /**
     * {@inheritdoc}
     */
    public function loadChoiceList($value = null)
    {
        if (null !== $this->choiceList) {
            return $this->choiceList;
        }

        return $this->choiceList = new ArrayChoiceList(\call_user_func($this->callback), $value);
    }

    /**
     * {@inheritdoc}
     */
    public function loadChoicesForValues(array $values, $value = null)
    {
        // Optimize
        if (empty($values)) {
            return [];
        }

        $choiceForValues = $this->loadChoiceList($value)->getChoicesForValues($values);

        if (empty($choiceForValues)) {
            $choiceForValues = $values;
        }

        return $choiceForValues;
    }

    /**
     * {@inheritdoc}
     */
    public function loadValuesForChoices(array $choices, $value = null)
    {
        // Optimize
        if (empty($choices)) {
            return [];
        }

        $valuesForChoice = $this->loadChoiceList($value)->getValuesForChoices($choices);

        if (empty($valuesForChoice)) {
            $valuesForChoice = $choices;
        }

        return $valuesForChoice;
    }
}