<?php


namespace App\Domain\EntityAttributes\Entity;

use App\Traits\EntityTrait;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * Class Attribute
 * @package App\Domain\EntityAttributes\Entity
 * @ORM\Entity()
 * @ORM\Table(name="entity_attributes.attributes")
 * @ORM\InheritanceType("JOINED")
 * @ORM\DiscriminatorColumn(name="_discr", type="string")
 */
class Attribute
{
    use EntityTrait;

    public const VALUE_TYPE_TEXT = 'text';
    public const VALUE_TYPE_NUMBER = 'number';
    public const VALUE_TYPE_BOOLEAN = 'boolean';
    public const VALUE_TYPE_DATE = 'date';
    public const VALUE_TYPE_DATETIME = 'datetime';

    public const VISIBILITY_PUBLIC = 'public';
    public const VISIBILITY_PROTECTED = 'protected';
    public const VISIBILITY_PRIVATE = 'private';

    /**
     * @var string
     * @ORM\Column(name="title", type="string")
     * @Serializer\Groups({"rest","common","full","summary","attributes"})
     */
    protected $title;
    /**
     * @var string|null
     * @ORM\Column(name="description", type="text", nullable=true)
     * @Serializer\Groups({"rest","common","full","summary","attributes"})
     */
    protected $description;
    /**
     * @var bool
     * @ORM\Column(name="required", type="boolean")
     * @Serializer\Groups({"rest","common","full","summary","attributes"})
     */
    protected $required;
    /**
     * @var string
     * @ORM\Column(name="value_type", type="string")
     * @Serializer\Groups({"rest","common","full","summary","attributes"})
     */
    protected $valueType;
    /**
     * @var bool
     * @ORM\Column(name="allow_multiple_values", type="boolean")
     * @Serializer\Groups({"rest","common","full","summary","attributes"})
     */
    protected $allowMultipleValues;
    /**
     * @var string|null
     * @ORM\Column(name="default_value", type="string", nullable=true)
     * @Serializer\Groups({"rest","common","full","summary","attributes"})
     */
    protected $defaultValue;
    /**
     * @var string
     * @ORM\Column(name="visibility", type="string")
     * @Serializer\Groups({"rest","common","full","summary","attributes"})
     */
    protected $visibility;
    /**
     * @var AttributeOption[]|null
     * @ORM\OneToMany(targetEntity="AttributeOption", mappedBy="attribute")
     * @ORM\OrderBy({"order":"ASC"})
     * @Serializer\Groups({"rest","common","full","summary","attributes"})
     */
    protected $options;
    /**
     * @var string
     * @ORM\Column(name="category", type="string")
     * @Serializer\Groups({"rest","common","full","summary","attributes"})
     */
    protected $category;
    /**
     * @var string
     * @ORM\Column(name="slug", type="string")
     * @Serializer\Groups({"rest","common","full","summary","attributes"})
     */
    protected $slug;
    /**
     * @var string[]|null
     * @ORM\Column(name="applicable_entity_types", type="jsonb")
     */
    protected $applicableEntityTypes;

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * @param string $title
     * @return Attribute
     */
    public function setTitle(string $title): Attribute
    {
        $this->title = $title;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getDescription(): ?string
    {
        return $this->description;
    }

    /**
     * @param string|null $description
     * @return Attribute
     */
    public function setDescription(?string $description): Attribute
    {
        $this->description = $description;
        return $this;
    }

    /**
     * @return bool
     */
    public function isRequired(): bool
    {
        return $this->required;
    }

    /**
     * @param bool $required
     * @return Attribute
     */
    public function setRequired(bool $required): Attribute
    {
        $this->required = $required;
        return $this;
    }

    /**
     * @return string
     */
    public function getValueType(): string
    {
        return $this->valueType??self::VALUE_TYPE_TEXT;
    }

    /**
     * @param string $valueType
     * @return Attribute
     */
    public function setValueType(string $valueType): Attribute
    {
        $this->valueType = $valueType;
        return $this;
    }

    /**
     * @return bool
     */
    public function isAllowMultipleValues(): bool
    {
        return $this->allowMultipleValues;
    }

    /**
     * @param bool $allowMultipleValues
     * @return Attribute
     */
    public function setAllowMultipleValues(bool $allowMultipleValues): Attribute
    {
        $this->allowMultipleValues = $allowMultipleValues;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getDefaultValue(): ?string
    {
        return $this->defaultValue;
    }

    /**
     * @param string|null $defaultValue
     * @return Attribute
     */
    public function setDefaultValue(?string $defaultValue): Attribute
    {
        $this->defaultValue = $defaultValue;
        return $this;
    }

    /**
     * @return string
     */
    public function getVisibility(): string
    {
        return $this->visibility;
    }

    /**
     * @param string $visibility
     * @return Attribute
     */
    public function setVisibility(string $visibility): Attribute
    {
        $this->visibility = $visibility;
        return $this;
    }

    /**
     * @return AttributeOption[]|null
     */
    public function getOptions()
    {
        return $this->options;
    }

    /**
     * @param AttributeOption[]|null $options
     * @return Attribute
     */
    public function setOptions( $options): Attribute
    {
        $this->options = $options;
        return $this;
    }

    /**
     * @return string
     */
    public function getCategory(): string
    {
        return $this->category;
    }

    /**
     * @param string $category
     * @return Attribute
     */
    public function setCategory(string $category): Attribute
    {
        $this->category = $category;
        return $this;
    }

    /**
     * @return string
     */
    public function getSlug(): string
    {
        return $this->slug;
    }

    /**
     * @param string $slug
     * @return Attribute
     */
    public function setSlug(string $slug): Attribute
    {
        $this->slug = $slug;
        return $this;
    }

    /**
     * @return string[]|null
     */
    public function getApplicableEntityTypes(): ?array
    {
        return $this->applicableEntityTypes;
    }

    /**
     * @param string[]|null $applicableEntityTypes
     * @return Attribute
     */
    public function setApplicableEntityTypes(?array $applicableEntityTypes): Attribute
    {
        $this->applicableEntityTypes = $applicableEntityTypes;
        return $this;
    }
}
