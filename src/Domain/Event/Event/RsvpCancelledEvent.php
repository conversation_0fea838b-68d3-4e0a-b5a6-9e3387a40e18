<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 3/26/2020
 * @time: 1:37 PM
 */

namespace App\Domain\Event\Event;

use App\Domain\Notification\Manager\NotificationManager;
use App\Domain\Notification\Manager\NotificationTrigger;
use App\Domain\Notification\Manager\NotificationTriggerInterface;

class RsvpCancelledEvent extends RsvpEvent implements NotificationTriggerInterface
{
    use NotificationTrigger;

    public function getNotificationTemplate()
    {
        return 'rsvp:cancelled';
    }

    public function getNotificationDeliveryMethods()
    {
        return [
            NotificationManager::DELIVERY_METHOD_EMAIL=>NotificationManager::DELIVERY_PRIORITY_FORCE,
            NotificationManager::DELIVERY_METHOD_INTERNAL=>NotificationManager::DELIVERY_PRIORITY_ALLOW,
            NotificationManager::DELIVERY_METHOD_PUSH=>NotificationManager::DELIVERY_PRIORITY_EXCLUDE,
        ];
    }

    public function getNotificationRec<PERSON>()
    {
        return $this->getRSVP()->getEvent()->getCreatedBy();
    }

    public function getNotificationSender()
    {
        return $this->getRSVP()->getCreatedBy();
    }

    public function getNotificationSenderOrganization()
    {
        return null;
    }
}
