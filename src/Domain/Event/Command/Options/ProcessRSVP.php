<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 11/7/2019
 * @time: 5:34 PM
 */

namespace App\Domain\Event\Command\Options;

use App\Domain\Event\Entity\Event;
use App\Infrastructure\EventSourcing\Command\Command;
use Carbon\Carbon;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\Constraint;

class ProcessRSVP extends Command
{
    protected function run()
    {
        /** @var Event $event */
        $event = $this->getData('event');

        $event->setAllowChildren($this->getData('data.allowChildren', true));

        if ($this->getData('data.enableRSVP', false)) {
            $event->setEnableRsvp($this->getData('data.enableRSVP', false));

            if ($this->getData('data.limitAttendees', false)) {
                $event->setMaximumAttendees($this->getData('data.maximumAttendees', 0));
            }else{
                $event->setMaximum<PERSON>(0);
            }

            if ($this->getData('data.limitRSVPAttendees', false)) {
                $event->setRsvpMaximumAttendees(min($this->getData('data.rsvpMaximumAttendees', 51), 50));
            }else{
                $event->setRsvpMaximumAttendees(0);
            }
            $event->setRsvpRequireEmail($this->getData('data.requireAttendeeEmail', false));
            $event->setRsvpRequireName($this->getData('data.requireAttendeeName', false));
            $event->setAllowChildren($this->getData('data.allowChildren', true));
            $event->setEnableWaitList($this->getData('data.enableWaitList', false));
            $event->setRsvpAllowNotes($this->getData('data.allowRSVPNotes', false));
            $event->setRsvpAllowAttendeeNotes($this->getData('data.allowRSVPAttendeeNotes', false));

            if ($this->getData('data.limitRSVPDates', false)) {
                if ($this->getData('data.rsvpOpenDate', false)) {
                    $rsvpStart = (new Carbon($this->getData('data.rsvpOpenDate')))->startOfDay();

                    if (null === $event->getRsvpOpenDate() || $rsvpStart->startOfMinute()->notEqualTo($event->getRsvpOpenDate()->startOfMinute())) {
                        $correctedStartTime = new Carbon('now', $this->getData('data.timezone', $event->getTimezone()));
                        $correctedStartTime->setDateTimeFrom($rsvpStart);
                        $correctedStartTime->setTimezone(date_default_timezone_get());
                        $rsvpStart = $correctedStartTime;
                    }

                    $event->setRsvpOpenDate($rsvpStart);
                }else{
                    $event->setRsvpOpenDate(null);
                }

                if ($this->getData('data.rsvpCloseDate', false)) {
                    $rsvpEnd = (new Carbon($this->getData('data.rsvpCloseDate')))->startOfDay();

                    if (null === $event->getRsvpOpenDate() || $rsvpEnd->startOfMinute()->notEqualTo($event->getRsvpOpenDate()->startOfMinute())) {
                        $correctedEndTime = new Carbon('now', $this->getData('data.timezone', $event->getTimezone()));
                        $correctedEndTime->setDateTimeFrom($rsvpEnd);
                        $correctedEndTime->setTimezone(date_default_timezone_get());
                        $rsvpEnd = $correctedEndTime;
                    }

                    $event->setRsvpCloseDate($rsvpEnd);
                }else{
                    $event->setRsvpCloseDate(null);
                }
            }else{
                $event->setRsvpOpenDate(null);
                $event->setRsvpCloseDate(null);
            }
        }else{
            if (count($event->getRSVPs()) === 0) {
                $event->setEnableRsvp(false);
            }
        }

        return $event;
    }

    public function buildConstraints(): ?Constraint
    {
        return new Constraints\Collection([
            'fields' => [
                'event'=>new Constraints\Required(new Constraints\Type(Event::class)),
                'data'=>new Constraints\Optional()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }

    public function getRequiredRoles(): array
    {
        return ['IS_AUTHENTICATED_ANONYMOUSLY'];
    }
}
