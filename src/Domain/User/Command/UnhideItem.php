<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 11/29/2019
 * @time: 9:44 AM
 */

namespace App\Domain\User\Command;

use App\Domain\Organization\Entity\Organization;
use App\Domain\Post\Entity\Post;
use App\Domain\User\Entity\HiddenItem;
use App\Domain\User\Entity\User;
use App\Infrastructure\EventSourcing\Command\Command;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\Constraint;

class UnhideItem extends Command
{
    protected function run()
    {

        $em = $this->getDoctrine()->getManager();

        $criteria = array_filter([
            'user'=>$this->getUser(),
            'post'=>$this->getData('post', null),
            'author'=>$this->getData('author', null),
            'organization'=>$this->getData('organization', null)
        ]);

        /** @var HiddenItem[] $hiddenItems */
        if (!empty($criteria)) {
            $hiddenItems = $this->getDoctrine()->getRepository(HiddenItem::class)->findBy($criteria);
        }

        foreach($hiddenItems as $hiddenItem) {
            $em->remove($hiddenItem);
        }

        return ['status'=>true];
    }

    public function buildConstraints(): ?Constraint
    {
        return new Constraints\Collection([
            'fields' => [
                'post'=>new Constraints\Optional(),
                'author'=>new Constraints\Optional(),
                'organization'=>new Constraints\Optional(),
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }

    public function getRequiredRoles(): array
    {
        return ['IS_AUTHENTICATED_ANONYMOUSLY'];
    }
}
