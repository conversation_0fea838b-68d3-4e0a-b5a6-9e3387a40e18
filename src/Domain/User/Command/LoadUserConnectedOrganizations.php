<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 12/16/2018
 * @time: 8:27 AM
 */

namespace App\Domain\User\Command;

use App\Domain\Club\Entity\Club;
use App\Domain\Group\Entity\Group;
use App\Domain\Organization\Entity\Organization;
use App\Domain\Organization\Entity\OrganizationContact;
use App\Domain\Organization\Entity\OrganizationMember;
use App\Domain\Subscription_delete\Entity\Subscription;
use App\Infrastructure\EventSourcing\Command\Command;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\Constraint;

class LoadUserConnectedOrganizations extends Command
{
    protected function run()
    {
        $loadUserConnectedClubs = $this->container->get(LoadUserConnectedClubs::class);
        $loadUserConnectedGroups = $this->container->get(LoadUserConnectedGroups::class);

        $data = [
            'user'=> $this->getData('user'),
            'acceptedOnly'=> $this->getData('acceptedOnly', false)
        ];
        $loadUserConnectedClubs->setData($data);
        $loadUserConnectedGroups->setData($data);

        if ($loadUserConnectedGroups->validate()->count() > 0 || $loadUserConnectedClubs->validate()->count() > 0) {
            throw new \RuntimeException('User information missing');
        }

        $connectedGroups = $loadUserConnectedGroups();
        $connectedClubs = $loadUserConnectedClubs();

        $orgs = array_merge($connectedClubs, $connectedGroups);
        $connectedOrgs = [];
        if ($this->getData('verbose')) {
            $connectedOrgs = $orgs;
        }else {
            foreach ($orgs as $org) {
                $connectedOrgs[$org->getId()] = $org;
            }
        }

        if ($this->getData('filterClubGroups', true)) {
            $connectedOrgs = array_filter($connectedOrgs, function (Organization $organization) {
                if ($organization instanceof Group && $organization->getParentOrganization() instanceof Organization) {
                    return false;
                }
                return true;
            });
        }
        return array_values($connectedOrgs);
    }

    public function buildConstraints(): ?Constraint
    {
        return new Constraints\Collection([
            'fields'             => [
                'user'=>new Constraints\Required([new Constraints\NotBlank(), new Constraints\Uuid()]),
                'filterClubGroups'=> new Constraints\Optional(new Constraints\Type('boolean')),
                'acceptedOnly'=> new Constraints\Optional(),
                'verbose' => new Constraints\Optional()
            ],
            'allowExtraFields'   => true,
            'allowMissingFields' => true
        ]);
    }

    public function buildDefaults(): ?array
    {
        return [
            'filterClubGroups'=>true,
            'verbose'=>false
        ];
    }

    public function getRequiredRoles(): array
    {
        return ['IS_AUTHENTICATED_ANONYMOUSLY'];
    }
}
