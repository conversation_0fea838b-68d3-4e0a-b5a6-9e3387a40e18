<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 11/13/2018
 * @time: 5:35 AM
 */

namespace App\Domain\User\Entity;

use App\Domain\Attribute\Entity\AttributeValue;
use App\Domain\User\Model\UserAssetInterface;
use App\Domain\User\Traits\UserAsset;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class UserAttributeValue
 * @package App\Domain\User\Entity
 * @ORM\Entity()
 * @ORM\Table(name="attributes.user_attribute_values")
 */
class UserAttributeValue extends AttributeValue implements UserAssetInterface
{
    use UserAsset;
}
