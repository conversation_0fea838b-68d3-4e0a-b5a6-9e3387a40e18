<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 7/31/2018
 * @time: 12:31 PM
 */

namespace App\Domain\Comment\Entity;


use App\Domain\AggregateEntityInterface;
use App\Domain\Comment\Model\CommentInterface;
use App\Infrastructure\EventSourcing\AggregateRoot;
use App\Traits\AggregateEntityTrait;
use App\Traits\Blameable;
use App\Traits\Timestampable;
use Doctrine\ORM\Mapping as ORM;
use Fresh\DoctrineEnumBundle\Validator\Constraints\Enum;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * Class Comment
 * @IgnoreAnnotation("Enum")
 * @package App\Domain\Comment\Entity
 * @ORM\Entity(repositoryClass="CommentRepository")
 * @ORM\Table(name="comments")
 * @ORM\InheritanceType("JOINED")
 * @ORM\DiscriminatorColumn(name="_discr", type="string")
 */
class Comment extends AggregateRoot implements AggregateEntityInterface, CommentInterface
{
    use AggregateEntityTrait;
    use Timestampable;
    use Blameable;

    /**
     * @var string
     * @ORM\Column(name="status", type="CommentStatusType", nullable=false)
     * @Enum(entity="App\Domain\Comment\Types\CommentStatusType")
     * @Groups({"rest","common"})
     */
    protected $status;
    /**
     * @var string
     * @ORM\Column(type="text")
     * @Groups({"rest","common"})
     */
    protected $message;

    /**
     * @var Comment[]|\Doctrine\Common\Collections\Collection
     * @ORM\OneToMany(targetEntity="App\Domain\Comment\Entity\Comment", mappedBy="parent")
     * @Groups({"rest","common"})
     */
    protected $children;
    /**
     * @var Comment
     * @\Doctrine\ORM\Mapping\ManyToOne(targetEntity="App\Domain\Comment\Entity\Comment", inversedBy="children")
     * @ORM\JoinColumn(name="parent_id", referencedColumnName="id")
     */
    protected $parent;

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @return CommentInterface
     */
    public function setStatus(mixed $status): CommentInterface
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getMessage()
    {
        return $this->message;
    }

    /**
     * @return CommentInterface
     */
    public function setMessage(mixed $message): CommentInterface
    {
        $this->message = $message;
        return $this;
    }


}
