<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 7/31/2018
 * @time: 12:58 PM
 */

namespace App\Domain\Comment\Types;

use Fresh\DoctrineEnumBundle\DBAL\Types\AbstractEnumType;

final class CommentStatusType extends AbstractEnumType
{
    public const DRAFT = 'draft';
    public const PENDING = 'pending';
    public const PUBLISHED = 'published';
    public const DELETED = 'deleted';

// jxmot 4048 - did not use rector to find this, it caused a crash
    protected static array $choices = [
    //protected static $choices = [
        self::DRAFT => self::DRAFT,
        self::PENDING => self::PENDING,
        self::PUBLISHED => self::PUBLISHED,
        self::DELETED => self::DELETED
    ];
}
