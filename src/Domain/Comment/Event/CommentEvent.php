<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 3/11/2020
 * @time: 3:12 PM
 */
namespace App\Domain\Comment\Event;

use App\Domain\Comment\Entity\Comment;
use Symfony\Component\EventDispatcher\Event;

abstract class CommentEvent extends Event
{
    /**
     * @var Comment
     */
    private $comment;

    /**
     * CommentEvent constructor.
     */
    public function __construct(Comment $comment)
    {
        $this->comment = $comment;
    }

    /**
     * @return Comment
     */
    public function getComment(): Comment
    {
        return $this->comment;
    }
}
