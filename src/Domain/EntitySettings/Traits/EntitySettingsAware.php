<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 3/6/2020
 * @time: 10:55 AM
 */

namespace App\Domain\EntitySettings\Traits;

use App\Domain\EntitySettings\Command\LoadEntitySettings;
use App\Domain\EntitySettings\Command\LoadRealizedEntitySettings;
use App\Domain\EntitySettings\Entity\SettingValue;
use App\Kernel;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use JMS\Serializer\Annotation as Serializer;

/**
 * Trait EntitySettingsAware
 * @package App\Domain\EntitySettings\Traits
 */
trait EntitySettingsAware
{
    /**
     * @var SettingValue[]|ArrayCollection|null
     * @ORM\ManyToMany(targetEntity="App\Domain\EntitySettings\Entity\SettingValue", indexBy="slug")
     */
    protected $settings;
    /**
     * @var array
     * @Serializer\Exclude
     */
    protected $realizedSettings = [];

    /**
     * @return SettingValue[]|ArrayCollection|null
     */
    public function getSettings()
    {
        return $this->settings;
    }

    public function addSetting(SettingValue $setting)
    {
        if (!($this->settings instanceof ArrayCollection)) {
            $this->settings = new ArrayCollection([]);
        }
        $this->settings->add($setting);
        return $this;
    }

    /**
     * @param ArrayCollection|array|null $settings
     * @return EntitySettingsAware
     */
    public function setSettings($settings)
    {
        if (!($settings instanceof ArrayCollection) && is_array($settings)) {
            $settings = new ArrayCollection($settings);
        }
        $this->settings = $settings;
        return $this;
    }

    public function hasSetting($settingSlug)
    {
        foreach ($this->settings as $settingValue) {
            if ($settingValue->getSetting()->getSlug() === $settingSlug) {
                return null !== $settingValue->getId();
            }
        }
        return false;
    }

    public function getSetting($settingSlug, $useDefault = false)
    {
        foreach ($this->settings as $settingValue) {
            if ($settingValue->getSetting()->getSlug() === $settingSlug) {
                if ($settingValue->getSetting()->getValueType() == 'boolean') {
                    return (bool)(int)$settingValue->getValue();
                }
                return $settingValue->getValue();
            }
        }
        if ($useDefault && isset($this->realizedSettings[$settingSlug])) {
            if ($this->realizedSettings[$settingSlug]->getSetting()->getValueType() == 'boolean') {
                return (bool)(int)$this->realizedSettings[$settingSlug]->getValue();
            }
            return $this->realizedSettings[$settingSlug]->getValue();
        }
        return false;
    }

    /**
     * @param array $realizedSettings
     * @return EntitySettingsAware
     */
    public function setRealizedSettings(array $realizedSettings)
    {
        $this->realizedSettings = $realizedSettings;
        return $this;
    }
}
