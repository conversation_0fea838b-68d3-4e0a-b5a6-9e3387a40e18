<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 11/21/2018
 * @time: 11:19 AM
 */

namespace App\Domain\Geography\Entity;

use App\Traits\Blameable;
use App\Traits\EntityTrait;
use App\Traits\Timestampable;
use Carbon\Carbon;
use Doctrine\ORM\Mapping as ORM;
use JMS\Serializer\Annotation as Serializer;
use Gedmo\SoftDeleteable\Traits\SoftDeleteableEntity;

/**
 * Class Address
 * @package App\Domain\Geography\Entity
 * @ORM\Entity(repositoryClass="App\Repository\AddressRepository")
 * @ORM\Table(name="geography.addresses")
 */
class Address
{
    use EntityTrait;
    use Timestampable;
    use SoftDeleteableEntity;
    use Blameable;

    /**
     * @var string|int|null
     * @ORM\Column(name="street_number", type="string", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $streetNumber;

    /**
     * @var string|null
     * @ORM\Column(name="street_name", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $streetName;

    /**
     * @var string|null
     * @ORM\Column(name="sub_locality", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $subLocality;

    /**
     * @var string|null
     * @ORM\Column(name="locality", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $locality;
    /**
     * @var string|null
     * @ORM\Column(name="region_code", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $regionCode;
    /**
     * @var string|null
     * @ORM\Column(name="region_name", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $regionName;
    /**
     * @var string|null
     * @ORM\Column(name="postal_code", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $postalCode;

    /**
     * @var string[]
     * @ORM\Column(name="admin_levels", type="json_array", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $adminLevels;

    /**
     * @var string|null
     * @ORM\Column(name="country_name", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $countryName;

    /**
     * @var string|null
     * @ORM\Column(name="country_code", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $countryCode;

    /**
     * @var float
     * @ORM\Column(name="latitude", type="float", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $latitude;

    /**
     * @var float
     * @ORM\Column(name="longitude", type="float", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $longitude;

    /**
     * @var float[]|null
     * @ORM\Column(name="bounds", type="json_array", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $bounds;

    /**
     * @var Timezone|null
     * @ORM\ManyToOne(targetEntity="App\Domain\Geography\Entity\Timezone")
     * @ORM\JoinColumn(name="timezone", referencedColumnName="id", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $timezone;

    /**
     * @var string
     * @ORM\Column(name="geocode_provider", type="string", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $geocodeProvider;

    /**
     * @var \DateTimeInterface
     * @ORM\Column(name="geocoded_at", type="datetime", nullable=true)
     * @Serializer\Type("Carbon<'Y-m-d\TH:i:sP'>")
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $geocodedAt;

    /**
     * @var string
     * @ORM\Column(name="raw_string")
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $rawString;

    /**
     * @var string
     * @ORM\Column(name="formatted_string", nullable=true)
     * @Serializer\Groups({"rest","common","full"})
     */
    protected $formattedString;

    /**
     * @return int|null|string
     */
    public function getStreetNumber()
    {
        return $this->streetNumber;
    }

    /**
     * @param int|null|string $streetNumber
     *
     * @return Address
     */
    public function setStreetNumber($streetNumber)
    {
        $this->streetNumber = $streetNumber;
        return $this;
    }

    /**
     * @return null|string
     */
    public function getStreetName(): ?string
    {
        return $this->streetName;
    }

    /**
     * @param null|string $streetName
     *
     * @return Address
     */
    public function setStreetName(?string $streetName): Address
    {
        $this->streetName = $streetName;
        return $this;
    }

    /**
     * @return null|string
     */
    public function getSubLocality(): ?string
    {
        return $this->subLocality;
    }

    /**
     * @param null|string $subLocality
     *
     * @return Address
     */
    public function setSubLocality(?string $subLocality): Address
    {
        $this->subLocality = $subLocality;
        return $this;
    }

    /**
     * @return null|string
     */
    public function getLocality(): ?string
    {
        return $this->locality;
    }

    /**
     * @param null|string $locality
     *
     * @return Address
     */
    public function setLocality(?string $locality): Address
    {
        $this->locality = $locality;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getRegionCode(): ?string
    {
        return $this->regionCode;
    }

    /**
     * @param string|null $regionCode
     *
     * @return Address
     */
    public function setRegionCode(?string $regionCode): Address
    {
        $this->regionCode = $regionCode;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getRegionName(): ?string
    {
        return $this->regionName;
    }

    /**
     * @param string|null $regionName
     *
     * @return Address
     */
    public function setRegionName(?string $regionName): Address
    {
        $this->regionName = $regionName;
        return $this;
    }


    /**
     * @return null|string
     */
    public function getPostalCode(): ?string
    {
        return $this->postalCode;
    }

    /**
     * @param null|string $postalCode
     *
     * @return Address
     */
    public function setPostalCode(?string $postalCode): Address
    {
        $this->postalCode = $postalCode;
        return $this;
    }

    /**
     * @return string[]
     */
    public function getAdminLevels(): array
    {
        return $this->adminLevels;
    }

    /**
     * @param string[] $adminLevels
     *
     * @return Address
     */
    public function setAdminLevels(array $adminLevels): Address
    {
        $this->adminLevels = $adminLevels;
        return $this;
    }

    /**
     * @return null|string
     */
    public function getCountryName(): ?string
    {
        return $this->countryName;
    }

    /**
     * @param null|string $countryName
     *
     * @return Address
     */
    public function setCountryName(?string $countryName): Address
    {
        $this->countryName = $countryName;
        return $this;
    }

    /**
     * @return null|string
     */
    public function getCountryCode(): ?string
    {
        return $this->countryCode;
    }

    /**
     * @param null|string $countryCode
     *
     * @return Address
     */
    public function setCountryCode(?string $countryCode): Address
    {
        $this->countryCode = $countryCode;
        return $this;
    }

    /**
     * @return float
     */
    public function getLatitude(): float
    {
        return $this->latitude;
    }

    /**
     * @param float $latitude
     *
     * @return Address
     */
    public function setLatitude(float $latitude): Address
    {
        $this->latitude = $latitude;
        return $this;
    }

    /**
     * @return float
     */
    public function getLongitude(): float
    {
        return $this->longitude;
    }

    /**
     * @param float $longitude
     *
     * @return Address
     */
    public function setLongitude(float $longitude): Address
    {
        $this->longitude = $longitude;
        return $this;
    }

    /**
     * @return float[]|null
     */
    public function getBounds(): ?array
    {
        return $this->bounds;
    }

    /**
     * @param float[]|null $bounds
     *
     * @return Address
     */
    public function setBounds(?array $bounds): Address
    {
        $this->bounds = $bounds;
        return $this;
    }

    /**
     * @return null|Timezone
     */
    public function getTimezone(): ?string
    {
        return $this->timezone;
    }

    /**
     * @param Timezone|null $timezone
     *
     * @return Address
     */
    public function setTimezone(?Timezone $timezone): Address
    {
        $this->timezone = $timezone;
        return $this;
    }

    /**
     * @return string
     */
    public function getGeocodeProvider(): ?string
    {
        return $this->geocodeProvider;
    }

    /**
     * @param string $geocodeProvider
     *
     * @return Address
     */
    public function setGeocodeProvider(string $geocodeProvider): Address
    {
        $this->geocodeProvider = $geocodeProvider;
        return $this;
    }

    /**
     * @return \DateTimeInterface
     */
    public function getGeocodedAt(): \DateTimeInterface
    {
        return $this->geocodedAt;
    }

    /**
     * @param \DateTimeInterface $geocodedAt
     *
     * @return Address
     */
    public function setGeocodedAt(\DateTimeInterface $geocodedAt): Address
    {
        $this->geocodedAt = $geocodedAt;
        return $this;
    }

    /**
     * @return string
     */
    public function getRawString(): ?string
    {
        return $this->rawString;
    }

    /**
     * @param string $rawString
     *
     * @return Address
     */
    public function setRawString(string $rawString): Address
    {
        $this->rawString = $rawString;
        return $this;
    }

    /**
     * @return string
     */
    public function getFormattedString(): ?string
    {
        return $this->formattedString;
    }

    /**
     * @param string $formattedString
     *
     * @return Address
     */
    public function setFormattedString(string $formattedString): Address
    {
        $this->formattedString = $formattedString;
        return $this;
    }
}
