<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 11/13/2018
 * @time: 5:36 AM
 */

namespace App\Domain\Attribute\Entity;

use App\Traits\EntityTrait;
use App\Traits\Timestampable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\SoftDeleteable\Traits\SoftDeleteableEntity;
use <PERSON>MS\Serializer\Annotation as Serializer;
use JMS\Serializer\Annotation\Groups;

/**
 * Class Attribute
 * @package App\Domain\Attribute\Entity
 * @ORM\Entity(repositoryClass="App\Repository\AttributeRepository")
 * @ORM\Table(name="attributes.attributes")
 * @ORM\InheritanceType("JOINED")
 * @ORM\DiscriminatorColumn(name="_discr", type="string")
 */
class Attribute
{
    use EntityTrait;
    use Timestampable;
    use SoftDeleteableEntity;

    public const VALUE_TYPE_TEXT = 'text';
    public const VALUE_TYPE_NUMBER = 'number';
    public const VALUE_TYPE_DATE = 'date';
    public const VALUE_TYPE_DATETIME = 'datetime';

    public const VISIBILITY_PUBLIC = 'public';
    public const VISIBILITY_PROTECTED = 'protected';
    public const VISIBILITY_PRIVATE = 'private';

    /**
     * @var string
     * @ORM\Column(name="title", type="string", nullable=false)
     * @Groups({"rest","common","full","summary"})
     */
    protected $title;
    /**
     * @var string
     * @ORM\Column(name="description", type="text", nullable=false)
     * @Groups({"rest","common","full","summary"})
     */
    protected $description;
    /**
     * @var string
     * @ORM\Column(name="value_type", type="string", nullable=false)
     * @Groups({"rest","common","full","summary"})
     */
    protected $valueType;
    /**
     * @var bool
     * @ORM\Column(name="required", type="boolean", nullable=false)
     * @Groups({"rest","common","full","summary"})
     */
    protected $required;
    /**
     * @var bool
     * @ORM\Column(name="allow_multiple_values", type="boolean", nullable=false)
     * @Groups({"rest","common","full","summary"})
     */
    protected $allowMultipleValues;
    /**
     * @var string
     * @ORM\Column(name="visibility", type="string", nullable=false)
     * @Groups({"rest","common","full","summary"})
     */
    protected $visibility;
    /**
     * @var \App\Domain\Attribute\Entity\AttributeOption[]
     * @ORM\OneToMany(targetEntity="App\Domain\Attribute\Entity\AttributeOption", mappedBy="attribute")
     * @Groups({"rest","common","full","summary"})
     * @Serializer\Accessor(getter="getOptions")
     */
    protected $options;
    /**
     * @var string
     * @ORM\Column(name="category", nullable=false, options={"default":"general"})
     * @Groups({"rest","common","full","summary"})
     */
    protected $category;

    /**
     * Attribute constructor.
     *
     * @param string $title
     */
    public function __construct()
    {
        $this->options = new ArrayCollection();
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * @param string $title
     *
     * @return Attribute
     */
    public function setTitle(string $title): Attribute
    {
        $this->title = $title;
        return $this;
    }

    /**
     * @return string
     */
    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * @param string $description
     *
     * @return Attribute
     */
    public function setDescription(string $description): Attribute
    {
        $this->description = $description;
        return $this;
    }

    /**
     * @return string
     */
    public function getValueType(): string
    {
        return $this->valueType;
    }

    /**
     * @param string $valueType
     *
     * @return Attribute
     */
    public function setValueType(string $valueType): Attribute
    {
        $this->valueType = $valueType;
        return $this;
    }

    /**
     * @return bool
     */
    public function isRequired(): bool
    {
        return $this->required;
    }

    /**
     * @param bool $required
     *
     * @return Attribute
     */
    public function setRequired(bool $required): Attribute
    {
        $this->required = $required;
        return $this;
    }

    /**
     * @return bool
     */
    public function isAllowMultipleValues(): bool
    {
        return $this->allowMultipleValues;
    }

    /**
     * @param bool $allowMultipleValues
     *
     * @return Attribute
     */
    public function setAllowMultipleValues(bool $allowMultipleValues): Attribute
    {
        $this->allowMultipleValues = $allowMultipleValues;
        return $this;
    }

    /**
     * @return string
     */
    public function getVisibility(): string
    {
        return $this->visibility;
    }

    /**
     * @param string $visibility
     *
     * @return Attribute
     */
    public function setVisibility(string $visibility): Attribute
    {
        $this->visibility = $visibility;
        return $this;
    }

    /**
     * @return \App\Domain\Attribute\Entity\AttributeOption[]
     */
    public function getOptions()
    {
        if (is_array($this->options) || empty($this->options)) {
            $this->options = new ArrayCollection($this->options);
        }
        return $this->options;
    }

    /**
     * @param \App\Domain\Attribute\Entity\AttributeOption[] $options
     *
     * @return Attribute
     */
    public function setOptions($options): Attribute
    {
        if (is_array($options)) {
            $options = new ArrayCollection($options);
        }
        $this->options = $options;
        return $this;
    }

    /**
     * @return string
     */
    public function getCategory(): string
    {
        return $this->category;
    }

    /**
     * @param string $category
     *
     * @return Attribute
     */
    public function setCategory(string $category): Attribute
    {
        $this->category = $category;
        return $this;
    }
}
