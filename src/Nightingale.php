<?php
namespace <PERSON><PERSON>\Nightingale;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Clubster\Nightingale\Infrastructure\Node\NodeManager;
use Clubster\Nightingale\Infrastructure\Notifications\QueueProcessor;
use Clubster\Nightingale\Infrastructure\Queue\MongoQueueConsumer;
use Clubster\Nightingale\Infrastructure\Queue\QueueConsumerInterface;
use Clubster\Nightingale\Infrastructure\Statistics\DatabaseStatisticsManager;
use Clubster\Nightingale\Infrastructure\Statistics\Prometheus\CounterMetric;
use Clubster\Nightingale\Infrastructure\Statistics\PrometheusStatisticsManager;
use Clubster\Nightingale\Infrastructure\Statistics\SocketStatisticsManager;
use Clubster\Nightingale\Infrastructure\Statistics\TimeSeriesStatisticsManager;
use Psr\Log\LoggerInterface;
use React\EventLoop\Loop;
use React\Http\Browser;
use React\Promise\Promise;
use React\Promise\PromiseInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\DependencyInjection\Attribute\Exclude;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;
use Symfony\Component\DependencyInjection\ContainerInterface;
use function React\Promise\map;
use function React\Promise\resolve;
use function React\Promise\Stream\all;

#[Exclude]
class Nightingale implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    private int $iteration = 1;
    private int $processedLast = 0;
    private int $processedTotal = 0;
    private array $processTimes = [];
    private static Nightingale $instance;

    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly SocketStatisticsManager $socketStatisticsManager,
        #[Autowire(param: 'nightingale.active_notification_processors')]
        private readonly array $activeProcessors,
        private readonly Carbon $startTime
    )
    {}

    public function boot(): Nightingale
    {
        echo "
  _   _ _       _     _   _                   _       
 | \ | (_)     | |   | | (_)                 | |      
 |  \| |_  __ _| |__ | |_ _ _ __   __ _  __ _| | ___  
 | . ` | |/ _` | '_ \| __| | '_ \ / _` |/ _` | |/ _ \ 
 | |\  | | (_| | | | | |_| | | | | (_| | (_| | |  __/ 
 |_| \_|_|\__, |_| |_|\__|_|_| |_|\__, |\__,_|_|\___| 
           __/ |                   __/ |              
          |___/                   |___/               ⠀                              

Queue Id: ".$this->container->getParameter('nightingale.queue_name')."

";
        if (!empty($_ENV['ECS_CONTAINER_METADATA_URI_V4'])) {
            $httpClient = new Browser();
            $httpClient->get($_ENV['ECS_CONTAINER_METADATA_URI_V4'].'/task', [])
                ->then(function ($response) {
                    $response = (string)$response->getBody()->getContents();
                    $this->logger->info($response);
                    return $response;
                }, function ($error) {
                    $this->logger->error(sprintf('[%-28s] %s','AWS Metadata','Failed to get service metadata: ' . $error->getMessage()));
                    return [];
                });
        }

        Loop::addSignal(SIGINT, function () {
            $this->shutdown();
            exit;
        });
        Loop::addSignal(SIGTERM, function () {
            $this->shutdown();
            exit;
        });

        return $this;
    }

    function runQueue(): PromiseInterface
    {
        $this->logger->info(sprintf('loop %s started', $this->iteration));
        $stime = microtime(true);
        $this->container->get(NodeManager::class)->register();
        return $this->container->get(MongoQueueConsumer::class)->readQueue()
            ->then(function($results) {
                $this->socketStatisticsManager->publishMessage([
                    'queue'=>[
                        'status'=>'processing'
                    ]
                ],'admin:notification_queue_statistics');
                return $results;
            })
            ->then([$this->container->get(QueueProcessor::class), 'process'])
            ->then(function($results) {
                return $this->container->get(DatabaseStatisticsManager::class)->writeStats($results);
            })
            ->then(function($results) {
                $result = $this->container->get(TimeSeriesStatisticsManager::class)->writeStats($results);
                return $results;
            })
            ->then(function($results) {
                return $this->container->get(PrometheusStatisticsManager::class)->writeStats($results);
            })
            ->then(function ($results) use ($stime) {
                if (empty($results)) {
                    $results = [];
                }
                $this->processedLast = count($results);
                if (empty($results)) {
                    $this->logger->info(sprintf('loop %s complete, no notifications sent', $this->iteration));
                    return resolve();
                }
                $this->lastProcessTime = Carbon::now();
                $this->processedTotal += $this->processedLast;
                $this->processTimes[] = (microtime(true) - $stime)+$_ENV['INTERVAL_SLEEP_TIME'];
                $this->logger->info(sprintf('loop %s complete, %s notifications sent', $this->iteration, $this->processedTotal));
                return $results;

            })
            ->then(function($results) {
                return $this->socketStatisticsManager->writeStats($results);
            })->otherwise(function ($e) {
                $this->logger->error($e->getMessage());
            })->always(function () use ($stime) {
                $sleep = $_ENV['INTERVAL_SLEEP_TIME'];
                if ($this->processedLast == 0) {
                    $sleep = $_ENV['EMPTY_QUEUE_SLEEP_TIME'];

                    $this->logger->info(sprintf('Ran for %s seconds. Sleeping for %s second', (microtime(true) - $stime), $sleep));
                    return resolve(
                        $this->socketStatisticsManager->publishMessage([
                            'queue' => [
                                'status' => 'sleeping'
                            ]
                        ], 'admin:notification_queue_statistics')
                    )
                    ->then(function() use ($sleep) {
                        sleep($sleep);
                    })->then(function() {
                        $this->iteration++;
                        return resolve($this->runQueue());
                    });
                }else{
                    return resolve()
                        ->then(function() use ($sleep) {
                            sleep($sleep);
                        })->then(function() {
                            $this->iteration++;
                            return resolve($this->runQueue());
                        });
                }

            });
    }

    public static function getInstance(?ContainerInterface $container=null): Nightingale
    {
        if (!isset(self::$instance)) {
            self::$instance = $container->get(Nightingale::class);
            self::$instance->setContainer($container);
            self::$instance->boot();
        }
        return self::$instance;
    }

    public static function getIteration():int
    {
        return self::getInstance()->iteration;
    }

    public static function getProcessedLast():int
    {
        return self::getInstance()->processedLast;
    }

    public static function getProcessedTotal():int
    {
        return self::getInstance()->processedTotal;
    }

    public static function getProcessTimes():array
    {
        return self::getInstance()->processTimes;
    }

    public static function getLastProcessTime():float
    {
        if (self::getProcessedLast() == 0) {
            return 0;
        }
        return end(self::getInstance()->processTimes);
    }

    public static function getAverageProcessTime():float
    {
        if (empty(self::getInstance()->processTimes)) {
            return 0;
        }
        return array_sum(self::getInstance()->processTimes)/count(self::getInstance()->processTimes);
    }

    public static function getAge():string
    {
        return self::getInstance()->startTime->diffForHumans([
            'parts'=>6,
            'join'=>' ',
            'short'=>true
        ], CarbonInterface::DIFF_ABSOLUTE);
    }

    public static function getContainer(): ContainerInterface
    {
        return self::getInstance()->container;
    }

    public function shutdown()
    {
        $this->logger->info('Nightingale shutting down');
        $this->container->get(NodeManager::class)->deregister();
    }
}
