<?php
declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Listener;

use Exception;
use Mockingbird\Infrastructure\DependencyInjection\ContainerAwareInterface;
use <PERSON>ckingbird\Infrastructure\DependencyInjection\ContainerAwareTrait;
use <PERSON><PERSON><PERSON>\Infrastructure\Listener\Listener;
use <PERSON>ckingbird\Infrastructure\Listener\ListenerInterface;
use Mockingbird\Infrastructure\Logger\Logger;
use Mockingbird\Infrastructure\Task\Task;
use Mockingbird\Listener\Messenger\Connection\ConnectionManager;
use Mockingbird\Listener\Messenger\Request;
use Mockingbird\Mockingbird;
use Mockingbird\Task\Messaging\Channel\LeaveChannel;
use Mockingbird\Task\Messaging\RequestIdent;
use Ratchet\ConnectionInterface;
use Ratchet\Http\HttpServer;
use Ratchet\MessageComponentInterface;
use Ratchet\Server\IoServer;
use Ratchet\WebSocket\WsConnection;
use Ratchet\WebSocket\WsServer;
use React\EventLoop\Loop;
use React\Socket\SocketServer;
use VertigoLabs\DataAware\Exceptions\DataNotFoundNoDefaultException;

class Messenger extends Listener implements MessageComponentInterface, ContainerAwareInterface
{
    use ContainerAwareTrait;

    private int $handledRequestCount = 0;

    /**
     * @throws Exception
     */
    public function initialize(): ListenerInterface
    {
        try {
            $this->uri = $this->configuration->getData('uri');
        }catch (DataNotFoundNoDefaultException $e) {
            throw new \Exception('uri is a required configuration value for Messenger');
        }

        $this->loop = Loop::get();
        $this->socket = new SocketServer($this->uri, [], $this->loop);
        $wsServer = new WsServer($this);
        $this->server = new IoServer(new HttpServer($wsServer), $this->socket, $this->loop);
        $wsServer->enableKeepAlive($this->loop, 30);

//        Cron::create(new Cron\Action('CleanConnections', 30, '*/5 * * * *', function(): PromiseInterface {
////            $this->cleanConnections();
//            $this->container->get(CleanChannels::class)();
//            return resolve(true);
//        }));

        return $this;
    }

    public function run(): void
    {
        $this->loop->run();
    }

    public function onOpen(ConnectionInterface $conn): void
    {
        if ($conn instanceof WsConnection) {
            ConnectionManager::CreateConnection($conn);
            $request = new Request($conn->resourceId);
            Mockingbird::Container()->get(RequestIdent::class)->setRequest($request)();
        }
    }

    public function onMessage(ConnectionInterface $from, $msg): void
    {
        try {
            $this->handledRequestCount++;
            $input = json_decode($msg, true, 512, JSON_THROW_ON_ERROR);
            $request = new Request($from->resourceId);
            $request->setData($input);
            $taskName = $request->determineTask();

            if ($taskName !== 'Messaging:Ident' && !$request->getConnection()->hasIdent()) {
                Mockingbird::Container()->get(RequestIdent::class)->setRequest($request)();
                return;
            }
            $payload = $request->determinePayload();

            /** @var Task $task */
            $task = Mockingbird::Container()->get($taskName)
                                            ->setData($payload)
                                            ->setRequest($request)();
        }catch (Exception $exception) {
            echo $exception->getMessage().PHP_EOL;
        }
    }

    public function onClose(ConnectionInterface $conn): void
    {
        if ($conn instanceof WsConnection) {
            // unjoin all channels
            $request = new Request($conn->resourceId);
            Mockingbird::Container()->get(LeaveChannel::class)->setRequest($request)();
            ConnectionManager::CloseConnection($conn);
            $conn->close();
        }
    }

    function onError(ConnectionInterface $conn, Exception $e): void
    {
        $this->log($e->getMessage(), Logger::ERROR);
        $this->onClose($conn);
    }

    public function getHandledRequestCount(): int
    {
        return $this->handledRequestCount;
    }
}
