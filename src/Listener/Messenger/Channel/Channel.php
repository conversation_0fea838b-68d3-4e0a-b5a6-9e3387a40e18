<?php

namespace Mockingbird\Listener\Messenger\Channel;

use <PERSON><PERSON><PERSON>\Listener\Messenger\Connection\Connection;
use React\EventLoop\Loop;
use VertigoLabs\DataAware\DataAware;
use VertigoLabs\DataAware\DataAwareInterface;
use function React\Promise\map;

class Channel implements DataAwareInterface
{
    use DataAware;

    private string $id;
    private array $connections = [];
    private array $deferredActions = [];

    public function __construct(string $id)
    {
        $this->id = $id;
    }

    public function broadcast(array $payload, ?Connection $sender = null, ?bool $includeSender = true): Channel
    {
        $payload['channel'] = $this->getId();
        if (null !== $sender) {
            $payload['sender'] = $sender->getId();
        }
        $jsonPayload = json_encode($payload, JSON_THROW_ON_ERROR);

        $promise = map($this->connections, function (Connection $connection) use ($jsonPayload, $sender, $includeSender) {
            if ($includeSender === false && $sender->getId() === $connection->getId()) {
                return;
            }
            $connection->send($jsonPayload);
        });

        return $this;
    }

    public function addConnection(Connection $connection): Channel
    {
        $this->connections[$connection->getId()] = $connection;
        return $this;
    }

    public function removeConnection(Connection $connection): Channel
    {
        unset($this->connections[$connection->getId()]);
        return $this;
    }

    /**
     * @return string
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     * @return array
     */
    public function getConnections(): array
    {
        return $this->connections;
    }

    public function addDeferredAction($id, $timeout, $callback): Channel
    {
        $this->deferredActions[$id] = Loop::addTimer($timeout, function() use($id, $callback) {
            $callback();
            $this->removeDeferredAction($id);
        });
        return $this;
    }

    public function hasDeferredAction($id): bool
    {
        return array_key_exists($id, $this->deferredActions);
    }

    public function getDeferredAction($id)
    {
        if ($this->hasDeferredAction($id)) {
            return $this->deferredActions[$id];
        }
    }

    public function removeDeferredAction($id): Channel
    {
        $timer = $this->getDeferredAction($id);
        if (null !== $timer) {
            Loop::cancelTimer($timer);
            unset($this->deferredActions[$id]);
        }

        return $this;
    }
}
