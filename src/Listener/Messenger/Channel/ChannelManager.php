<?php

namespace Mocking<PERSON>\Listener\Messenger\Channel;

class ChannelManager
{
    private static array $channels = [];

    public static function CreateChannel($id): Channel
    {
        if (empty(self::$channels[$id])) {
            $channel = new Channel($id);
            self::$channels[$id] = $channel;
        }
        return self::$channels[$id];
    }

    public static function GetChannelById($id): Channel
    {
        return self::CreateChannel($id);
    }
}
