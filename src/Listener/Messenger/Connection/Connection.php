<?php

namespace <PERSON>cking<PERSON>\Listener\Messenger\Connection;

use <PERSON><PERSON><PERSON>\Listener\Messenger\Channel\Channel;
use Ramsey\Uuid\Uuid;
use Ratchet\ConnectionInterface;

class Connection
{
    private array $channels = [];
    private string $id;
    private string $userId;
    private ConnectionInterface $wsConnection;

    /**
     * @param $id
     * @param $wsConnection
     * @param $userId
     */
    public function __construct(ConnectionInterface $wsConnection)
    {
        $this->id = (string)Uuid::uuid4();
        $this->wsConnection = $wsConnection;
    }

    public function send(string $data)
    {
        $this->wsConnection->send($data);
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function setUserId(string $userId): void
    {
        $this->userId = $userId;
    }

    public function getUserId(): string
    {
        return $this->userId;
    }

    public function getResourceId(): string
    {
        return $this->wsConnection->resourceId;
    }

    public function hasIdent()
    {
        return !empty($this->userId);
    }

    public function addChannel(Channel $channel)
    {
        $this->channels[$channel->getId()] = $channel;
    }

    public function removeChannel(Channel $channel)
    {
        unset($this->channels[$channel->getId()]);
    }

    /**
     * @return array
     */
    public function getChannels(): array
    {
        return $this->channels;
    }
}
