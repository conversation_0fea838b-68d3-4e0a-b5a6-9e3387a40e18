<?php

namespace Mo<PERSON><PERSON>\Listener\Messenger;

use Mocking<PERSON>\Infrastructure\Request\Request as BaseRequest;
use <PERSON><PERSON><PERSON>\Listener\Messenger\Connection\Connection;
use <PERSON><PERSON><PERSON>\Listener\Messenger\Connection\ConnectionManager;

class Request extends BaseRequest
{
    private Connection $connection;

    /**
     * @throws Exception\ConnectionNotFoundException
     */
    public function __construct(int $connectionResourceId)
    {
        $this->connection = ConnectionManager::LocateConnectionByResource($connectionResourceId);
    }

    /**
     * @return Connection
     */
    public function getConnection(): Connection
    {
        return $this->connection;
    }
}
