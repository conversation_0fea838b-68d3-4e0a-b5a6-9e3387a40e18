<?php

namespace Mockingbird\Listener\Http;

use Mockingbird\Infrastructure\Request\Request as BaseRequest;
use Psr\Http\Message\ServerRequestInterface;

class Request extends BaseRequest
{

    private ServerRequestInterface $serverRequest;

    public function __construct(ServerRequestInterface $serverRequest)
    {
        $this->serverRequest = $serverRequest;
    }

    public function determinePayload(): array
    {
        $payload = [];
        $query = $this->serverRequest->getQueryParams();
        $payload = $query;

        $parsedBody = $this->serverRequest->getParsedBody();
        if (!empty($parsedBody)) {
            if (is_string($parsedBody)) {
                $body = json_decode(trim($parsedBody), true, 512, JSON_THROW_ON_ERROR);
            }

            if (is_array($parsedBody)) {
                $payload = array_merge($payload, $parsedBody);
            }
        }

        $body = $this->serverRequest->getBody()->getContents();
        if (!empty($body)) {
            if (is_string($body)) {
                $body = json_decode(trim($body), true, 512, JSON_THROW_ON_ERROR);
            }

            if (is_array($body)) {
                $payload = array_merge($payload, $body);
            }
        }

        return $payload;
    }

    public function determineTask(): string
    {
        $uriPath = $this->serverRequest->getUri()->getPath();
        if (str_starts_with($uriPath, '/')) {
            $uriPath = substr($uriPath, 1);
        }

        if (empty($uriPath)) {
            // check input
        }

        return $uriPath;
    }

}
