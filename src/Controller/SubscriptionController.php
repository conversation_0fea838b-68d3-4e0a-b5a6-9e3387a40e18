<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 10/12/2018
 * @time: 2:56 PM
 */

namespace App\Controller;

use App\Domain\Organization\Entity\Organization;
use App\Domain\Subscription\Entity\Subscription;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class SubscriptionController
 * @package App\Controller
 * @Route("/subscription", name="subscription")
 */
class SubscriptionController extends BaseController
{
    public function index(){}

    /**
     * @param $feed
     * @Route("/create/{feed}", name=":create")
     *
     * @return JsonResponse
     */
    public function subscribe($feed)
    {
        $organizationRepo = $this->getDoctrine()->getRepository(Organization::class);
        $subscription = $organizationRepo->submitRPC('App\\Domain\\Organization\\Command\\Members\\CreateOrganizationMember', [
            'organization'=>$feed,
            'user'=>$this->getUser()->getId(),
        ]);
        return $this->redirectBack();
    }

    /**
     * @param $feed
     * @Route("/remove/{feed}", name=":remove")
     *
     * @return JsonResponse
     */
    public function unsubscribe($feed)
    {
        $organizationRepo = $this->getDoctrine()->getRepository(Organization::class);
        $subscription = $organizationRepo->submitRPC('App\\Domain\\Organization\\Command\\Members\\RemoveOrganizationMember', [
            'organization'=>$feed,
            'user'=>$this->getUser()->getId(),
        ]);
        return $this->redirectBack();
    }
}
