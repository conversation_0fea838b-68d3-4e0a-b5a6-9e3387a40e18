<?php

namespace App\Controller\User\Account;

use App\Controller\BaseController;
use App\Domain\User\Entity\User;
use App\Form\User\ProfileType;
use App\Form\User\UserProfileType;
use App\Infrastructure\Command\RPCCommand;
use App\Infrastructure\Falcon\FalconRequest;
use App\Security\CardinalTokenAuthenticator;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Event\InteractiveLoginEvent;

/**
 * Class ProfileController
 * @package App\Controller\User\Account
 * @Route("/user/account/profile", name="user:account:profile")
 */
class ProfileController extends BaseController
{
    private CardinalTokenAuthenticator $cardinalTokenAuthenticator;
    private EventDispatcherInterface $eventDispatcher;

    public function __construct(RequestStack $requestStack, CardinalTokenAuthenticator $cardinalTokenAuthenticator, EventDispatcherInterface $eventDispatcher)
    {
        parent::__construct($requestStack);
        $this->cardinalTokenAuthenticator = $cardinalTokenAuthenticator;
        $this->eventDispatcher = $eventDispatcher;
    }

    /**
     * @param Request $request
     * @Route("/edit", name=":edit")
     *
     * @return Response
     */
    public function edit(Request $request)
    {
        $attributes = FalconRequest::execute('Attributes:LoadAttributes', [
            'entityType'=>\App\Domain\User\Entity\User::class,
            'entityId' => $this->getUser()->getId()
        ]);

        $attributeValues = [
            'firstName' => $this->getUser()->getFirstName(),
            'lastName' => $this->getUser()->getLastName(),
            'email' => $this->getUser()->getEmail(),
        ];
        foreach ($attributes as $attribute) {
            $attributeValues[$attribute['slug']] = $attribute['value']['value']??'';
        }

        $profileForm = $this->createForm(ProfileType::class, $attributeValues, [
            'attributes'=>$attributes
        ]);

        $profileForm->handleRequest($request);

        if ($profileForm->isSubmitted() && $profileForm->isValid()) {
            $data = $profileForm->getData();
            $attributes = $data;
            unset($attributes['firstName'],$attributes['lastName'],$attributes['email'], $attributes['avatar'], $attributes['avatarCrop'], $attributes['cover'], $attributes['coverCrop']);

            $userUpdate = FalconRequest::execute('User:UpdateUser', [
                'userId' => $this->getUser()->getId(),
                'firstName'=>$data['firstName'],
                'lastName'=>$data['lastName'],
                'email'=>$data['email'],
            ]);

            if (FalconRequest::hasErrors()) {
                $error = FalconRequest::getErrors();
                if($error['error'] === 'Account With Email Already Exists') {
                    $this->addFlash('errors','There Was An Error Changing This Email.');
                } else {
                    $this->addFlash('errors','An Unknown Error Has Occurred.');
                }
            } else {
                FalconRequest::execute('User:UpdateAvatar', [
                    'userId' => $this->getUser()->getId(),
                    'avatar' => $data['avatar'],
                    'avatarCrop' => $data['avatarCrop'],
                    'avatarRemoved' => $data['avatarRemoved'],
                ]);
                FalconRequest::execute('User:UpdateCover', [
                    'userId' => $this->getUser()->getId(),
                    'cover' => $data['cover'],
                    'coverCrop' => $data['coverCrop'],
                    'coverRemoved' => $data['coverRemoved'],
                ]);
                FalconRequest::execute('Attributes:UpdateAttributeValues', [
                    'entityType' => \App\Domain\User\Entity\User::class,
                    'entityId' => $this->getUser()->getId(),
                    'attributes' => $attributes,
                    'removeEmpty' => true
                ]);
                $session = $this->container->get('session');
                $session->set('refresh_profile', true);
                $tokenManager = $this->get('security.token_storage');
                $tokenManager->setToken(null);
                $token = $this->cardinalTokenAuthenticator->forceCreateToken($request, $data['email'], 'common');
                $this->container->get('security.token_storage')->setToken($token);
                $this->container->get('session')->set('_security_main', serialize($token));
                $event = new InteractiveLoginEvent($request, $token);
                $this->eventDispatcher->dispatch($event, 'security.interactive_login');
                $this->addFlash('messages', 'Profile Updated Successfully.');
            }
        }
        return $this->render('user/account/edit-profile.html.twig', [
            //'falconErrors'=>$errors??[],
            'profileForm'=>$profileForm->createView(),
            'breadcrumbs'=> [
                'My Profile'=>$request->getUri(),
            ]
        ]);
    }
}
