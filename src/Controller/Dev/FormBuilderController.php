<?php

namespace App\Controller\Dev;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Form Builder Controller for development environment
 */
#[Route('/dev', name: 'dev_')]
class FormBuilderController extends AbstractController
{
    #[Route('/builder', name: 'form_builder')]
    public function builder(): Response
    {
        return $this->render('dev/builder.html.twig', [
            'page_title' => 'Form Builder',
            'page_description' => 'Create dynamic forms with drag and drop functionality'
        ]);
    }
}
