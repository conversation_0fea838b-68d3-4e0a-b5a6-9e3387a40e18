<?php

namespace App\Controller\Dev;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * API Controller for Form Builder operations
 * This is a simple implementation for demonstration purposes
 */
#[Route('/dev/api/forms', name: 'dev_form_builder_api_')]
class FormBuilderApiController extends AbstractController
{
    private const FORMS_STORAGE_FILE = 'var/form_builder_forms.json';

    #[Route('', name: 'list', methods: ['GET'])]
    public function listForms(): JsonResponse
    {
        $forms = $this->loadFormsFromStorage();
        
        return new JsonResponse($forms);
    }

    #[Route('/{id}', name: 'get', methods: ['GET'])]
    public function getForm(string $id): JsonResponse
    {
        $forms = $this->loadFormsFromStorage();
        
        foreach ($forms as $form) {
            if ($form['id'] === $id) {
                return new JsonResponse($form);
            }
        }
        
        return new JsonResponse(['error' => 'Form not found'], Response::HTTP_NOT_FOUND);
    }

    #[Route('', name: 'create', methods: ['POST'])]
    public function createForm(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        
        if (!$data) {
            return new JsonResponse(['error' => 'Invalid JSON'], Response::HTTP_BAD_REQUEST);
        }
        
        // Validate required fields
        if (!isset($data['name']) || empty($data['name'])) {
            return new JsonResponse(['error' => 'Form name is required'], Response::HTTP_BAD_REQUEST);
        }
        
        // Generate ID if not provided
        if (!isset($data['id']) || empty($data['id'])) {
            $data['id'] = 'form_' . uniqid();
        }
        
        // Set metadata
        $data['_meta'] = array_merge($data['_meta'] ?? [], [
            'created_at' => date('c'),
            'created_by' => $this->getUser()?->getId() ?? 'anonymous',
            'updated_at' => date('c'),
            'updated_by' => $this->getUser()?->getId() ?? 'anonymous',
        ]);
        
        // Generate ordinals for elements
        if (isset($data['elements']) && is_array($data['elements'])) {
            foreach ($data['elements'] as $index => &$element) {
                $element['ordinal'] = $index;
                
                // Generate ordinals for section fields
                if (isset($element['fields']) && is_array($element['fields'])) {
                    foreach ($element['fields'] as $fieldIndex => &$field) {
                        $field['ordinal'] = $fieldIndex;
                    }
                }
                
                // Generate ordinals for field options
                if (isset($element['options']) && is_array($element['options'])) {
                    foreach ($element['options'] as $optionIndex => &$option) {
                        $option['ordinal'] = $optionIndex;
                    }
                }
            }
        }
        
        $forms = $this->loadFormsFromStorage();
        
        // Update existing form or add new one
        $updated = false;
        foreach ($forms as $index => $existingForm) {
            if ($existingForm['id'] === $data['id']) {
                $forms[$index] = $data;
                $updated = true;
                break;
            }
        }
        
        if (!$updated) {
            $forms[] = $data;
        }
        
        $this->saveFormsToStorage($forms);
        
        return new JsonResponse($data, $updated ? Response::HTTP_OK : Response::HTTP_CREATED);
    }

    #[Route('/{id}', name: 'update', methods: ['PUT'])]
    public function updateForm(string $id, Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        
        if (!$data) {
            return new JsonResponse(['error' => 'Invalid JSON'], Response::HTTP_BAD_REQUEST);
        }
        
        $forms = $this->loadFormsFromStorage();
        
        foreach ($forms as $index => $existingForm) {
            if ($existingForm['id'] === $id) {
                $data['id'] = $id;
                $data['_meta'] = array_merge($existingForm['_meta'] ?? [], [
                    'updated_at' => date('c'),
                    'updated_by' => $this->getUser()?->getId() ?? 'anonymous',
                ]);
                
                $forms[$index] = $data;
                $this->saveFormsToStorage($forms);
                
                return new JsonResponse($data);
            }
        }
        
        return new JsonResponse(['error' => 'Form not found'], Response::HTTP_NOT_FOUND);
    }

    #[Route('/{id}', name: 'delete', methods: ['DELETE'])]
    public function deleteForm(string $id): JsonResponse
    {
        $forms = $this->loadFormsFromStorage();
        
        foreach ($forms as $index => $existingForm) {
            if ($existingForm['id'] === $id) {
                unset($forms[$index]);
                $forms = array_values($forms); // Re-index array
                $this->saveFormsToStorage($forms);
                
                return new JsonResponse(['message' => 'Form deleted successfully']);
            }
        }
        
        return new JsonResponse(['error' => 'Form not found'], Response::HTTP_NOT_FOUND);
    }

    private function loadFormsFromStorage(): array
    {
        $filePath = $this->getParameter('kernel.project_dir') . '/' . self::FORMS_STORAGE_FILE;
        
        if (!file_exists($filePath)) {
            return [];
        }
        
        $content = file_get_contents($filePath);
        $forms = json_decode($content, true);
        
        return is_array($forms) ? $forms : [];
    }

    private function saveFormsToStorage(array $forms): void
    {
        $filePath = $this->getParameter('kernel.project_dir') . '/' . self::FORMS_STORAGE_FILE;
        $directory = dirname($filePath);
        
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
        
        file_put_contents($filePath, json_encode($forms, JSON_PRETTY_PRINT));
    }
}
