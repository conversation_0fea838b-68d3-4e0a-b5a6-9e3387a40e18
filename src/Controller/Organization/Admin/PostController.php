<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 12/17/2018
 * @time: 9:44 AM
 */

namespace App\Controller\Organization\Admin;


use App\Controller\BaseController;
use App\Controller\Organization\BaseOrganizationController;
use App\Domain\Organization\Entity\Organization;
use App\Infrastructure\Command\RPCCommand;
use App\Infrastructure\Falcon\FalconRequest;
use Carbon\Carbon;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class BillingController
 * @package App\Controller\Organization\Admin
 * @Route("/organization/{organization}/admin/posts", name="organization:admin:posts")
 */
class PostController extends BaseOrganizationController
{
    /**
     * @param Request $request
     * @Route("/", name=":index")
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \Exception
     */
    public function index(Request $request)
    {
        if (!(!$this->organization->getMeta()['isUserContact'] && $this->organization->getMeta()['isUserMember'] && $this->organization->getSetting('allow-members-to-post')) &&
            !$this->isOrgGranted('PERMISSION:ORG:ADMINISTRATION') &&
            !$this->isOrgGranted('PERMISSION:ORG:POST:EDIT:ALL') &&
            !$this->isOrgGranted('PERMISSION:ORG:POST:CREATE') && 
            !$this->isGranted('ROLE_SUPER_ADMIN', $this->getUser())) {
            $this->addFlash('errors', 'Permission Denied');
            return $this->redirectToRoute('organization:index', ['organization' => $this->organization->getSlug()]);
        }

        $start = (new Carbon())->startOfMonth();
        if ($request->query->has('start')) {
            $start = (new Carbon($request->query->get('start')))->startOfMonth();
        }
        $end = (Carbon::instance($start))->endOfMonth();

        $isUserMember = (!$this->organization->getMeta()['isUserContact'] && $this->organization->getMeta()['isUserMember']);

        if($isUserMember) {
            $bc = [
                $this->organization->getName()=>$this->generateUrl('organization:index', ['organization'=>$this->organization->getSlug()]),
                'Manage Your '.ucfirst($this->organization->orgContext).' Posts'=>$request->getUri(),
            ];
        } else {
            $bc = [
                $this->organization->getName()=>$this->generateUrl('organization:index', ['organization'=>$this->organization->getSlug()]),
                'Administration'=>$this->generateUrl('organization:admin:index', ['organization'=>$this->organization->getSlug()]),
                'Manage '.ucfirst($this->organization->orgContext).' Posts'=>$request->getUri(),
            ];
        }

        $author = null;
        if(!$this->isOrgGranted('PERMISSION:ORG:POST:EDIT:ALL')) {
            $author = $this->getUser()->getId();
        }
        if($this->isGranted('ROLE_SUPER_ADMIN', $this->getUser())) {
            $author = null;
        }
        return $this->render('organization/admin/posts/index.html.twig',[
            'start'=>$start,
            'end'=>$end,
            'author'=>$author,
            'isUserMember'=>$isUserMember,
            'breadcrumbs'=> $bc
        ]);
    }

    private function getAuthorTitle($post) 
    {
        $title = '';
        if(!empty($post['created_by']['contact']['id'])) {
            $title = (empty($post['created_by']['contact']['title']) ? 'Staff' : $post['created_by']['contact']['title']);
        } else {
            if(!empty($post['created_by']['member']['id'])) {
                $title = 'Member';
            }
        }
        return $title;
    }

    /**
     * @Route("/load", name=":load")
     * @param Request $request
     * @return Response
     */
    public function loadPosts(Request $request)
    {
        $start = $request->get('start', null);
        if (!empty($start)) {
            $start = (new Carbon($start))->startOfDay()->toDateTimeString();
        }

        $end = $request->get('end', null);
        if (!empty($end)) {
            $end = (new Carbon($end))->endOfDay()->toDateTimeString();
        }

        $query = $request->get('query', null);
        $author = $request->get('author', null);

        $outputStructure = [
            'id'=>'',
            'status'=>'',
            'url'=>'',
            'subject'=>'',
            'is_published'=>'',
            'is_media_clear'=>'',
            'created_by.name'=>'',
            'created_by.first_name'=>'',
            'created_by.last_name'=>'',
            'created_by.email'=>'',
            'created_by.contact.id'=>'',
            'created_by.contact.title'=>'',
            'created_by.member.id'=>'',
            'created_by.member.status'=>'',
            'created_by.member.suspended'=>'',
            'created_at'=>'',
            'created_at_tz'=>'',
            'publish_at'=>'',
            'publish_at_tz'=>'',
            'timezone'=>'',
            'send_notifications'=>'',
            'notifications_sent'=>'',
            //'event'=>'',
            'event.subject'=>'',
            'organization.slug'=>'',
            //''=>'',
        ];

        $posts = FalconRequest::execute('Post:LoadFeed', [
            'targetAudience'=>$this->organization->getId(),
            'publishedOnly'=>'false',
            'includeDeleted'=>true,
            'start' => $start??null,
            'end' => $end??null,
            'query' => $query??null,
            'author' => $author??null,
            'userId'=>$this->getUser()->getId(),
            'offset'=>0,
            'limit'=>0
        ], $outputStructure);

        switch($request->get('sort','start_at')) {
            case 'subject':
                uasort($posts, fn($a, $b) => strtolower(trim($a['subject'])) <=> strtolower(trim($b['subject'])));
                break;
            case 'author':
                uasort($posts, fn($a, $b) => strtolower($a['created_by']['last_name']) <=> strtolower($b['created_by']['last_name']));
                break;
            case 'publish_at':
                uasort($posts, fn($a, $b) => $a['publish_at'] <=> $b['publish_at']);
                break;
            case 'created_at':
                uasort($posts, fn($a, $b) => $a['created_at'] <=> $b['created_at']);
                break;
            case 'status':
                uasort($posts, fn($a, $b) => $a['status'] <=> $b['status']);
                break;
        }

        if ($request->get('direction','asc') == 'desc') {
            $posts = array_reverse($posts);
        }
        $posts = array_values($posts);

        $posts = array_map(function($post){
            $post['url'] = $this->generateUrl('organization:post:index',['organization'=>$post['organization']['slug'], 'post'=>$post['id']]);
            $post['created_by']['title'] = $this->getAuthorTitle($post);
            if($post['status'] === 'pending') {
                if(!empty($post['publish_at']??null) && 
                   (!$post['is_published']??false && $post['is_media_clear']??false) &&
                   (new Carbon($post['publish_at']))->gte(Carbon::now()->subMinutes(2))) {
                    $post['status'] = 'scheduled';
                }
            }
            return $post;
        },$posts);

        return new JsonResponse($posts);
    }
}
