<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 4/11/2018
 * @time: 4:23 PM
 */

namespace App\Types;


use Carbon\Carbon;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\DateTimeType;

class CarbonDateTimeType extends DateTimeType
{
//  public const CARBONDATETIME = 'carbondatetime';

//  public function getName():string
//  {
//    return static::CARBONDATETIME;
//  }

  /**
   * @param $value
   * @param \Doctrine\DBAL\Platforms\AbstractPlatform $platform
   *
   * @return bool|\DateTime|false|mixed
   * @throws \Doctrine\DBAL\Types\ConversionException
   */
  public function convertToPHPValue($value, AbstractPlatform $platform)
  {
    $result = parent::convertToPHPValue($value, $platform);
    if ($result instanceof \DateTime) {
      return Carbon::instance($result);
    }
    return $result;
  }
//
//  /**
//   * @param \Doctrine\DBAL\Platforms\AbstractPlatform $platform
//   *
//   * @return bool
//   */
//  public function requiresSQLCommentHint(AbstractPlatform $platform):bool
//  {
//    return true;
//  }
}
