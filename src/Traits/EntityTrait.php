<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 7/15/2018
 * @time: 7:54 AM
 */

namespace App\Traits;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation\Groups;

trait EntityTrait
{
    /**
     * @var string
     * @ORM\Id()
     * @ORM\Column(type="guid")
     * @ORM\GeneratedValue(strategy="CUSTOM")
     * @ORM\CustomIdGenerator(class="App\Infrastructure\UuidGenerator")
     * @Assert\Uuid
     * @Groups({"minimal","rest","common","full"})
     */
    private $id;

    /**
     * @return string
     * @Groups({"rest","common"})
     */
    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }
}
