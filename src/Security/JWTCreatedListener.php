<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 7/24/2018
 * @time: 5:05 PM
 */

namespace App\Security;


use App\Domain\Group\Entity\PersonalGroup;
use App\Domain\Organization\Entity\Organization;
use App\Domain\Organization\Entity\OrganizationContact;
use App\Domain\User\Command\LoadUserContactClubs;
use Doctrine\Common\Persistence\ManagerRegistry;
use JMS\Serializer\SerializationContext;
use J<PERSON>\Serializer\SerializerInterface;
use Lexik\Bundle\JWTAuthenticationBundle\Event\JWTCreatedEvent;
use Symfony\Component\HttpFoundation\RequestStack;

class JWTCreatedListener
{
    /**
     * @var \Symfony\Component\HttpFoundation\RequestStack
     */
    private $requestStack;
    /**
     * @var \JMS\Serializer\SerializerInterface
     */
    private $serializer;
    /**
     * @var \App\Domain\User\Command\LoadUserContactClubs
     */
    private $loadUserContactClubs;
    /**
     * @var \Symfony\Bridge\Doctrine\ManagerRegistry
     */
    private $doctrine;

    public function __construct(RequestStack $requestStack, SerializerInterface $serializer, ManagerRegistry $doctrine)
    {
        $this->requestStack = $requestStack;
        $this->serializer = $serializer;
        $this->doctrine = $doctrine;
    }

    public function onJWTCreated(JWTCreatedEvent $event)
    {
        $payload = $event->getData();

        $user = $event->getUser();
        $payload['is_contact'] = false;

        /** @var OrganizationContact[] $contactAccounts */
        $contactAccounts = $this->doctrine->getRepository(OrganizationContact::class)->findBy(['user'=>$user->getId()]);
        $permissions = [];
        foreach ($contactAccounts as $contactAccount) {
            if ($contactAccount->getOrganization() instanceof PersonalGroup) {
                continue;
            }
            if ($contactAccount->getOrganization() instanceof Organization) {
                foreach ($contactAccount->getPermissions() as $permission) {
                    $permission->getPermission()->getRoleName();
                    $permissions[] = $permission->getPermission()->getRoleName().':'.$contactAccount->getOrganization()->getId();
                }
                $payload['is_contact'] = true;
            }
        }

        $context = new SerializationContext();
        $context->setGroups(['common']);
        $userData = json_decode($this->serializer->serialize($user, 'json',$context), true);

//        $payload['id'] = $user->getId();
        $payload['firstName'] = $user->getFirstName();
        $payload['lastName'] = $user->getLastName();
//        if (count($permissions) > 0) {
//            $payload['roles'] = array_merge($payload['roles'], $permissions);
//        }

        $payload = array_merge_recursive($payload, $userData);

        $event->setData($payload);
    }
}
