<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 10/18/2018
 * @time: 2:34 PM
 */

namespace App\Repository;


use App\Domain\Organization\Entity\OrganizationContact;
use App\Infrastructure\Domain\EntityHydrator;
use Doctrine\Common\Persistence\ManagerRegistry;
use GuzzleHttp\Client;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class OrganizationContactRepository extends BaseRepository
{

    public function __construct(KernelInterface $kernel, LoggerInterface $logger, Client $client, TokenStorageInterface $securityTokenStorage, EntityHydrator $entityHydrator, ManagerRegistry $registry)
    {
        parent::__construct($kernel, $logger, $client, $securityTokenStorage, $entityHydrator, $registry, OrganizationContact::class);
    }
}
