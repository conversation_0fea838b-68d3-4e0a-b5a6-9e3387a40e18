<?php
declare(strict_types=1);

namespace Mockingbird\Infrastructure\Request;


use <PERSON>ckingbird\Infrastructure\Logger\Logger;
use Mockingbird\Infrastructure\Task\Exception\NoTaskRequestedException;
use Mockingbird\Infrastructure\Task\Exception\TaskNotFoundException;
use Mockingbird\Infrastructure\Task\Exception\TaskPayloadNotFoundException;
use Mockingbird\Mockingbird;
use VertigoLabs\DataAware\DataAware;
use Vertigo<PERSON>abs\DataAware\DataAwareInterface;
use VertigoLabs\DataAware\Exceptions\DataNotFoundNoDefaultException;
use VertigoLabs\LoggerAware\LoggerAware;
use Vertigo<PERSON>abs\LoggerAware\LoggerAwareInterface;

abstract class Request implements RequestInterface, DataAwareInterface, LoggerAwareInterface
{
    use DataAware;
    use LoggerAware;

    /**
     * @return array
     * @throws TaskPayloadNotFoundException
     * @throws DataNotFoundNoDefaultException
     */
    public function determinePayload(): array
    {
        $payload = $this->getData('payload', []);
        if (!empty($payload) && !is_array($payload)) {
            throw new TaskPayloadNotFoundException();
        }
        if (empty($payload)) {
            $this->log('Empty task payload found', Logger::WARNING);
            $payload = [];
        }

        return $payload;
    }

    /**
     * @return string
     * @throws DataNotFoundNoDefaultException
     * @throws NoTaskRequestedException
     * @throws TaskNotFoundException
     */
    public function determineTask(): string
    {
        $task = $this->getData('task', false);
        if (empty($task)) {
            throw new NoTaskRequestedException();
        }
        if (!Mockingbird::Container()->has($task)) {
            throw new TaskNotFoundException($task);
        }

        return $task;
    }
}
