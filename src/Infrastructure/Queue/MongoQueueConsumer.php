<?php

namespace Clubster\Nightingale\Infrastructure\Queue;

use MongoDB\BSON\UTCDateTime;
use MongoDB\Client;
use MongoDB\Collection;
use Psr\Log\LoggerInterface;
use React\Promise\Promise;
use React\Promise\PromiseInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\DependencyInjection\Attribute\Exclude;
use function React\Promise\all;
use function React\Promise\resolve;

#[Exclude]
class MongoQueueConsumer implements QueueConsumerInterface
{
    private readonly Collection $collection;
    private readonly Client $client;

    public function __construct(
        private readonly LoggerInterface $logger,
        #[Autowire(param: 'nightingale.queue_name')]
        private readonly string $queueId,
        #[Autowire(param: 'mongoDbUri')]
        string $host,
        #[Autowire(param: 'mongoDbName')]
        string $database,
        #[Autowire(param: 'mongoCollectionName')]
        string $collection,
        #[Autowire(param: 'nightingale.active_notification_processors')]
        private readonly array $activeProcessors
    )
    {
        $this->client = new Client($host);
        $this->collection = $this->client->selectCollection($database, $collection);
    }

    public function readQueue(): PromiseInterface
    {
        $deliveryTypes = array_map(fn($processor) => substr($processor, 0, -9), array_keys(array_filter($this->activeProcessors)));
        $notificationData = [];

        for($findIterations = 0; $findIterations<(int)$_ENV['INTERVAL_MAX_MESSAGES']; $findIterations++) {
            $foundIds = array_column($notificationData, '_id');
            $cursor = $this->collection->findOneAndUpdate([
                '_id' => ['$nin' => $foundIds],
                '$or'=>[['queue' => null], ['queue' => $this->queueId]],
                'seen' => false,
                'notification_data.delivery_method'=>[
                    '$in'=>$deliveryTypes
                ]
            ],
            [
                '$set' => ['queue' => $this->queueId]
            ]);
            if($cursor) {
                $notificationData[] = $cursor->getArrayCopy();
            }
        }
        $this->logger->info(sprintf('Found %s messages to process', count($notificationData)));
        return all($notificationData);

    }

    public function markRecordsAsSeen($records)
    {
        if (empty($records)) {
            $this->logger->info('No records to mark as seen');
            return resolve();
        }

        $this->logger->info(sprintf('Marking %s records as seen', count($records)));
        $cacheExpireBson = new UTCDateTime(new \DateTime());
        $this->collection->updateMany(['_id' => ['$in'=>array_column($records, '_id')]], ['$set' => ['seen' => true, 'seen_at' => $cacheExpireBson]]);
        $this->collection->aggregate([
            ['$match'=>['_id' => ['$in'=>array_column($records, '_id')]]],
            ['$unset'=>'_id'],
            ['$merge'=>['into'=>'notifications_history', 'on'=>'_id', 'whenMatched'=>'replace', 'whenNotMatched'=>'insert']]
        ]);

        return $records;
    }

    public function markOneRecordAsSeen($record)
    {
        if (empty($record)) {
            $this->logger->info('No record to mark as seen');
            return resolve();
        }

        if (($_ENV['KEEP_QUEUE']??0) == 1) {
            return $record;
        }
        $this->logger->info(sprintf('[%s] Marking record as seen', (string)$record['_id']));
        $cacheExpireBson = new UTCDateTime(new \DateTime());
        $this->collection->updateMany(['_id' => $record['_id']], ['$set' => ['seen' => true, 'seen_at' => $cacheExpireBson]]);
        $this->collection->aggregate([
            ['$match'=>['_id' => $record['_id']]],
            ['$unset'=>'_id'],
            ['$merge'=>['into'=>'notifications_history', 'on'=>'_id', 'whenMatched'=>'replace', 'whenNotMatched'=>'insert']]
        ]);

        return $record;
    }

    public function markOneRecordAsFailed($record, $failureMessage)
    {
        if (empty($record)) {
            $this->logger->info('No record to mark as seen');
            return resolve();
        }

        $this->logger->info(sprintf('[%s] Marking record as seen', (string)$record['_id']));
        $cacheExpireBson = new UTCDateTime(new \DateTime());
        $this->collection->updateMany(['_id' => $record['_id']], ['$set' => ['seen'=>true,'failed' => true, 'failed_at' => $cacheExpireBson, 'fail_msg'=>$failureMessage]]);
//        $this->collection->aggregate([
//            ['$match'=>['_id' => $record['_id']]],
//            ['$unset'=>'_id'],
//            ['$merge'=>['into'=>'notifications_history', 'on'=>'_id', 'whenMatched'=>'replace', 'whenNotMatched'=>'insert']]
//        ]);

        return $record;
    }
}
