<?php

namespace Clubster\Nightingale\Infrastructure\Notifications;

use Clubster\Nightingale\Infrastructure\Notifications\Processor\EmailProcessor;
use Clubster\Nightingale\Infrastructure\Notifications\Processor\ProcessorInterface;
use Clubster\Nightingale\Infrastructure\Queue\MongoQueueConsumer;
use Clubster\Nightingale\Infrastructure\Template\TemplateManager;
use Clubster\Nightingale\Nightingale;
use GuzzleHttp\Utils;
use Psr\Log\LoggerInterface;
use React\Promise\Deferred;
use React\Promise\Promise;
use React\Promise\PromiseInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use function React\Promise\all;
use function React\Promise\map;
use function React\Promise\resolve;

class QueueProcessor implements ProcessorInterface
{
    private ProcessorInterface $emailSender;
    private ProcessorInterface $pushSender;
    private ProcessorInterface $internalSender;

    private array $processors = [];

    public function __construct(private readonly LoggerInterface $logger,
                                private readonly TemplateManager $templateManager,
                                #[Autowire(param: 'nightingale.active_notification_processors')]
                                private readonly array $activeProcessors,
                                private readonly MongoQueueConsumer $mongoQueueConsumer,
    )
    {

        foreach (array_filter($this->activeProcessors) as $processor=>$active) {
            $this->processors[$processor] = Nightingale::getContainer()->get($processor);
        }
    }

    public function process(array $data)
    {
        if (empty($data)) {
            return resolve();
        }
        $templateIds =  array_column(array_column($data, 'notification_data'), 'template_id', 'template_id');

        $result = resolve($this->templateManager->readTemplates($templateIds)
            ->then(function($templates) use ($data) {
                $this->logger->info(sprintf('Loaded %d templates', count($templates)));

                $templates = array_combine(array_column($templates, 'id'), $templates);
                return map($data, function($data) use ($templates) {
                    $data['rendered'] = $this->templateManager->renderTemplate($templates[$data['notification_data']['template_id']], $data['notification_data']['template_data']);
                    return $data;
                });
            })
            ->then(function ($data) {
                $promises = map($data, function ($notification){
                    $processor = $notification['notification_data']['delivery_method'].'Processor';
                    return $this->processors[$processor]->process($notification)->then(function($results) {
                        return $this->mongoQueueConsumer->markOneRecordAsSeen($results);
                    }, function ($result) use ($notification) {
                        return $this->mongoQueueConsumer->markOneRecordAsFailed($notification, $result->getMessage());
                    });
                });

                return resolve($promises);
            }));

        return $result;
    }
}
