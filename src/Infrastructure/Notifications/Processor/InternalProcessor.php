<?php

namespace Clubster\Nightingale\Infrastructure\Notifications\Processor;

use Drift\DBAL\ConnectionPool;
use Psr\Log\LoggerInterface;
use React\Promise\PromiseInterface;

class InternalProcessor implements ProcessorInterface
{
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly ConnectionPool $database,
    ){}
    public function process(array $data): PromiseInterface
    {
        $insertData = [
            'id'=>$data['notification_data']['id'],
            'recipient_id'=>$data['notification_data']['recipient_id'],
            'template_id'=>$data['notification_data']['template_id'],
            'status'=>'sent',
            'send_at'=>'now()',
            'created_at'=>'now()',
            'updated_at'=>'now()',
            'deleted_at'=>null,
            '"_discr"'=>$data['notification_data']['discr'],
            'template_data'=>json_encode($data['notification_data']['template_data']),
            'message_id'=>null,
            '"read"'=>false,
            'read_at'=>null,
            'sender'=>$data['notification_data']['sender'],
            'sender_organization'=>$data['notification_data']['sender_organization'],
            'recipient'=>$data['notification_data']['recipient'],
            'sent_at'=>'now()'
        ];

        return $this->database->upsert('notifications.internal', ['id'=>$data['notification_data']['id']], $insertData)
            ->then(function ($result) use ($data) {
                $this->logger->info(sprintf('[%s] Internal Sent.', $data['_id']));
                $data['success'] = true;
                return $data;
            })->otherwise(function($reason) use ($data) {
                $this->logger->info(sprintf('[%s] Internal failed.', $data['_id']));
                $data['success'] = false;
                return $data;
            });
    }
}
