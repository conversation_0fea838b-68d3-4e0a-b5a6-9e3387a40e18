<?php

namespace Clubster\Nightingale\Infrastructure\Notifications\Processor;

use Clubster\Nightingale\Infrastructure\Notifications\Processor\ProcessorInterface;
use Psr\Log\LoggerInterface;
use React\Promise\Promise;
use React\Promise\PromiseInterface;
use React\Http\Browser as HttpClient;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

class PushProcessor implements ProcessorInterface
{
    public function __construct(
        private readonly LoggerInterface $logger,
        #[Autowire(param: 'nightingale.queue_name')]
        private readonly string $queueId
    ){
        $this->httpClient = (new HttpClient())->withRejectErrorResponse(true)->withTimeout(false);

    }
    public function process(array $data): PromiseInterface
    {
        $headers = [
            'host' => "exp.host",
            'accept' => 'application/json',
            'accept-encoding' => 'gzip, deflate',
            'content-type' => 'application/json',
        ];

        $bodyText = $data['rendered']['body']['text']??null;
        if (!empty($data['rendered']['subject'])) {
            $bodyText = $data['rendered']['subject']."\n".$bodyText;
        }

        $body = [
            'to' => $data['notification_data']['recipient'],
            'title' => 'Clubster'??null,
            'body' => $bodyText,
            'data' => array_filter([
                'environment' => $_ENV['API_ENV'],
                'organization' => $data['notification_data']['template_data']['metadata']['sender_organization_id'] ?? null,
                'metadata' => $data['notification_data']['template_data']['metadata']??[]
            ]),
            'sound'=>'default',
            'badge' => 1
        ];

        // strip all html out of the body, but allow bold and italic
        $body['body'] = strip_tags($body['body'], '<b><i>');

        // if body subtitle is the same as body body, remove it
        if (empty($data['notification_data']['post_id']) || (!empty($body['subtitle']) && $body['body'] === $body['subtitle'])) {
            unset($body['subText'], $body['subtitle']);
        }

        if (!empty($body['subtitle'])) {
            $body['body'] = $body['subtitle']."\n". trim($body['body']);
            unset($body['subText'], $body['subtitle']);
        }

        $body['body'] = html_entity_decode(trim($body['body']));

        return $this->httpClient->post('https://exp.host/--/api/v2/push/send', $headers, json_encode($body))
            ->then(function($response) use ($data) {
                $response = (string)$response->getBody()->getContents();
                $response = \Safe\json_decode($response, true);
                $data['result'] = $response['data'];
                $data['success'] = true;
                $this->logger->info(sprintf('[%s] Push Sent.', $data['_id']));
                return new Promise(function($resolve, $reject) use ($data) {
                    $resolve($data);
                });
            }, function($error) use ($data) {
                $this->logger->error(sprintf('[%s] Push Failed.', $data['_id']));
                $data['success'] = false;
                return new Promise(function($resolve, $reject) use ($data) {
                    $reject($data);
                });
            });
    }
}
