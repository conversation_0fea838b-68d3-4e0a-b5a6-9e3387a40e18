<?php
declare(strict_types=1);
namespace Clubster\Nightingale\Infrastructure\Notifications\Processor;

use Aws\Ses\SesClient;
use Psr\Log\LoggerInterface;
use React\Promise\Promise;
use React\Promise\PromiseInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

class EmailProcessor implements ProcessorInterface
{
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly SesClient $sesClient,
        #[Autowire(param: 'nightingale.queue_name')]
        private readonly string $queueId
    ) {}

    public function process(array $data): PromiseInterface
    {
        return new Promise(function ($resolver) use ($data) {
            $notificationData = $data['notification_data'];
            $recipient = $data['notification_data']['recipient'];
            if (!empty($_ENV['FORCE_RECIPIENT'])) {
                $recipient = $_ENV['FORCE_RECIPIENT'];
            }
            $sender = sprintf('%s <%s>', !empty($notificationData['template_data']['metadata']['sender']) ? $notificationData['template_data']['metadata']['sender'] : 'Clubster', '<EMAIL>');
            $replyTo = 'notify+' . $notificationData['id'] . '@clubsteremail.com';
            $tags = array_values(array_filter([
                [
                    'Name' => 'recipient_id',
                    'Value' => $notificationData['recipient_id']??'',
                ],
                [
                    'Name' => 'notification_id',
                    'Value' => $notificationData['id']??'',
                ],
                [
                    'Name' => 'sender_id',
                    'Value' => $notificationData['sender']??'',
                ],
                [
                    'Name' => 'organization_id',
                    'Value' => $notificationData['sender_organization']??'',
                ],
                [
                    'Name' => 'post_id',
                    'Value' => $notificationData['post_id']??'',
                ],
                [
                    'Name' => 'queue_id',
                    'Value' => $this->queueId,
                ],
            ], fn ($tag) => !empty($tag['Value'])));
            $result = $this->sesClient->sendEmail([
                'ConfigurationSetName' => $_ENV['AWS_SES_CONFIG_SET'],
                'Source' => $sender,
                'ReplyToAddresses' => [$replyTo],
                'Destination' => [
                    'ToAddresses' => [$recipient],
                ],
                'Message' => [
                    'Subject' => [
                        'Data' => $data['rendered']['subject'],
                        'Charset' => 'UTF-8',
                    ],
                    'Body' => [
                        'Text' => [
                            'Data' => $data['rendered']['body']['text'],
                            'Charset' => 'UTF-8',
                        ],
                        'Html' => [
                            'Data' => $data['rendered']['body']['html'],
                            'Charset' => 'UTF-8',
                        ],
                    ],
                ],
                'Tags' => $tags,
            ]);

            $this->logger->info(sprintf('[%s] Email Sent to %s. message id: %s', $data['_id'], $data['notification_data']['recipient'], $result->get('MessageId')));
            $data['result'] = $result->toArray();
            $data['success'] = !empty($data['result']['MessageId']);
            return $resolver($data);
        });
    }
}
