<?php

namespace Mockingbird\Infrastructure\Falcon;

use Guzzle<PERSON>ttp\Client;
use GuzzleHttp\Exception\ServerException;
use GuzzleHttp\Psr7\MultipartStream;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\RequestOptions;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class FalconRequest
{
    private static $errors = [];
    public static function dumpRequest($task, $data)
    {
        $payload = self::prepPayload($data, 'payload');
        array_unshift($payload,[
            'name'=>'task',
            'contents'=>$task
        ]);

        $outputData = '';
        foreach ($payload as $payloadItem) {
            $outputData .= $payloadItem['name'].':'.$payloadItem['contents']."\r\n";
        }
        return $outputData;
    }

    /**
     * @param string $task
     * @param array $data
     * @param callable $callback
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function execute($task, $data, $callback = null)
    {
        try {
            self::$errors = [];
            $client = new Client([
                'base_uri' => $_ENV['FALCON_URL'],
                RequestOptions::VERIFY => false
            ]);

            $payload = self::prepPayload($data, 'payload');
            array_unshift($payload,[
                'name'=>'task',
                'contents'=>$task
            ]);

            $body = new MultipartStream($payload);

//            $options[RequestOptions::MULTIPART] = $payload;
            $request = new Request('POST', $_ENV['FALCON_URL']);
            $request = $request->withHeader('X-U', '689ab575-0f37-4253-8b72-42fbcae011e2');
            $request = $request->withBody($body);

            $resp = $client->send($request);

            $resp = (string)$resp->getBody()->getContents();
            if (!empty($resp)) {
                $resp = json_decode($resp, true);
            }

            if (is_callable($callback)) {
                $resp = $callback($resp);
            }

            return $resp;
        }catch (ServerException $e){
            $content = $e->getResponse()->getBody()->getContents();
            try {
                $decoded = json_decode($content, false, 512, JSON_THROW_ON_ERROR);
                self::$errors = $decoded;
                self::$errors['exception'] = $e;
            }catch (\Exception $decodeException) {
                self::$errors = ['error'=>$content, 'exception'=>$e];
            }
        }catch (\Exception $e){
            self::$errors = ['exception'=>$e->getMessage()];
        }
    }

    public static function hasErrors()
    {
        return !empty(self::$errors);
    }

    public static function getErrors()
    {
        return self::$errors;
    }

    private static function  prepPayload($data, $key=null, &$out=[])
    {
        foreach ($data as $fieldName=>$value) {
            $fieldName = empty($key)?$fieldName:$key.'['.$fieldName.']';
            if (is_array($value)) {
                $out = array_merge($out, self::prepPayload($value, $fieldName));
                continue;
            }
            if ($value instanceof \SplFileInfo) {
                $out[] = [
                    'name'=>$fieldName,
                    'contents'=>fopen($value->getRealPath(),'r')
                ];
                continue;
            }
            $out[] = [
                'name'=>$fieldName,
                'contents'=>$value
            ];
        }

        return $out;
    }
}
