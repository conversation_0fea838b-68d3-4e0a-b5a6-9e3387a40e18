<?php

namespace Clubster\Nightingale\Infrastructure\Socket;

use Psr\Log\LoggerInterface;
use <PERSON>act\Http\Browser as HttpClient;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

class SocketWriter
{
    private HttpClient $httpClient;
    public function __construct(
        private readonly LoggerInterface $logger,
        #[Autowire(param: 'nightingale.queue_name')]
        private readonly string $queueId,
        #[Autowire(param: 'socketUrl')]
        private readonly string $socketUrl,
        #[Autowire(param: 'socketApiKey')]
        private readonly string $socketApiKey
    )
    {
        $this->httpClient = (new HttpClient())->withRejectErrorResponse(true)->withTimeout(false);
    }

    public function writeData(array $data, string $channel)
    {
        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'apikey '.$this->socketApiKey,
        ];

        $message = [
            'channel' => $channel,
            'queue' => $this->queueId,
            'data' => $data
        ];

        return $this->httpClient->post($this->socketUrl.'/publish', $headers, json_encode($message, JSON_THROW_ON_ERROR))
            ->then(function ($response){
                $response = (string)$response->getBody()->getContents();
                $response = \Safe\json_decode($response, true);
                if (isset($response['error'])) {
                    $this->logger->error('Failed to publish message: ' . $response['error']['message'].' - '.$this->socketUrl);
                } else {
                    $this->logger->info('Message published to socket');
                }
                return $response;
            }, function($error){
                $this->logger->error('Failed to publish message: ' . $error->getMessage().' - '.$this->socketUrl);
                return [];
            });
    }
}
