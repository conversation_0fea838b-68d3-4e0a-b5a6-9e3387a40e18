<?php
namespace Clubster\Nightingale\Infrastructure\Statistics;

use Drift\DBAL\ConnectionPool;
use Psr\Log\LoggerInterface;
use S<PERSON>fony\Component\DependencyInjection\Attribute\Autowire;
use function React\Promise\map;
use function React\Promise\resolve;

class TimeSeriesStatisticsManager
{
    public function __construct(
        private readonly LoggerInterface $logger,
        #[Autowire(service: 'TimeSeriesConnectionPool')]
        private readonly ConnectionPool $database,
        #[Autowire(param: 'nightingale.queue_name')]
        private readonly string $queueId
    ){}

    public function writeStats($records)
    {
//        return resolve($records);
        if (empty($records)) {
            $this->logger->info('No notifications to log stats for');
            return resolve();
        }

        $result = map($records, function ($data) {
            return (new \Fiber(function () use ($data) {
                $this->logger->info('Writing statistic record to time series database');
                $insertData = [
                    'type' => $data['notification_data']['delivery_method'],
                    'event_type' => 'send',
                    'recipient_id' => $data['notification_data']['recipient_id'],
                    'sender_id' => $data['notification_data']['sender'] ?? null,
                    'organization_id' => $data['notification_data']['sender_organization'] ?? null,
                    'post_id' => $data['notification_data']['post_id'] ?? null,
                    'queue_id' => $this->queueId,
                ];
                $result = $this->database->insert('notification_statistics', $insertData)
                    ->then(function ($result) use ($data) {
                        $this->logger->info('Successfully wrote statistic record to time series database');
                        return $data;
                    });
                return $result;
            }))->start();
        });
        return $records;
    }
}
