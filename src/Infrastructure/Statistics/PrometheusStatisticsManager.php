<?php

namespace Clubster\Nightingale\Infrastructure\Statistics;

use Clubster\Nightingale\Infrastructure\Statistics\Prometheus\CounterMetric;
use Prometheus\CollectorRegistry;
use Prometheus\Storage\Redis;
use Psr\Log\LoggerInterface;
use function React\Promise\map;
use function React\Promise\resolve;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

class PrometheusStatisticsManager
{
    public function __construct(
        private readonly LoggerInterface $logger,
        #[Autowire(param: 'nightingale.queue_name')]
        private readonly string $queueId,
        private readonly Redis $adapter
    ){}

    public function writeStats($records)
    {
        if (empty($records)) {
            $this->logger->info('No notifications to log stats for');
            return resolve();
        }

        $registry = new CollectorRegistry($this->adapter);

        $results = map($records, function ($data) use ($registry){
            $stop = $data;
            return ( new \Fiber(function () use ($data, $registry){
                $record = new CounterMetric(
                    namespace: 'nightingale',
                    name: 'notifications',
                    help: 'Number of notifications sent',
                    labels: [
                        'status' => $data['success'] ? 'sent' : 'failed',
                        'delivery_method' => $data['notification_data']['delivery_method'],
                        'sender' => $data['notification_data']['sender'],
                        'recipient' => $data['notification_data']['recipient'],
                        'queue_id' => $this->queueId,
                        'event_type' => 'send',
                        'type' => $data['notification_data']['delivery_method'],
                    ],
                    increment: 1
                );

                $this->logger->info('Writing statistic record to prometheus');
                $counter = $registry->getOrRegisterCounter(
                    namespace: 'nightingale',
                    name: 'notifications',
                    help: $record->getHelp(),
                    labels: array_keys($record->getLabels())
                );

                $counter->incBy($record->getIncrement(), $record->getLabels());
            }))->start();
        });
        return $records;
    }
}
