<?php

namespace Clubster\Nightingale\Infrastructure\Statistics;

use Clubster\Nightingale\Infrastructure\Socket\SocketWriter;
use <PERSON><PERSON>\Nightingale\Nightingale;
use Psr\Log\LoggerInterface;
use React\Http\Browser as HttpClient;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

class SocketStatisticsManager
{
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly SocketWriter $socketWriter,
        #[Autowire(param: 'nightingale.queue_name')]
        private readonly string $queueId
    ){
    }

    public function writeStats($records)
    {
        $channel = 'admin:notification_queue_statistics';
        $data = [
            'nodes-timestamp'=> time(),
            'queue'=> [
                'id'=>$this->queueId,
                'iteration'=>Nightingale::getIteration(),
                'age'=>Nightingale::getAge(),
                'processed_last'=>Nightingale::getProcessedLast(),
                'processed_total'=>Nightingale::getProcessedTotal(),
                'timing' => [
                    'last_process_time' => Nightingale::getLastProcessTime(),
                    'average_process_time' => Nightingale::getAverageProcessTime(),
                ]
            ]
        ];

        return $this->publishMessage($data, $channel)->then(function ($results) use ($records) {
            return $records;
        });
    }

    public function publishMessage($data, $channel)
    {
        if (!isset($data['queue']['id'])) {
            $data['queue']['id'] = $this->queueId;
        }

        if (!isset($data['queue']['iteration'])) {
            $data['queue']['iteration'] = Nightingale::getIteration();
        }

        if (!isset($data['queue']['age'])) {
            $data['queue']['age'] = Nightingale::getAge();
        }

        if (!isset($data['queue']['processed_last'])) {
            $data['queue']['processed_last'] = Nightingale::getProcessedLast();
        }

        if (!isset($data['queue']['processed_total'])) {
            $data['queue']['processed_total'] = Nightingale::getProcessedTotal();
        }

        return $this->socketWriter->writeData($data, $channel);
    }
}
