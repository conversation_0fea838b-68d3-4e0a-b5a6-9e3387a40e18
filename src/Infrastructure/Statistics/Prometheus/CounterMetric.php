<?php

namespace Clubster\Nightingale\Infrastructure\Statistics\Prometheus;

class CounterMetric
{
    public function __construct(
        private readonly string $namespace,
        private readonly string $name,
        private readonly ?string $help = null,
        private readonly array $labels = [],
        private readonly int $increment = 1
    ){}

    public function getNamespace(): string
    {
        return $this->namespace;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getHelp(): string
    {
        return $this->help;
    }

    public function getLabels(): array
    {
        return $this->labels;
    }

    public function getIncrement(): int
    {
        return $this->increment;
    }
}
