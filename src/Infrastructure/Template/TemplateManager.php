<?php

namespace Clubster\Nightingale\Infrastructure\Template;

use Drift\DBAL\ConnectionPool;
use Drift\DBAL\Result;
use Psr\Log\LoggerInterface;
use React\Promise\PromiseInterface;
use function React\Promise\resolve;

class TemplateManager
{
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly ConnectionPool $database,
        private readonly TwigRenderer $twigRenderer
    )
    {}

    public function readTemplates(array $templateIds): PromiseInterface
    {
        if (empty($templateIds)) {
            return resolve();
        }
        $this->logger->info(sprintf('reading template %s', implode(', ',$templateIds)));
        $query = 'SELECT * FROM notifications.templates WHERE id in (\''.implode('\',\'',$templateIds).'\')';
        $this->logger->debug($query);

        return $this->database->queryBySQL($query)
            ->then(function(Result $result) {
                $this->logger->info(sprintf('Loaded %d templates', $result->fetchCount()));
                return $result->fetchAllRows();
            });

    }

    public function renderTemplate($template, $data): array
    {
        return $this->twigRenderer->render($template, $data);
    }
}
