<?php
declare(strict_types=1);
namespace Clubster\Nightingale\Infrastructure\Template;

use Monolog\Logger;
use Psr\Log\LoggerInterface;
use Twig\Loader\FilesystemLoader;

class TwigRenderer
{
    private \Twig\Environment $twig;
    public function __construct(private readonly LoggerInterface $logger)
    {
        $templateLoader = new FilesystemLoader(getcwd().'/templates');
        $this->twig = new \Twig\Environment($templateLoader, ['cache'=>false,'autoescape'=>false]);
    }
    public function render($template, $data): array
    {
        $templateData = array_merge([
            'API_ENV' => $data['API_ENV']??$_ENV['API_ENV'],
            'WWW_URL' => $data['WWW_URL']??$_ENV['WWW_URL'],
        ], (array)$data);

        $templateData['template_slug'] = $template['slug'];
        $templateData['template_method'] = $template['delivery_method'];

        $bodyHTML = $bodyText = $this->twig->createTemplate($template['body_text'])->render($templateData);
        $subject = $this->twig->createTemplate($template['subject'])->render($templateData);

        if (!empty($template['body_html'])) {
            $roundLogo = true;

            $twigTemplate = $this->twig->createTemplate($template['body_html']);

            $headline = null;
            if ($twigTemplate->hasBlock('headline')) {
                $headline = $twigTemplate->renderBlock('headline', $templateData);
            }

            $body = null;
            if ($twigTemplate->hasBlock('body')) {
                $body = $twigTemplate->renderBlock('body', $templateData);
            }else{
                $body = $twigTemplate->render($templateData);
            }

            if (!empty($headline)) {
                $headline = $this->twig->createTemplate($headline)->render($templateData);
            }

            if (!empty($body)) {
                $body = $this->twig->createTemplate($body)->render($templateData);
                $body = html_entity_decode($body);
            }

            $bodyHTML = $body;
            if ($data['includeBaseTemplate']??true) {
                $baseTemplate = 'notifications' . DIRECTORY_SEPARATOR . 'email' . DIRECTORY_SEPARATOR . 'base.html.twig';

                $bodyHTML = $this->twig->load($baseTemplate)->render(array_merge($templateData, [
                    'headline' => $headline,
                    'body' => $body,
                    'roundLogo' => $roundLogo
                ]));
            }
        }

        return [
            'id'=>$template['id'],
            'body'=>[
                'text'=>trim($bodyText),
                'html'=>trim($bodyHTML)
            ],
            'subject'=>$subject
        ];
    }
}
