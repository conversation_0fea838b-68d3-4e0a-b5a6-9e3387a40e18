<?php
declare(strict_types=1);
namespace Mockingbird\Infrastructure\Task;

use Mockingbird\Infrastructure\Database\DatabaseAware;
use <PERSON>ckingbird\Infrastructure\Database\DatabaseAwareInterface;
use Mockingbird\Infrastructure\DependencyInjection\ContainerAwareInterface;
use Mockingbird\Infrastructure\DependencyInjection\ContainerAwareTrait;
use Mockingbird\Infrastructure\Request\RequestAware;
use Mockingbird\Infrastructure\Request\RequestAwareInterface;
use Mockingbird\Infrastructure\Response\ResponseAware;
use Mockingbird\Infrastructure\Response\ResponseAwareInterface;
use Mockingbird\Mockingbird;
use Ramsey\Uuid\Uuid;
use VertigoLabs\DataAware\DataAware;
use VertigoLabs\DataAware\DataAwareInterface;
use VertigoLabs\LoggerAware\LoggerAware;
use VertigoLabs\LoggerAware\LoggerAwareInterface;
use VertigoLabs\ValidationAware\Exception\ValidationViolationException;
use VertigoLabs\ValidationAware\ValidationAware;
use VertigoLabs\ValidationAware\ValidationAwareInterface;
use function React\Promise\reject;

abstract class Task implements TaskInterface, DataAwareInterface, ValidationAwareInterface, LoggerAwareInterface, ContainerAwareInterface, RequestAwareInterface, ResponseAwareInterface, DatabaseAwareInterface
{
    use DataAware;
    use LoggerAware;
    use ValidationAware;
    use ContainerAwareTrait;
    use RequestAware;
    use ResponseAware;
    use DatabaseAware;

    protected Mockingbird $mockingbird;
    protected string $taskGuid;
    protected string $taskName;

    public function __construct()
    {
        $this->taskGuid = (string)Uuid::uuid4();
        $this->taskName = str_replace('\\', ':',substr(static::class, 17));
        TaskStack::registerTask($this->taskGuid, ['name'=>static::class]);
        $this->container = Mockingbird::Container();
    }

    abstract protected function run();

    final public function __invoke()
    {
        try {
            TaskStack::report($this->taskGuid, TaskStack::TASK_STATE_BOOT);

//            $this->log('Input Data | Beginning Validation', Logger::DEBUG);
            $violations = $this->validate();
            if ($violations->count() > 0) {
                foreach ($violations as $violation) {
                    TaskStack::report($this->taskGuid, TaskStack::TASK_STATE_FAILURE, [
                        'field' => $violation->getPropertyPath(),
                        'message' => $violation->getMessage()
                    ]);
                }
                throw new ValidationViolationException(static::class, $violations->count());
            }
//            $this->log('Input Data | Validation Complete', Logger::DEBUG);

            TaskStack::report($this->taskGuid, TaskStack::TASK_STATE_RUNNING);
            try {
                $this->log(sprintf('<comment>Running Task <info>%s</info> with data <info>%s</info></comment>', $this->taskName, json_encode($this->getData(), JSON_THROW_ON_ERROR)));
            }catch (\Exception $e){}
            $result = $this->run();
            TaskStack::report($this->taskGuid, TaskStack::TASK_STATE_STOPPED);

            if (is_string($result) || is_array($result)) {
                TaskStack::report($this->taskGuid, TaskStack::TASK_STATE_COMPLETE, $result);
            } else {
                    TaskStack::report($this->taskGuid, TaskStack::TASK_STATE_COMPLETE, '[object]');
            }

            return $result;
        }catch (\Exception $exception) {
            TaskStack::report($this->taskGuid, TaskStack::TASK_STATE_FATAL, ['msg'=>$exception->getMessage()]);
            return reject($exception);
        }
    }

    final public function getTaskGuid(): string
    {
        return $this->taskGuid;
    }

    public function __destruct()
    {
        if (!empty($this->taskGuid)) {
            TaskStack::report($this->taskGuid, TaskStack::TASK_STATE_CLOSED);
        }
    }
}
