<?php
declare(strict_types=1);
namespace Mockingbird\Infrastructure\Task;

use Mockingbird\Infrastructure\Request\RequestInterface;
use Mockingbird\Infrastructure\Response\ResponseInterface;
use <PERSON>ckingbird\Mockingbird;
use React\Promise\Promise;
use React\Promise\PromiseInterface;
use Vertigo<PERSON><PERSON>s\LoggerAware\LoggerAware;
use Vertigo<PERSON><PERSON>s\LoggerAware\LoggerAwareInterface;

class TaskStack implements LoggerAwareInterface
{
    use LoggerAware;

    public const TASK_STATE_CREATED = 'CREATED';
    public const TASK_STATE_BOOT = 'BOOT';
    public const TASK_STATE_RUNNING = 'RUNNING';
    public const TASK_STATE_STOPPED = 'STOPPED';
    public const TASK_STATE_COMPLETE = 'COMPLETE';
    public const TASK_STATE_CLOSED = 'CLOSED';
    public const TASK_STATE_ERROR = 'ERROR';
    public const TASK_STATE_FAILURE = 'FAILURE';
    public const TASK_STATE_FATAL = 'FATAL';

    private static array $stack = [];
    protected static RequestInterface $request;

    public static function registerTask($id, $data)
    {
        self::report($id, self::TASK_STATE_CREATED, $data);
    }

    public static function report(string $id, string $state, $data=null): void
    {
        if (!array_key_exists($id, self::$stack)) {
            self::$stack[$id] = [];
        }

        self::$stack[$id][] = [
            'state'=>$state,
            'data'=>$data,
            'time'=>microtime(true)
        ];
    }

    /**
     * @return array
     */
    public static function getStack(): array
    {
        return self::$stack;
    }

    public static function handle(RequestInterface $request): PromiseInterface
    {
        $taskName = $request->determineTask();
        $payload = $request->determinePayload();

        /** @var TaskInterface $task */
        $task = Mockingbird::Container()->get($taskName);
        $task->setData($payload);

        $promise = new Promise(function ($resolve, $reject, $notify) use ($task) {
            $resolve($task());
        });

        return $promise;
    }
}
