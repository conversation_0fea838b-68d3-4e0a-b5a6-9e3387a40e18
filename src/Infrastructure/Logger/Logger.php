<?php


namespace Mockingbird\Infrastructure\Logger;


use Monolog\Handler\StreamHandler;
use Symfony\Component\Console\Output\ConsoleOutput;
use VertigoLabs\LoggerAware\LoggerInterface;

class Logger extends \Monolog\Logger implements LoggerInterface
{
    /**
     * Detailed debug information
     */
    public const DEBUG = 100;

    /**
     * Interesting events
     *
     * Examples: User logs in, SQL logs.
     */
    public const INFO = 200;

    /**
     * Uncommon events
     */
    public const NOTICE = 250;

    /**
     * Exceptional occurrences that are not errors
     *
     * Examples: Use of deprecated APIs, poor use of an API,
     * undesirable things that are not necessarily wrong.
     */
    public const WARNING = 300;

    /**
     * Runtime errors
     */
    public const ERROR = 400;

    /**
     * Critical conditions
     *
     * Example: Application component unavailable, unexpected exception.
     */
    public const CRITICAL = 500;

    /**
     * Action must be taken immediately
     *
     * Example: Entire website down, database unavailable, etc.
     * This should trigger the SMS alerts and wake you up.
     */
    public const ALERT = 550;

    /**
     * Urgent alert.
     */
    public const EMERGENCY = 600;

    public function writeLog(string $message, ?int $level = null, array|string|null $channel = null): LoggerInterface
    {
        $this->log($level??200, $message);
        return $this;
    }

    public function enableConsoleLogging(ConsoleOutput $console): LoggerInterface
    {
        $this->pushHandler(new StreamHandler($console->getStream()));
        return $this;
    }
}
