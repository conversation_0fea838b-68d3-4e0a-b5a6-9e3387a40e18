<?php

namespace Mockingbird\Infrastructure\Logger;

use Symfony\Component\Console\Output\Output;
use VertigoLabs\LoggerAware\LoggerInterface;

class ConsoleLogger extends Logger
{
    private Output $output;
    private ?int $minLevel;

    public function setOutput(Output $output, ?int $minLevel = 0): ConsoleLogger
    {
        $this->output = $output;
        $this->minLevel = $minLevel;
        return $this;
    }

    public function writeLog(string $message, ?int $level = null, array|string|null $channel = null): LoggerInterface
    {
        if ($level >= $this->minLevel || empty($level)) {
            $this->output->writeln($message);
        }
        parent::writeLog(...func_get_args());
        return $this;
    }

}
