<?php
declare(strict_types=1);
namespace Mockingbird;

use Mockingbird\Infrastructure\Configuration\Configuration;
use <PERSON>cking<PERSON>\Infrastructure\Listener\Listener;
use Mockingbird\Infrastructure\Listener\ListenerInterface;
use Mockingbird\Infrastructure\Logger\ConsoleLogger;
use Mockingbird\Infrastructure\Logger\Logger;
use Mockingbird\Infrastructure\Task\Compiler\TaskCompilerPass;
use Mockingbird\Listener\Http\Compiler\RoutableTaskCompilerPass;
use Symfony\Bridge\Monolog\Handler\ConsoleHandler;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\Console\Input\ArgvInput;
use Symfony\Component\Console\Output\ConsoleOutput;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Dumper\PhpDumper;
use Symfony\Component\DependencyInjection\Dumper\XmlDumper;
use Symfony\Component\DependencyInjection\Loader\PhpFileLoader;
use VertigoLabs\LoggerAware\LoggerAware;
use VertigoLabs\LoggerAware\LoggerAwareInterface;

class Mockingbird implements LoggerAwareInterface
{
    use LoggerAware { log as public; }

    private Configuration $config;
    private array $listeners = [];
    private static ?ContainerInterface $container = null;
    private string $basePath;
    private string $cachePath;
    private ArgvInput $input;
    private ConsoleOutput $output;
    private static Mockingbird $instance;
    public static int $bootTime;

    public function __construct(Configuration $config)
    {
        self::$bootTime = time();

        $this->input = new ArgvInput();
        $this->output = new ConsoleOutput();

        $logger = new Logger('Mockingbird');
        $logger->pushHandler(new ConsoleHandler($this->output));
        $this->setLogger($logger);

//        if ($this->input->hasParameterOption('-i')) {
            $this->setLogger((new ConsoleLogger('Mockingbird-Interactive'))->setOutput($this->output, Logger::INFO));
//        }

        $this->basePath = dirname(__DIR__, );
        $this->cachePath = $this->basePath.DIRECTORY_SEPARATOR.'cache';

        self::$container = $this->buildContainer(true);
        $this->config = $config;

        try {
            foreach ($this->config->getData('listeners') as $listenerName => $listenerConfiguration) {
                $listener = self::$container->get($listenerConfiguration['listener']);
                if ($listener instanceof ListenerInterface) {
                    $listener->setConfiguration(new Configuration($listenerConfiguration));
                    $listener->initialize();
                    $this->listeners[$listenerName] = $listener;
                }
            }
        }catch (\Exception $exception) {
            $this->log(sprintf('Exception: %s', $exception->getMessage()), Logger::CRITICAL);
        }

        self::$instance = $this;
    }

    public static function Container(): ContainerInterface
    {
        return self::$container;
    }

    public function run(): void
    {
        $this->announceBanner();
        foreach ($this->listeners as $listenerName=>$listener) {
            $listener->run();
        }
    }

    private function buildContainer($forceRebuild = false): ContainerInterface
    {
        $cachedContainer = $this->cachePath.DIRECTORY_SEPARATOR.'container-cache.php';

        if ($forceRebuild === false && file_exists($cachedContainer)) {
            require_once $cachedContainer;
            if (class_exists('ProjectServiceContainer', false)) {
                return new ProjectServiceContainer();
            }
        }

        $this->log('Building Service Container');

        $containerBuilder = new ContainerBuilder();
        $containerBuilder->addCompilerPass(new TaskCompilerPass());
        $containerBuilder->addCompilerPass(new RoutableTaskCompilerPass());
        $containerBuilder->set(__CLASS__, $this);
        $containerBuilder->set(Logger::class, $this->logger);
        $containerBuilder->setParameter('basePath', $this->basePath);
        $containerBuilder->setParameter('cachePath', $this->cachePath);

        $loader = new PhpFileLoader($containerBuilder, new FileLocator($this->basePath.DIRECTORY_SEPARATOR.'config'));
        $loader->load('services.php');
        $containerBuilder->compile();

        $dumper = new PhpDumper($containerBuilder);
        $xmlDumper = new XmlDumper($containerBuilder);

        if (!is_writable($this->cachePath) && !chmod($this->cachePath, 0664)) {
            $this->log(sprintf('Container cache directory is not writeable: "%s"', $this->cachePath));
            return $containerBuilder;
        }

        if (!file_exists($cachedContainer)) {
            touch($cachedContainer);
        }

        if (!is_writable($cachedContainer)) {
            $this->log(sprintf('Container cache file is not writeable "%s"',$cachedContainer));
            return $containerBuilder;
        }

//        file_put_contents($cachedContainer, $dumper->dump());
        file_put_contents($cachedContainer.'.xml', $xmlDumper->dump());

        return $containerBuilder;
    }

    /**
     * @return Configuration
     */
    public function getConfig(): Configuration
    {
        return $this->config;
    }

    /**
     * @return Listener[]
     */
    public static function Listeners()
    {
        return self::$instance->listeners;
    }

    private function announceBanner()
    {
        $this->output->write('<info>

███╗   ███╗ ██████╗  ██████╗██╗  ██╗██╗███╗   ██╗ ██████╗ ██████╗ ██╗██████╗ ██████╗ 
████╗ ████║██╔═══██╗██╔════╝██║ ██╔╝██║████╗  ██║██╔════╝ ██╔══██╗██║██╔══██╗██╔══██╗
██╔████╔██║██║   ██║██║     █████╔╝ ██║██╔██╗ ██║██║  ███╗██████╔╝██║██████╔╝██║  ██║
██║╚██╔╝██║██║   ██║██║     ██╔═██╗ ██║██║╚██╗██║██║   ██║██╔══██╗██║██╔══██╗██║  ██║
██║ ╚═╝ ██║╚██████╔╝╚██████╗██║  ██╗██║██║ ╚████║╚██████╔╝██████╔╝██║██║  ██║██████╔╝
╚═╝     ╚═╝ ╚═════╝  ╚═════╝╚═╝  ╚═╝╚═╝╚═╝  ╚═══╝ ╚═════╝ ╚═════╝ ╚═╝╚═╝  ╚═╝╚═════╝ 
             Mockingbird Realtime Framework. (c) Clubster LLC 2021
 
</info>');

        foreach ($this->listeners as $listenerName=>$listener) {
            $this->output->writeln(sprintf('<comment>Initializing <info>%s</info> with listener <info>%s</info></comment>', $listenerName, get_class($listener)));
            $this->output->writeln(sprintf('<comment><info>%s</info> Listening on <info>%s</info></comment>', $listenerName, $listener->getUri()));
        }
    }
}
