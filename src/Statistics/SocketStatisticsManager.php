<?php

namespace Clubster\Puffin\Statistics;

use Psr\Log\LoggerInterface;
use React\Http\Browser as HttpClient;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

class SocketStatisticsManager
{
    private HttpClient $httpClient;

    public function __construct(
        private readonly LoggerInterface $logger,
        #[Autowire(param: 'socketUrl')]
        private readonly string $socketUrl,
        #[Autowire(param: 'socketApiKey')]
        private readonly string $socketApiKey,
        #[Autowire(param: 'socketSecretKey')]
        private readonly string $socketSecretKey
    ){
        $this->httpClient = (new HttpClient())->withRejectErrorResponse(true)->withTimeout(false);
    }

    public function publishMessage($data, $channel)
    {
        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'apikey '.$this->socketApiKey,
        ];

        $message = [
            'channel' => $channel,
            'data' => $data
        ];

        return $this->httpClient->post($this->socketUrl.'/publish', $headers, json_encode($message, JSON_THROW_ON_ERROR))
            ->then(function ($response){
                $response = (string)$response->getBody()->getContents();
                $response = json_decode($response, true);
                if (isset($response['error'])) {
                    $this->logger->error(sprintf('[%-28s] %s','Socket Publish','Failed to publish message: ' . $response['error']['message']));
                } else {
                    $this->logger->info(sprintf('[%-28s] %s','Socket Publish','Message published to socket'));
                }
                return $response;
            }, function($error){
                $this->logger->error(sprintf('[%-28s] %s','Socket Publish','Failed to publish message: ' . $error->getMessage()));
                return [];
            });
    }
}
