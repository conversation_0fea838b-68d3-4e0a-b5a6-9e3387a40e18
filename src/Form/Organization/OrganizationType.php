<?php

namespace App\Form\Organization;

use App\Domain\Organization\Entity\Organization;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class OrganizationType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('name', TextType::class)
            ->add('description', TextareaType::class,[
                'required' => true
            ])
            ->add('orientation', ChoiceType::class, [
                'choices'=>[
                    ucwords(Organization::ORIENTATION_PRIVATE) => Organization::ORIENTATION_PRIVATE,
                    ucwords(Organization::ORIENTATION_PUBLIC) => Organization::ORIENTATION_PUBLIC,
                    ucwords(Organization::ORIENTATION_SEMIPRIVATE) => Organization::ORIENTATION_SEMIPRIVATE,
                ]
            ])
            ->add('foundingDate', DateType::class, [
                'attr'=>['class'=>'js-datepicker'],
                'widget' => 'single_text',
                'html5'=>false,
                'format'=>'MM-dd-yyyy',
                'required'=>false,
                'empty_data' => ''
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => Organization::class,
        ]);
    }
}
