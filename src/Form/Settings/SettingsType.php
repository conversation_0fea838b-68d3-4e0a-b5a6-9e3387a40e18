<?php

namespace App\Form\Settings;

use Carbon\Carbon;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;

class SettingsType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder->addEventListener(FormEvents::PRE_SET_DATA, function (FormEvent $event) use ($builder, $options): void {
            $categories = [];

            foreach ($options['settings'] as $setting) {
                if (!array_key_exists($setting['category'], $categories)) {
                    $categories[$setting['category']] = [
                        'form'=>null,
                        'fields'=>[]
                    ];
                }
                $categories[$setting['category']]['fields'][] = $setting;

                if (null === $categories[$setting['category']]['form']) {
                    $categories[$setting['category']]['form'] = $this->createFormSection($builder, $setting['category']);
                }

                $fieldType = null;
                $fieldOptions = [];
                switch($setting['field']['type']) {
                    case 'text':
                        $fieldType = TextType::class;
                        break;
                    case 'textarea':
                        $fieldType = TextareaType::class;
                        $fieldOptions['attr'] = [
                            'class'=>'resize-y'
                        ];
                        break;
                    case 'boolean':
                        $fieldType = CheckboxType::class;
                        break;
                    case 'date':
                        $fieldType = DateType::class;
                        $fieldOptions['widget']='single_text';
                        break;
                }

                if (!empty($setting['field']['options'])) {
                    $fieldType = ChoiceType::class;
                    $fieldOptions['choices'] = array_column($setting['field']['options'], 'value','label');
                    if ($setting['field']['allow_multiple']) {
                        $fieldOptions['expanded'] = true;
                    }
                }

                $fieldOptions['required'] = $setting['field']['required'];

                $fieldOptions['label'] = $setting['label'];

                $categories[$setting['category']]['form']->add($setting['slug'], $fieldType, $fieldOptions);

                switch($setting['field']['type']) {
                    case 'boolean':
                        $categories[$setting['category']]['form']->get($setting['slug'])->addModelTransformer(new CallbackTransformer(
                            function ($input) {
                                if (empty($input)) {
                                    $input = false;
                                }
                                // db->form
                                return (bool)$input;
                            },
                            fn($data) =>
                                // form->db
                                (bool)$data,
                        ));
                        break;
                    case 'date':
                        $categories[$setting['category']]['form']->get($setting['slug'])->addModelTransformer(new CallbackTransformer(
                            function ($input) {
                                if (!($input instanceof \DateTimeInterface)) {
                                    return new Carbon($input);
                                }
                                return $input;
                            },
                            function ($data) {
                                if (($data instanceof \DateTimeInterface)) {
                                    $data = Carbon::instance($data);
                                }

                                if (!($data instanceof \DateTimeInterface)) {
                                    $data = new Carbon($data);
                                }
                                return $data->toDateString();
                            },
                        ));
                        break;
                }
            }

            foreach ($categories as $category) {
                $event->getForm()->add($category['form']->getForm());
            }
/*
            $event->getForm()->add('Update Settings', SubmitType::class, []);
*/
        });
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'allow_extra_fields' => true,
            'settings'=>[]
        ]);
    }

    /**
     * @param FormBuilderInterface $builder
     * @param $category
     * @return FormBuilderInterface
     */
    protected function createFormSection(FormBuilderInterface $builder, $category): FormBuilderInterface
    {
        return $builder->create(strtolower(str_replace(' ', '_', $category)), FormType::class, [
            'inherit_data' => true,
            'auto_initialize' => false,
            'label' => $category,
            'label_attr' => [
                'hidden' => true,
                'section' => true,
            ],
            'allow_extra_fields' => true
        ]);
    }
}
