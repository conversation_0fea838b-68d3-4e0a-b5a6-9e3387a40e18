<?php

namespace App\Form\Post\Options;

use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class MediaType extends PostOptionType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('mediaFiles', CollectionType::class, [
                'entry_type'=>HiddenType::class,
//                'label'=>false,
                'allow_add'=>true,
                'allow_delete'=>true,
                'prototype_name'=> '_file_name_',
                'required'=>false
            ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        parent::configureOptions($resolver);
        $resolver->setDefaults([
            'block_name' => 'media_option',
            'attr' => [
                'data-wide' => true,
                'icon' => 'fa-camera',
                'iconLabel'=>'Media',
                'allowPhotos'=>true,
                'allowVideos'=>true,
                'allowExternalMedia'=>true,
            ],
            'order' => 0
        ]);
    }


}
