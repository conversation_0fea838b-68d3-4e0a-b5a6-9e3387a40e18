<?php

namespace App\Form\Post\Options;

use App\Form\Post\Options\PostOptionType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class PostEventType extends PostOptionType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('createPost', CheckboxType::class, [
                'data'=>false,
                'label'=>'Automatically Create An Event Post About This Event',
                'required'=>false,
                'attr'=>[
                    'class'=>'mr-2'
                ]
            ])
        ;
    }

}
