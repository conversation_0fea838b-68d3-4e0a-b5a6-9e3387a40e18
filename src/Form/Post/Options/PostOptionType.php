<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 1/11/2019
 * @time: 11:56 AM
 */

namespace App\Form\Post\Options;


use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\DataMapperInterface;
use Symfony\Component\Form\Exception;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormView;
use Symfony\Component\OptionsResolver\OptionsResolver;

abstract class PostOptionType extends AbstractType implements DataMapperInterface
{
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'attr'=>[
                'data-wide'=>true,
                'icon'=>'fa-calendar-alt',
            ],
            'siblings'=>null,
            'showButton'=>true,
            'order'=>null,
        ]);
    }

    public function buildView(FormView $view, FormInterface $form, array $options)
    {
        parent::buildView($view, $form, $options);
        $view->vars['siblings'] = $options['siblings'];
        $view->vars['showButton'] = $options['showButton'];
        $view->vars['order'] = $options['order'];
    }

    public function mapDataToForms($viewData, iterable $forms)
    {
        if (null === $viewData) {
            return;
        }

        $forms = iterator_to_array($forms);

        foreach ($forms as $formName=>$form) {
            if (array_key_exists($formName, $viewData)) {
                $form->setData($viewData[$formName]);
            }
        }
    }

    public function mapFormsToData(iterable $forms, &$viewData)
    {
        $forms = iterator_to_array($forms);

        $data = array_map(fn($form) => $form->getData(), $forms);

        $viewData = $data;
    }


}
