<?php

namespace Mockingbird\Task\User;

use Exception;
use Mockingbird\Infrastructure\Task\Task;
use Mockingbird\Task\User\Friend\LoadFriendship;
use Mockingbird\ValueObjects\UserRelationship;
use Symfony\Component\Validator\Constraints;

class DetermineUserRelationship extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $users = $this->getData('users');
            if ($users[0] === $users[1]) {
                return UserRelationship::RELATIONSHIP_SELF;
            }

            $friendship = $this->container->get(LoadFriendship::class)->setData([
                'userId'=>$users[0],
                'friendId'=>$users[1],
            ])();

            if (!empty($friendship)) {
                if ($friendship['status'] == 'accepted') {
                    return UserRelationship::RELATIONSHIP_FRIEND_ACCEPTED;
                }
                if ($friendship['status'] == 'pending') {
                    return UserRelationship::RELATIONSHIP_FRIEND_PENDING;
                }
            }

            return UserRelationship::RELATIONSHIP_PUBLIC;

        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'users'=>new Constraints\Required([
                    new Constraints\Count([
                        'min'=>2,
                        'max'=>2,
                        'minMessage'=>'You must specify exactly two users to determine a relationship',
                        'maxMessage'=>'You must specify exactly two users to determine a relationship',
                    ])
                ])
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
