<?php

namespace Mockingbird\Task\Settings;

use Doctrine\DBAL\Connection;
use Exception;
use Mockingbird\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class LoadSettings extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $entityCachedSettings = [];

            $entityType = $this->getData('entityType');
            if (!is_array($entityType)) {
                $entityType = [$entityType];
            }

            $entityType = array_map(static function ($entityType) {
                return match (strtolower(trim($entityType))) {
                    'user', 'app\\domain\\user\\entity\\user' => 'App\\Domain\\User\\Entity\\User',
                    'organization', 'app\\domain\\organization\\entity\\organization' => 'App\\Domain\\Organization\\Entity\\Organization',
                    'group', 'app\\domain\\group\\entity\\group' => 'App\\Domain\\Group\\Entity\\Group',
                    'personalgroup', 'app\\domain\\group\\entity\\personalgroup' => 'App\\Domain\\Group\\Entity\\PersonalGroup',
                    'club', 'app\\domain\\club\\entity\\club' => 'App\\Domain\\Club\\Entity\\Club',
                    'app\\domain\\serviceplan\\entity\\serviceplan' => 'App\\Domain\\ServicePlan\\Entity\\ServicePlan',
                    default => throw new \InvalidArgumentException(sprintf('The entity type "%s" is not mapped', $entityType)),
                };
            }, $entityType);

            $sql = $this->connection->createQueryBuilder();
            $sql->select([
                'settings."id" as setting_id',
                'settings.title as setting_title',
                'settings.description as setting_description',
                'settings.required as setting_required',
                'settings.value_type as setting_type',
                'settings.allow_multiple_values as setting_allow_multiple_values',
                'settings.default_value as setting_default_value',
                'settings.visibility as setting_visibility',
                'settings.category as setting_category',
                'settings.slug as setting_slug',
                'settings.applicable_entity_types as setting_applicable_entity_types',
                'options."id" as options_id',
                'options.label as options_label',
                'options."value" as options_value',
                'options."order" as options_order'
            ])
            ->from('entity_settings.settings', 'settings')
            ->leftJoin('settings', 'entity_settings."options"','options','settings."id" = "options".setting_id')
            ->where('"settings".applicable_entity_types::jsonb @> '.$sql->createPositionalParameter(json_encode($entityType)).'::jsonb')
            ;

            if ($this->getData('category', false)) {
                $category = $this->getData('category');
                if (!is_array($category)) {
                    $category = [$category];
                }
                $sql->andWhere('settings.category in ('.$sql->createPositionalParameter($category, Connection::PARAM_STR_ARRAY).')');
            }

            if ($this->getData('slug', false)) {
                $slug = $this->getData('slug');
                if (!is_array($slug)) {
                    $slug = [$slug];
                }
                $sql->andWhere('settings.slug in ('.$sql->createPositionalParameter($slug, Connection::PARAM_STR_ARRAY).')');
            }

            if ($this->getData('visibility', false)) {
                $sql->andWhere('settings.visibility = '.$sql->createPositionalParameter($this->getData('visibility')));
            }

            $settings = $sql->execute()->fetchAllAssociative();
            $settingsOut = [];
            $defaults = [];
            foreach ($settings as $setting) {
                if (!array_key_exists($setting['setting_slug'], $settingsOut)) {
                    $settingsOut[$setting['setting_slug']] = [
                        'id'=>$setting['setting_id'],
                        'label'=>$setting['setting_title'],
                        'description'=>$setting['setting_description'],
                        'category'=>$setting['setting_category'],
                        'visibility'=>$setting['setting_visibility'],
                        'slug'=>$setting['setting_slug'],
                        'field'=>[
                            'type'=>$setting['setting_type'],
                            'allowMultiple'=>$setting['setting_allow_multiple_values'],
                            'default'=>$setting['setting_default_value'],
                            'required'=>$setting['setting_required'],
                            'options'=>[]
                        ]
                    ];

                    $defaults[$setting['setting_slug']] = [
                        'id'=>null,
                        'owner'=>null,
                        'isDefault'=>true,
                        'option'=> []
                    ];
                    if (isset($setting['setting_default_value'])) {
                        $defaults[$setting['setting_slug']]['value']=$setting['setting_default_value'];
                    }
                }

                if (!empty($setting['options_id'])) {
                    $settingsOut[$setting['setting_slug']]['field']['options'][$setting['options_id']] = [
                        'id'=>$setting['options_id'],
                        'label'=>$setting['options_label'],
                        'value'=>$setting['options_value'],
                        'order'=>$setting['options_order'],
                    ];

                    usort($settingsOut[$setting['setting_slug']]['field']['options'], static function ($a, $b){
                        return $a['order'] <=> $b['order'];
                    });
                }
            }

            if ($this->getData('entityId', false)) {
                $entityId = $this->getData('entityId');
                if (!is_array($entityId)) {
                    $entityId = [$entityId];
                }

                $entityId = array_combine($entityId, $entityId);
                $entityId = array_diff_key($entityId, $entityCachedSettings);

                if (count($entityId) > 0) {
                    $entitySettingsOut = array_fill_keys($entityId, $settingsOut);
                    $entityDefaults = array_fill_keys($entityId, $defaults);

                    $entityIdentifierField = '';
                    $entityTable = '';
                    switch (strtolower(trim(current($entityType)))) {
                        case 'serviceplan':
                        case 'app\\domain\\serviceplan\\entity\\serviceplan':
                            $entityTable = 'service_plan_setting_value';
                            $entityIdentifierField = 'service_plan_id';
                            break;
                        case 'user':
                        case 'app\\domain\\user\\entity\\user':
                            $entityTable = 'user_setting_value';
                            $entityIdentifierField = 'user_id';
                            break;
                        case 'organization':
                        case 'personalgroup':
                        case 'group':
                        case 'club':
                        case 'app\\domain\\organization\\entity\\organization':
                        case 'app\\domain\\group\\entity\\group':
                        case 'app\\domain\\group\\entity\\personalgroup':
                        case 'app\\domain\\club\\entity\\club':
                            $entityTable = 'organization_setting_value';
                            $entityIdentifierField = 'organization_id';
                            break;
                    }
                    $valueSql = $this->connection->createQueryBuilder();
                    $valueSql->select([
                        'eav."value" as setting_value',
                        'eav.setting_id as setting_id',
                        'eav.option_id as setting_option_id',
                        'eav_options.label setting_option_label',
                        'eav_options."value" as setting_option_value',
                        'eav."id" as setting_value_id',
                        'settings.slug as setting_slug',
                        $entityTable . '.' . $entityIdentifierField . ' as entity_id',
                    ])
                        ->from($entityTable)
                        ->innerJoin($entityTable, 'entity_settings."values"', 'eav', $entityTable . '.setting_value_id = eav."id"')
                        ->innerJoin($entityTable, 'entity_settings."settings"', 'settings', 'settings.id = eav.setting_id')
                        ->leftJoin('eav', 'entity_settings."options"', 'eav_options', 'eav_options.id = eav.option_id')
                        ->where($entityTable . '.' . $entityIdentifierField . ' in (' . $valueSql->createNamedParameter($entityId, Connection::PARAM_STR_ARRAY) . ')');

                    $values = $valueSql->execute()->fetchAllAssociative();

                    foreach ($values as $value) {
                        if (array_key_exists($value['setting_slug'], $entitySettingsOut[$value['entity_id']])) {
                            unset($entityDefaults[$value['entity_id']][$value['setting_slug']]);
                            $val = $value['setting_option_value'] ?? $value['setting_value'] ?? $entitySettingsOut[$value['entity_id']][$value['setting_slug']]['field']['default'];
                            if ($entitySettingsOut[$value['entity_id']][$value['setting_slug']]['field']['type'] == 'boolean') {
                                $val = (bool)$val;
                            }
                            $entitySettingsOut[$value['entity_id']][$value['setting_slug']]['value'] = [
                                'id' => $value['setting_value_id'],
                                'value' => $val,
                                'owner' => $value['entity_id'],
                                'isDefault' => false,
                                'option' => !empty($value['setting_option_id']) ? [
                                    'id' => $value['setting_option_id'],
                                    'label' => $value['setting_option_label'],
                                    'value' => $value['setting_option_value'],
                                ] : []
                            ];
                        }
                    }

                    foreach ($entityDefaults as $entityDefaultsId => $defaults) {
                        foreach ($defaults as $settingId => $default) {
                            $entitySettingsOut[$entityDefaultsId][$settingId]['value'] = $default;
                        }
                    }

                    $settingsOut = $entitySettingsOut;
                    if (count($entityId) === 1 && !$this->getData('retainEntityIdIndex', false)) {
                        $settingsOut = current($entitySettingsOut);
                    }

//                    try {
//                        foreach ($settingsOut as $settingsOutEntityId => $settingsOutEntityData) {
//
//                            $this->cachePool->save($this->cachePool->getItem('entity-settings-' . $settingsOutEntityId)
//                                ->set(json_encode($settingsOutEntityData, JSON_THROW_ON_ERROR))
//                                ->setTags(['entity-settings-' . $settingsOutEntityId])
//                                ->expiresAfter(strtotime('+7 days'))
//                            );
//                        }
//                    } catch (\Exception $exception) {
//                        $this->log('Failed to cache settings data: ' . $exception->getMessage());
//                    }
                }

                $settingsOut = array_merge($settingsOut, $entityCachedSettings);
            }

            return $settingsOut;
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'entityType'=>new Constraints\Required(),
                'entityId'=> new Constraints\Optional(),
                'category'=>new Constraints\Optional(),
                'slug'=>new Constraints\Optional(),
                'refreshCache' => new Constraints\Optional(),
                'ownerType'=>new Constraints\Optional(),
                'ownerId'=>new Constraints\Optional(),

                'retainEntityIdIndex'=>new Constraints\Optional()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
