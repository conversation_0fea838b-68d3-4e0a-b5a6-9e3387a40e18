<?php

namespace Mockingbird\Task\Attributes;

use Doctrine\DBAL\Connection;
use Exception;
use Mockingbird\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class LoadAttributes extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $entityType = $this->getData('entityType');
            if (!is_array($entityType)) {
                $entityType = [$entityType];
            }

            $entityType = array_map(static function ($entityType) {
                return match (strtolower(trim($entityType))) {
                    'user', 'app\\domain\\user\\entity\\user' => 'App\\Domain\\User\\Entity\\User',
                    'organization', 'app\\domain\\organization\\entity\\organization' => 'App\\Domain\\Organization\\Entity\\Organization',
                    'personalgroup', 'app\\domain\\group\\entity\\personalgroup' => 'App\\Domain\\Group\\Entity\\PersonalGroup',
                    'group', 'app\\domain\\group\\entity\\group' => 'App\\Domain\\Group\\Entity\\Group',
                    'club', 'app\\domain\\club\\entity\\club' => 'App\\Domain\\Club\\Entity\\Club',
                    default => throw new \InvalidArgumentException(sprintf('The entity type "%s" is not mapped', $entityType)),
                };
            }, $entityType);

            $sql = $this->connection->createQueryBuilder();
            $sql->select([
                'attributes."id" as attribute_id',
                'attributes.title as attribute_title',
                'attributes.description as attribute_description',
                'attributes.required as attribute_required',
                'attributes.value_type as attribute_type',
                'attributes.allow_multiple_values as attribute_allow_multiple_values',
                'attributes.default_value as attribute_default_value',
                'attributes.visibility as attribute_visibility',
                'attributes.category as attribute_category',
                'attributes.slug as attribute_slug',
                'attributes.applicable_entity_types as attribute_applicable_entity_types',
                'attributes."_discr" as attribute_discriminator',
                'options."id" as options_id',
                'options.label as options_label',
                'options."value" as options_value',
                'options."order" as options_order',
                'custom_attributes.created_by_id as attribute_created_by_id',
                'custom_attributes.updated_by_id as attribute_updated_by_id',
                'custom_attributes.deleted_at as attribute_deleted_at',
                'custom_attributes.created_at as attribute_created_at',
                'custom_attributes.updated_at as attribute_updated_at',
            ])
            ->from('entity_attributes.attributes', 'attributes')
            ->leftJoin('attributes', 'entity_attributes."options"','options','attributes."id" = "options".attribute_id')
            ->leftJoin('attributes', 'entity_attributes."custom_attributes"','custom_attributes','"attributes"."id" = custom_attributes."id"')
//            ->where('"attributes".applicable_entity_types::jsonb @> \'["App\\Domain\\User\\Entity\\User"]\'::jsonb')
            ->where('"attributes".applicable_entity_types::jsonb @> '.$sql->createPositionalParameter(json_encode($entityType)).'::jsonb')
            ;

            if ($this->getData('category', false)) {
                $category = $this->getData('category');
                if (!is_array($category)) {
                    $category = [$category];
                }
                $sql->andWhere('attributes.category in ('.$sql->createPositionalParameter($category, Connection::PARAM_STR_ARRAY).')');
            }

            if ($this->getData('slug', false)) {
                $slug = $this->getData('slug');
                if (!is_array($slug)) {
                    $slug = [$slug];
                }
                $sql->andWhere('attributes.slug in ('.$sql->createPositionalParameter($slug, Connection::PARAM_STR_ARRAY).')');
            }

            if ($this->getData('visibility', false)) {
                $visibility = $this->getData('visibility');
                if (!is_array($visibility)) {
                    $visibility = [$visibility];
                }

                $visibility = array_map('strtolower', $visibility);

                $sql->andWhere('LOWER(attributes.visibility) in ('.$sql->createPositionalParameter($visibility, Connection::PARAM_STR_ARRAY).')');
            }

            $attributes = $sql->execute()->fetchAllAssociative();
            $attributesOut = [];
            $defaults = [];
            foreach ($attributes as $attribute) {
                if (!array_key_exists($attribute['attribute_id'], $attributesOut)) {
                    $attributesOut[$attribute['attribute_id']] = [
                        'id'=>$attribute['attribute_id'],
                        'label'=>$attribute['attribute_title'],
                        'description'=>$attribute['attribute_description'],
                        'visibility'=>$attribute['attribute_visibility'],
                        'category'=>$attribute['attribute_category'],
                        'slug'=>$attribute['attribute_slug'],
                        'createdById'=>$attribute['attribute_created_by_id'],
                        'createdAt'=>$attribute['attribute_created_at'],
                        'updatedById'=>$attribute['attribute_updated_by_id'],
                        'updatedAt'=>$attribute['attribute_updated_at'],
                        'field'=>[
                            'type'=>$attribute['attribute_type'],
                            'allowMultiple'=>$attribute['attribute_allow_multiple_values'],
                            'default'=>$attribute['attribute_default_value'],
                            'required'=>$attribute['attribute_required'],
                            'options'=>[]
                        ]
                    ];

                    $defaults[$attribute['attribute_id']] = [
                        'id'=>null,
                        'value'=>null,
                        'owner'=>null,
                        'isDefault'=> true,
                        'option'=> []
                    ];

                    if (isset($attribute['attribute_default_value'])) {
                        $defaults[$attribute['attribute_id']]['value']=$attribute['attribute_default_value'];
                    }
                }

                if (!empty($attribute['options_id'])) {
                    $attributesOut[$attribute['attribute_id']]['field']['options'][$attribute['options_id']] = [
                        'id'=>$attribute['options_id'],
                        'label'=>$attribute['options_label'],
                        'value'=>$attribute['options_value'],
                        'order'=>$attribute['options_order'],
                    ];

                    usort($attributesOut[$attribute['attribute_id']]['field']['options'], static function ($a, $b){
                        return $a['order'] <=> $b['order'];
                    });
                }
            }

            if ($this->getData('entityId', false)) {
                $entityId = $this->getData('entityId');
                if (!is_array($entityId)) {
                    $entityId = [$entityId];
                }

                $entityAttributesOut = array_fill_keys($entityId, $attributesOut);
                $entityAttributesDefaults = array_fill_keys($entityId, $defaults);

                switch (strtolower(trim(current($entityType)))) {
                    case 'user':
                    case 'app\\domain\\user\\entity\\user':
                        $entityTable = 'user_attribute_value';
                        $entityIdentifierField = 'user_id';
                        break;
                    case 'organization':
                    case 'group':
                    case 'personalgroup':
                    case 'club':
                    case 'app\\domain\\organization\\entity\\organization':
                    case 'app\\domain\\group\\entity\\group':
                    case 'app\\domain\\group\\entity\\personalgroup':
                    case 'app\\domain\\club\\entity\\club':
                        $entityTable = 'organization_attribute_value';
                        $entityIdentifierField = 'organization_id';
                        break;
                    default:
                        throw new \InvalidArgumentException(sprintf('The entity type "%s" is not mapped', $entityType));
                }
                $valueSql = $this->connection->createQueryBuilder();
                $valueSql->select([
                    'eav."value" as attribute_value',
                    'eav.attribute_id as attribute_id',
                    'eav.option_id as attribute_option_id',
                    'eav_options.label attribute_option_label',
                    'eav_options."value" as attribute_option_value',
                    'eav."id" as attribute_value_id',
                    $entityTable.'.'.$entityIdentifierField.' as entity_id',
                ])
                    ->from($entityTable)
                    ->innerJoin($entityTable,'entity_attributes."values"','eav', $entityTable.'.attribute_value_id = eav."id"')
                    ->leftJoin('eav','entity_attributes."options"','eav_options', 'eav_options.id = eav.option_id')
                    ->where($entityTable.'.'.$entityIdentifierField.' in ('.$valueSql->createPositionalParameter($entityId,Connection::PARAM_STR_ARRAY).')');

                $values = $valueSql->execute()->fetchAllAssociative();
                foreach ($values as $value) {
                    if (array_key_exists($value['attribute_id'], $entityAttributesOut[$value['entity_id']])) {
                        unset($entityAttributesDefaults[$value['entity_id']][$value['attribute_id']]);
                        $entityAttributesOut[$value['entity_id']][$value['attribute_id']]['value'] = [
                            'id'=>$value['attribute_value_id'],
                            'value'=>$value['attribute_option_value']??$value['attribute_value']??$entityAttributesOut[$value['entity_id']][$value['attribute_id']]['field']['default'],
                            'owner'=>$value['entity_id'],
                            'isDefault'=>false,
                            'option'=> !empty($value['attribute_option_id'])?[
                                'id'=>$value['attribute_option_id'],
                                'label'=>$value['attribute_option_label'],
                                'value'=>$value['attribute_option_value'],
                            ]:[]
                        ];
                    }
                }
                foreach ($entityAttributesDefaults as $entityId => $entityDefaults) {
                    foreach($entityDefaults as $attributeId=>$default) {
                        $entityAttributesOut[$entityId][$attributeId]['value'] = $default;
                    }

                    $entityAttributesOut[$entityId] = array_combine(array_column($entityAttributesOut[$entityId], 'slug'), $entityAttributesOut[$entityId]);
                }

                $attributesOut = $entityAttributesOut;
                if (count($attributesOut) === 1 && !$this->getData('retainEntityIdIndex', false)) {
                    $attributesOut = current($attributesOut);
                }
            }

            return $attributesOut;
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'entityType'=>new Constraints\Required(),
                'entityId'=> new Constraints\Optional(),
                'category'=>new Constraints\Optional(),
                'slug'=>new Constraints\Optional(),
                'visibility'=>new Constraints\Optional(),

                'ownerType'=>new Constraints\Optional(),
                'ownerId'=>new Constraints\Optional(),

                'retainEntityIdIndex'=>new Constraints\Optional()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
