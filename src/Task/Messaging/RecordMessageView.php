<?php
namespace Mockingbird\Task\Messaging;

use Doctrine\DBAL\ParameterType;
use Exception;
use Mockingbird\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class RecordMessageView extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $participantId = $this->getData('participantId');

            if (!is_array($participantId)) {
                $participantId = [$participantId];
            }

            $sql = 'INSERT INTO messaging.message_views (message_id, participant_id, created_at) VALUES (?,?,NOW()) ON CONFLICT DO NOTHING';

            foreach ($participantId as $participant) {
                $this->connection->executeStatement($sql, [
                    $this->getData('messageId'),
                    $participant
                ],[
                    ParameterType::STRING,
                    ParameterType::STRING,
                ]);
            }

            return;
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'messageId'=>new Constraints\Required(),
                'participantId'=>new Constraints\Required(),
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
