<?php

namespace Mockingbird\Task\Messaging;

use Exception;
use Mockingbird\Infrastructure\Task\Task;
use Mockingbird\Task\User\LoadUser;
use Mockingbird\ValueObjects\UserRelationship;
use Ramsey\Uuid\Uuid;
use Symfony\Component\Validator\Constraints;

class StoreMessage extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $insert = $this->connection->createQueryBuilder();
            $id = (string)Uuid::uuid4();
            $insert->insert('messaging.messages')
                ->setValue('id',$insert->createNamedParameter($id))
                ->setValue('channel_id', $insert->createNamedParameter($this->getData('channelId')))
                ->setValue('message', $insert->createNamedParameter($this->getData('message')))
                ->setValue('created_by_id', $insert->createNamedParameter($this->getData('userId')))
                ->setValue('created_at', 'now()')
                ->setValue('updated_by_id', $insert->createNamedParameter($this->getData('userId')))
                ->setValue('updated_at', 'now()')
                ->executeQuery();

            $user = $this->container->get(LoadUser::class)->setData(['userId'=> $this->getData('userId'), 'forceRelationship'=>UserRelationship::RELATIONSHIP_SELF])();
            return [
                'id' => $id,
                'channel_id' => $this->getData('channelId'),
                'created_by' => $user,
                'updated_by' => $user,
                'message' => $this->getData('message'),
                'created_at' => date(DATE_ATOM),
                'timestamp' => time(),
                'updated_at' => date(DATE_ATOM),
            ];
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'channelId'=>new Constraints\Required(),
                'userId'=>new Constraints\Required(),
                'message'=>new Constraints\Required(),
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
