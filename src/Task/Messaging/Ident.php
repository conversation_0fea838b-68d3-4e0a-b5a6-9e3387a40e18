<?php

namespace Mockingbird\Task\Messaging;

use Exception;
use <PERSON>cking<PERSON>\Infrastructure\Task\Task;
use <PERSON><PERSON><PERSON>\Listener\Messenger\Connection\Connection;
use <PERSON>ckingbird\Listener\Messenger\Connection\ConnectionManager;
use Symfony\Component\Validator\Constraints;

class Ident extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            /** @var Connection $connection */
            $connection = $this->getRequest()->getConnection();

            if ($connection->getId() === $this->getData('ident')) {
                $connection->setUserId($this->getData('userId'));
            }

            $packet = json_encode([
                'type' => 'ack:ident',
            ], JSON_THROW_ON_ERROR);

            $this->getRequest()->getConnection()->send($packet);
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'ident'=>new Constraints\Required(),
                'userId'=>new Constraints\Required(),
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
