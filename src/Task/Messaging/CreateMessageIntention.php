<?php

namespace Mocking<PERSON>\Task\Messaging;

use Exception;
use Mockingbird\Infrastructure\Task\Task;
use <PERSON>ckingbird\Listener\Messenger\Channel\ChannelManager;
use Mockingbird\Task\User\LoadUser;
use React\Promise\Promise;
use Symfony\Component\Validator\Constraints;
use function React\Promise\reject;

class CreateMessageIntention extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $channelId = $this->getData('channelId');
            (new Promise(fn($resolver) => $resolver()))
                ->then(function () use ($channelId) {
                    $intentionId = $this->getRequest()->getConnection()->getUserId();
                    $channel = ChannelManager::GetChannelById($channelId);
                    if ($channel->hasDeferredAction('intention-'.$intentionId)) {
                        $channel->removeDeferredAction('intention-'.$intentionId)
                                ->addDeferredAction('intention-'.$intentionId, $this->getData('timeout'), function () use ($intentionId, $channelId) {
                                    $this->container->get(TimeoutMessageIntention::class)->setRequest($this->getRequest())->setData([
                                        'intentionId'=>$intentionId,
                                        'channelId'=>$channelId
                                    ])();
                                });
                        return reject();
                    }
                })
                ->then($this->container->get(LoadUser::class)->setData([
                    'userId'=>$this->getRequest()->getConnection()->getUserId()
                ]))
                ->then(function ($user) use ($channelId) {
                    $intentionId = $user['id'];
                    ChannelManager::GetChannelById($channelId)
                        ->broadcast([
                            'type' => 'message:intention',
                            'data' => [
                                'intention'=>[
                                    'id'=> $intentionId,
                                ],
                                'created_by'=>$user
                            ]
                        ], $this->request->getConnection(), false)
                        ->addDeferredAction('intention-'.$intentionId, $this->getData('timeout'), function () use ($intentionId, $channelId) {
                            $this->container->get(TimeoutMessageIntention::class)->setRequest($this->getRequest())->setData([
                                'intentionId'=>$intentionId,
                                'channelId'=>$channelId
                            ])();
                        });

                    return $intentionId;
                });
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'channelId'=>new Constraints\Required(),
                'timeout'=>new Constraints\Optional()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }

    protected function defineDataDefaults(): array
    {
        return [
            'timeout'=>10
        ];
    }

}
