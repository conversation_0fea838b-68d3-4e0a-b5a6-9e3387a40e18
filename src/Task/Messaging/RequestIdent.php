<?php

namespace Mockingbird\Task\Messaging;

use Exception;
use Mockingbird\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class RequestIdent extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $connection = $this->getRequest()->getConnection();
            $packet = json_encode([
                'type' => 'ident',
                'data' => [
                    'id'=>$connection->getId()
                ]
            ], JSON_THROW_ON_ERROR);

            $this->getRequest()->getConnection()->send($packet);
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [

            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
