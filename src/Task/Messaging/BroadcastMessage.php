<?php

namespace Mocking<PERSON>\Task\Messaging;

use Exception;
use <PERSON>ckingbird\Infrastructure\Task\Task;
use <PERSON>cking<PERSON>\Listener\Http\RoutableTaskInterface;
use <PERSON><PERSON><PERSON>\Listener\Messenger\Channel\ChannelManager;
use <PERSON><PERSON>bird\Listener\Messenger\Connection\Connection;
use React\Promise\Promise;
use Symfony\Component\Validator\Constraints;

class BroadcastMessage extends Task implements RoutableTaskInterface
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            (new Promise(function($resolver){ $resolver(); }))
                ->then(function () {
                    $message = trim($this->getData('message'));
                    if ($this->getData('encode', true)) {
                        $message = htmlspecialchars($message);
                    }
                    return [
                        'channel_id' => $this->getData('channelId'),
                        'message' => $message,
                        'created_at' => date(DATE_ATOM),
                        'timestamp' => time(),
                        'updated_at' => date(DATE_ATOM),
                    ];
                })
                ->then(function ($message) {
                    ChannelManager::GetChannelById($this->getData('channelId'))
                                ->broadcast([
                                    'type' => $this->getData('messageType','message:created'),
                                    'data' => $message
                                ]);
                    return $message;
                });
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'channelId'=>new Constraints\Required(),
                'message'=>new Constraints\Required(),
                'messageType'=>new Constraints\Optional(),
                'encode'=>new Constraints\Optional(),
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }

    public function getRoutableName(): string
    {
        return 'messaging/broadcast-message';
    }
}
