<?php

namespace Mocking<PERSON>\Task\Messaging;

use Carbon\Carbon;
use Exception;
use Mockingbird\Infrastructure\Task\Task;
use <PERSON>ckingbird\Listener\Http\RoutableTaskInterface;
use <PERSON><PERSON><PERSON>\Listener\Messenger\Channel\ChannelManager;
use <PERSON>ckingbird\Listener\Messenger\Connection\Connection;
use <PERSON>ckingbird\Mockingbird;
use Mockingbird\Task\Messaging\Channel\Participant\ReengageParticipant;
use MongoDB\Client;
use React\Promise\Promise;
use Symfony\Component\Validator\Constraints;

class CreateMessage extends Task implements RoutableTaskInterface
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            (new Promise(function($resolver){ $resolver(); }))
                ->then(function () {
                    return htmlspecialchars(trim($this->getData('message')));
                })
                ->then($this->container->get(StoreMessage::class)->setData(
                    array_merge($this->getData(), ['userId'=>$this->getRequest()->getConnection()->getUserId()])
                ))
                ->then(function ($message) {
                    $this->container->get(ReengageParticipant::class)->setData($message)();
                    return $message;
                })
                ->then(function ($message) {
                    $this->container->get(TimeoutMessageIntention::class)->setRequest($this->getRequest())->setData([
                        'intentionId' => $message['created_by']['id'],
                        'channelId'=>$this->getData('channelId')
                    ])();
                    return $message;
                })
                ->then(function ($message) {
                    ChannelManager::GetChannelById($this->getData('channelId'))
                                ->broadcast([
                                    'type' => 'message:created',
                                    'data' => $message
                                ]);
                    return $message;
                })
                ->then(function ($message) {
                    /** @var Connection[] $connections */
                    $connections = ChannelManager::GetChannelById($this->getData('channelId'))->getConnections();
                    $participantIds = [];
                    foreach ($connections as $connection) {
                        if ($connection->hasIdent()) {
                            $participantIds[] = $connection->getUserId();
                        }
                    }

                    $this->container->get(RecordMessageView::class)->setData([
                        'participantId'=>$participantIds,
                        'messageId'=>$message['id']
                    ])();
                    return $message;
                })->then(function($message) {
                    $this->logger->writeLog('Pushing to Mongo');
                    /** @var Client $mongo */
                    $mongo = $this->container->get(Client::class);
                    $mongo->selectCollection($_ENV['MONGO_DB_NAME'], 'message_queue')->insertOne([
                        'message_id' => $message['id'],
                        'queue'=>'messaging',
                        'seen'=>false,
                        'execute_time' => (new Carbon())->getTimestamp()
                    ]);
                });
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'channelId'=>new Constraints\Required(),
                'userId'=>new Constraints\Required(),
                'message'=>new Constraints\Required(),
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }

    public function getRoutableName(): string
    {
        return 'messaging/create-message';
    }
}
