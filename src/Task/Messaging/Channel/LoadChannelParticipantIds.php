<?php

namespace Mockingbird\Task\Messaging\Channel;

use Doctrine\DBAL\Connection;
use Exception;
use Mockingbird\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class LoadChannelParticipantIds extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $channelId = $this->getData('channelId');

            if (!is_array($channelId)){
                $channelId = [$channelId];
            }

            $loadSql = $this->connection->createQueryBuilder();
            $loadSql->select([
                        "channels.id as key",
                        "channels.id",
                        "channels.created_by_id",
                        "channels.updated_by_id",
                        "channels.deleted_at",
                        "channels.created_at",
                        "channels.updated_at",
                        "channels.name",
                        "channels.type",
                        "channels.status",
                    ])
                    ->from('messaging.participants')
                    ->where('channels.id in ('.$loadSql->createNamedParameter($channelId, Connection::PARAM_STR_ARRAY).')');

            $results = $loadSql->executeQuery()->fetchAllAssociativeIndexed();

            if (!$this->getData('forceArrayResults', false) && count($results) === 1) {
                $results = current($results);
            }

            return $results;
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'channelId'=>new Constraints\Required(),
                'forceArrayResult'=>new Constraints\Optional(),
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
