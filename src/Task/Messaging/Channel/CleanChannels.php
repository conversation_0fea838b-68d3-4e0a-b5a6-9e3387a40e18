<?php

namespace Mockingbird\Task\Messaging\Channel;

use <PERSON><PERSON>\Adapter\Common\CacheItem;
use C<PERSON>\Adapter\Predis\PredisCachePool;
use Exception;
use Mockingbird\Infrastructure\Task\Task;
use React\Promise\Promise;
use Symfony\Component\Validator\Constraints;

class CleanChannels extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $promise = new Promise(fn ($resolver) => $resolver());

            $promise->then(function () {
                $channelSql = $this->connection->createQueryBuilder()
                                                ->select(['id'])
                                                ->from('messaging.channels', 'channel');

                return $channelSql->fetchAllAssociative();
            })
            ->then(function($channels) {
                $channels = array_column($channels, 'id');
                $channels = array_combine($channels, $channels);
                return array_map(fn ($channel) => 'channel-'.$channel, $channels);
            })
            ->then(function ($channelKeys) {
                /** @var PredisCachePool $cache */
                $cache = $this->container->get(PredisCachePool::class);
                /** @var CacheItem[] $channels */
                $channels = $cache->getItems($channelKeys);
                foreach ($channels as $channelId => $channel) {
                    $channelData = $channel->get();
                    $this->log(sprintf("channel %s has the following data: %s", $channelId, json_encode($channelData)));
                }
            });

        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [

            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
