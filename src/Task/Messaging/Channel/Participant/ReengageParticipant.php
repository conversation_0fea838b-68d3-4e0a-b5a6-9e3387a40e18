<?php

namespace Mockingbird\Task\Messaging\Channel\Participant;

use Exception;
use Mockingbird\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class ReengageParticipant extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $participantSql = $this->connection->createQueryBuilder();
            $participantSql->update('messaging.participants')
                            ->set('status', 'null')
                            ->where('participants.channel_id = '.$participantSql->createNamedParameter($this->getData('channelId')))
                            ->andWhere('participants.status = '.$participantSql->createNamedParameter('left'))
                            ->executeQuery();

            return $this->getData();
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'channelId'=>new Constraints\Required(),
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
