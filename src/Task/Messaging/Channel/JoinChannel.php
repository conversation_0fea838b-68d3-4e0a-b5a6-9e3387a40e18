<?php

namespace Mocking<PERSON>\Task\Messaging\Channel;

use Exception;
use <PERSON><PERSON>bird\Infrastructure\Task\Task;
use <PERSON><PERSON><PERSON>\Listener\Messenger\Channel\ChannelManager;
use <PERSON><PERSON><PERSON>\Listener\Messenger\Request;
use Symfony\Component\Validator\Constraints;

class JoinChannel extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            if ($this->request instanceof Request) {
                $channelId = $this->getData('channelId');

                $channel = ChannelManager::CreateChannel($channelId);
                $connection = $this->getRequest()->getConnection();

                $connection->addChannel($channel);
                $channel->addConnection($connection);

                if ($this->getData('announceJoin', false)) {
                    $channel->broadcast([
                            'channelId'=>$channelId,
                            'type'=>'announcement',
                            'data'=>'new channel participant'
                        ], $connection, false);
                }

                $connection->send(json_encode([
                    'type'=>'channel:joined',
                    'data'=>[
                        'channel'=>$channelId
                    ]
                ], JSO<PERSON>_THROW_ON_ERROR));
            }

//            if ($this->request instanceof Request) {
//                // check if user is a participant
//                $connectionId = $this->request->getConnection()->resourceId;
//                $channelId = $this->getData('channelId');
//
//                /** @var PredisCachePool $cache */
//                $cache = $this->container->get(PredisCachePool::class);
//                $channel = $cache->getItem('channel-' . $channelId);
//                if (!$channel->isHit()) {
//                    $channel->set([]);
//                }
//                $channelParticipants = $channel->get();
//                if (empty($channelParticipants)) {
//                    $channelParticipants = [];
//                }
//
//                $connection = $cache->getItem('connection-' . $connectionId);
//                if (!$connection->isHit()) {
//                    $connection->set([]);
//                }
//                $connectionChannels = $connection->get();
//                if (empty($connectionChannels)) {
//                    $connectionChannels = [];
//                }
//
//                if (!in_array($connectionId, $channelParticipants, true)) {
//                    $channelParticipants[$connectionId]=$connectionId;
//
//                    if ($this->getData('announceJoin', false)) {
//                        $this->container->get(BroadcastData::class)
//                            ->setRequest($this->getRequest())
//                            ->setData([
//                            'channelId'=>$channelId,
//                            'type'=>'announcement',
//                            'data'=>'new channel participant'
//                        ])();
//                    }
//                }
//                if (!in_array($channelId, $connectionChannels, true)) {
//                    $connectionChannels[$channelId]= $channelId;
//                }
//
//                $channel->set($channelParticipants);
//                $connection->set($connectionChannels);
//
//                $cache->save($channel);
//                $cache->save($connection);
//
//                $this->getRequest()->getConnection()->send(json_encode([
//                    'type'=>'channel:joined',
//                    'data'=>[
//                        'channel'=>$channelId
//                    ]
//                ], JSON_THROW_ON_ERROR));
//            }
            // connect the current connection to the channel (maybe in redis?)
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'channelId'=>new Constraints\Required(),
                'userId'=>new Constraints\Required(),
                'announceJoin' => new Constraints\Optional()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
