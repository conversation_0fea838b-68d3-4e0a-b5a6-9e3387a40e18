<?php

namespace Mockingbird\Task\Messaging\Channel;

use Carbon\Carbon;
use Exception;
use Mockingbird\Infrastructure\Task\Task;
use Mockingbird\Task\Messaging\RecordMessageView;
use Mockingbird\Task\User\LoadUser;
use Mockingbird\ValueObjects\UserRelationship;
use React\Promise\Promise;
use Symfony\Component\Validator\Constraints;
use function React\Promise\map;

class LoadMessages extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $promise = new Promise(fn($resolver) => $resolver());
            $promise->then(function(){
                $select = $this->connection->createQueryBuilder();
                $select->select([
                        'messages.id as key',
                        'messages.id',
                        'messages.channel_id',
                        'messages.created_by_id',
                        'messages.updated_by_id',
                        'messages.message',
                        'messages.deleted_at',
                        'messages.created_at',
                        'messages.updated_at',
                    ])
                    ->from('messaging.messages')
                    ->where('messages.channel_id = '.$select->createNamedParameter($this->getData('channelId')))
                    ->orderBy('messages.created_at', 'DESC')
                    ->setMaxResults($this->getData('limit', 20));

                if ($this->getData('before', false)){
                    $select->andWhere('extract(epoch from messages.created_at) < '.$select->createNamedParameter($this->getData('before')));
                }else{
                    $select->setFirstResult($this->getData('offset', 0));
                }

                return $select->executeQuery()->fetchAllAssociativeIndexed();
            })
            ->then(function ($messages) {
                usort($messages, fn($a, $b) => $a['created_at']<=>$b['created_at']);
                return $messages;
            })
            ->then(function ($messages) {
                $loadUser = $this->container->get(LoadUser::class);
                return map($messages, function (array $message) use ($loadUser): array {
                    $message['created_by'] = $loadUser->setData(['userId'=>$message['created_by_id'], 'forceRelationship'=>UserRelationship::RELATIONSHIP_SELF])();
                    $message['updated_by'] = $loadUser->setData(['userId'=>$message['updated_by_id'], 'forceRelationship'=>UserRelationship::RELATIONSHIP_SELF])();
                    $message['updated_at'] = Carbon::createFromTimeString($message['updated_at'], 'UTC')->format('c');
                    $message['created_at'] = Carbon::createFromTimeString($message['created_at'], 'UTC')->format('c');
                    $message['timestamp'] = Carbon::createFromTimeString($message['created_at'], 'UTC')->timestamp;
                    unset($message['created_by_id'], $message['updated_by_id']);
                    return $message;
                });
            })
            ->then(function ($messages) {
                $request = $this->getRequest();
                $payload = [
                    'type'=>'message:loaded',
                    'data'=>array_values($messages)
                ];
                $request->getConnection()->send(json_encode($payload, JSON_THROW_ON_ERROR));
                return $messages;
            })
            ->then(function ($messages) {
                foreach ($messages as $message) {
                    $this->container->get(RecordMessageView::class)->setData([
                        'participantId' => $this->getRequest()->getConnection()->getUserId(),
                        'messageId' => $message['id']
                    ])();
                }
            })->otherwise(function (Exception $exception) {
                throw $exception;
            });
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'channelId'=>new Constraints\Required(),
                'reverseDirection'=>new Constraints\Optional(),
                'limit'=>new Constraints\Optional(),
                'offset'=>new Constraints\Optional(),
                'before'=>new Constraints\Optional()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
