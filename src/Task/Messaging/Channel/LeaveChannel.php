<?php

namespace Mockingbird\Task\Messaging\Channel;

use C<PERSON>\Adapter\Predis\PredisCachePool;
use Exception;
use <PERSON>ckingbird\Infrastructure\Task\Task;
use <PERSON>ckingbird\Listener\Messenger\Channel\ChannelManager;
use Mockingbird\Listener\Messenger\Connection\ConnectionManager;
use Symfony\Component\Validator\Constraints;
use function React\Promise\map;

class LeaveChannel extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $channelId = $this->getData('channelId', null);
            $connection = $this->getRequest()->getConnection();
            if (null !== $channelId) {
                $channels = [ChannelManager::GetChannelById($channelId)];
            }else{
                $channels = $connection->getChannels();
            }

            map($channels, function ($channel) use ($connection) {
                $connection->removeChannel($channel);
                $channel->removeConnection($connection);
            });
//            $connectionId = $this->request->getConnection()->resourceId;
//            /** @var PredisCachePool $cache */
//            $cache = $this->container->get(PredisCachePool::class);
//
//            $connection = $cache->getItem('connection-' . $connectionId);
//            if (!$connection->isHit()) {
//                $connection->set([]);
//            }
//            $connectionChannels = $connection->get();
//            if (empty($connectionChannels)) {
//                $connectionChannels = [];
//            }
//            if (!$this->getData('channelId', false)) {
//                $channelKeys = array_map(static fn ($channel) => 'connection-'.$channel, $connectionChannels);
//                $channels = $cache->getItems($channelKeys);
//                foreach ($channels as $channel) {
//                    if ($channel->isHit()) {
//                        $channelData = $channel->get();
//                        unset($channelData[$connectionId]);
//                        $channel->set($channelData);
//                        $cache->saveDeferred($channel);
//                    }
//                }
//
//                $connection->set([]);
//                $cache->saveDeferred($connection);
//            }
//
//            $cache->commit();
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'channelId'=>new Constraints\Optional(),
                'userId'=>new Constraints\Optional()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
