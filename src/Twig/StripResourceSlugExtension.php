<?php

namespace App\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;
use Twig\TwigFunction;

class StripResourceSlugExtension extends AbstractExtension
{
    public function getFunctions(): array
    {
        return [
            new TwigFunction('strip_resource_slug', [$this,'stripSlug']),
        ];
    }

    function stripSlug($str = '')
    {
        return preg_replace('/\/resources\/.+/','/resources', $str??'');
    }
}
