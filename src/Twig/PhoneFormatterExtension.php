<?php

namespace App\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;
use Twig\TwigFunction;

class PhoneFormatterExtension extends AbstractExtension
{
    public function getFunctions(): array
    {
        return [
            new TwigFunction('format_phone', [$this,'formatPhoneNumberToE164']),
        ];
    }

    function formatPhoneNumberToE164($phone, $humanReadable=true)
    {
        $cleanNumber = preg_replace('/\D/', '', $phone);
        if (strlen($cleanNumber) !== 10) {
            return $phone;
//            throw new \InvalidArgumentException(sprintf('Invalid phone number "%s"', $phone));
        }

        $e164Number = '+1'.$cleanNumber;
        if (!$humanReadable) {
            return $e164Number;
        }

        return preg_replace('/(\+1)? ?\(?([\d]{3})\)?[-. ]?([\d]{3})[-. ]?([\d]{4})\b/', '$1 ($2) $3-$4', $e164Number);
    }
}
