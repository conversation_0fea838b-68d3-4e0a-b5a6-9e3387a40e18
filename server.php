<?php
declare(strict_types=1);
date_default_timezone_set('UTC');
require_once 'vendor/autoload.php';

use <PERSON>ckingbird\Infrastructure\Configuration\Configuration;
use <PERSON>ckingbird\Listener\Http;
use <PERSON>cking<PERSON>\Listener\Messenger;
use Mockingbird\Mockingbird;
use Rollbar\Rollbar;
use Symfony\Component\Dotenv\Dotenv;

try {
    $envFile = __DIR__.'/.env';
    if (file_exists($envFile)) {
        $dotEnv = new Dotenv();
        $dotEnv->load($envFile);
    }

    $config = new Configuration([
        'listeners' => [
            'messenger' => [
                'listener' => Messenger::class,
                'uri' => '0.0.0.0:6625'
            ],
            'http' => [
                'listener' => Http::class,
                'uri' => '0.0.0.0:80'
            ],
        ],

    ]);

    $mockingBird = new Mockingbird($config);

    $mockingBird->run();
}catch (\Throwable $e) {
    echo $e->getMessage();
}
