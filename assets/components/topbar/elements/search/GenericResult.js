import React from 'react';

export default class GenericResult extends React.Component
{
    render() {
        let avatar = <div style={{width: '32px', height:'32px'}}></div>

        console.log(this.props.avatar.uri.length);
        if (this.props.avatar.uri.length > 0) {
            avatar = <img src={ this.props.avatar.uri } alt={ this.props.avatar.name } width="32" height="32" />
        }

        return (
            <a className="result-link" href={this.props.url}>
                <div className="row search-result search-result-type-generic" id={"result-"+this.props.id}>
                    <div className="avatar search-result-avatar">
                        {avatar}
                    </div>
                    <div className="search-result-title stretch">{this.props.name}</div>
                </div>
            </a>);

    }
}
