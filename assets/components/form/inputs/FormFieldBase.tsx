import * as React from 'react';
import { FormFieldData } from './BaseFormField';

export interface FormFieldBaseProps {
  field: FormFieldData;
  value?: any;
  attributes?: Record<string, any>;
  onChange?: (value: any) => void;
  onMultipleValuesChange?: (index: number, value: any) => void;
  onAddMultipleValue?: () => void;
  onRemoveMultipleValue?: (index: number) => void;
}

export interface FormFieldComponentProps extends FormFieldBaseProps {
  renderInput: (value: any, index?: number) => React.ReactNode;
  hasCustomLayout?: boolean;
}

const FormFieldBase: React.FC<FormFieldComponentProps> = ({
  field,
  value,
  attributes = {},
  onChange,
  onMultipleValuesChange,
  onAddMultipleValue,
  onRemoveMultipleValue,
  renderInput,
  hasCustomLayout = false
}) => {
  const isMultiple = field.constraints.allow_multiple;
  const values = isMultiple ? (Array.isArray(value) ? value : []) : value;
  const minCount = field.constraints.min_count || 0;
  
  // Initialize with minimum number of values if needed
  React.useEffect(() => {
    if (isMultiple && (!values || values.length < minCount) && onAddMultipleValue) {
      const valuesToAdd = minCount - (values?.length || 0);
      for (let i = 0; i < valuesToAdd; i++) {
        onAddMultipleValue();
      }
    }
  }, [isMultiple, minCount, values, onAddMultipleValue]);

  // If the component has custom layout (like checkbox), render it directly
  if (hasCustomLayout) {
    return <>{renderInput(value)}</>;
  }

  // For multiple values
  if (isMultiple) {
    return (
      <div className="frm-row">
        <div className="input-row">
          <div className="input-row-label">
            <label className="form-label">
              {field.label}
              {field.required && <span className="text-red-800"> *</span>}
            </label>
          </div>
          <div className="input-row-input">
            {values.map((val: any, index: number) => (
              <div key={index} className="multiple-field-row" style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '8px', 
                marginBottom: '8px' 
              }}>
                <div style={{ flex: 1 }}>
                  {renderInput(val, index)}
                </div>
                {onRemoveMultipleValue && values.length > minCount && (
                  <button 
                    type="button" 
                    className="btn btn-sm btn-outline-danger" 
                    onClick={() => onRemoveMultipleValue(index)}
                    style={{ whiteSpace: 'nowrap' }}
                  >
                    Remove
                  </button>
                )}
              </div>
            ))}
            
            {onAddMultipleValue && (!field.constraints.max_count || values.length < field.constraints.max_count) && (
              <button 
                type="button" 
                className="btn btn-sm btn-outline-primary" 
                onClick={onAddMultipleValue}
                style={{ marginTop: '8px' }}
              >
                Add Another
              </button>
            )}
            
            {field.help_text && <small className="form-text text-muted">{field.help_text}</small>}
          </div>
        </div>
      </div>
    );
  }

  // Standard field layout
  return (
    <div className="frm-row">
      <div className="input-row">
        <div className="input-row-label">
          <label htmlFor={field.id} className="form-label">
            {field.label}
            {field.required && <span className="text-red-800"> *</span>}
          </label>
        </div>
        <div className="input-row-input">
          <div>{renderInput(value)}</div>
          {field.help_text && <small className="form-text text-muted">{field.help_text}</small>}
        </div>
      </div>
    </div>
  );
};

export default FormFieldBase;
