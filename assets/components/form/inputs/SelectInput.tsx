import * as React from 'react';
import Base<PERSON><PERSON><PERSON>ield, { BaseFormFieldProps } from './BaseFormField';

class SelectInput extends BaseForm<PERSON>ield<string> {
  private getSelectOptions() {
    return this.props.field.options.length > 0
        ? this.props.field.options.filter(option => option.active).sort((a, b) => a.ordinal - b.ordinal)
        : [
          { id: 'true', label: 'Yes', value: 'true', active: true, ordinal: 0 },
          { id: 'false', label: 'No', value: 'false', active: true, ordinal: 1 }
        ];
  }

  renderSingleField(value: string, index?: number): React.ReactNode {
    const selectOptions = this.getSelectOptions();

    return (
        <select
            id={this.getFieldId(index)}
            value={value || ''}
            onChange={(e) => this.handleChange(e.target.value, index)}
            required={this.props.field.required}
            className="form-select"
            disabled={this.attributes.disabled}
        >
          <option value="">Select an option</option>
          {selectOptions.map(option => (
              <option key={option.id} value={option.value}>
                {option.label}
              </option>
          ))}
        </select>
    );
  }
}

export default SelectInput;
