import * as React from 'react';
import BaseForm<PERSON>ield, { BaseFormFieldProps } from './BaseFormField';

class TimeInput extends BaseFormField<string> {
  renderSingleField(value: string, index?: number): React.ReactNode {
    return (
        <input
            type="time"
            id={this.getFieldId(index)}
            value={value || ''}
            onChange={(e) => this.handleChange(e.target.value, index)}
            required={this.props.field.required}
            className="form-control"
            disabled={this.attributes.disabled}
            readOnly={this.attributes.readonly}
        />
    );
  }
}

export default TimeInput;
