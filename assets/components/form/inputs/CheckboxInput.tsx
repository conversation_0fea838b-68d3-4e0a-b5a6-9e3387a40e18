import * as React from 'react';
import BaseF<PERSON><PERSON>ield, { BaseFormFieldProps } from './BaseFormField';

class CheckboxInput extends BaseFormField<boolean> {
  renderSingleField(value: boolean, index?: number): React.ReactNode {
    return (
        <>
          <input
              type="checkbox"
              id={this.getFieldId(index)}
              checked={value || false}
              onChange={(e) => this.handleChange(e.target.checked, index)}
              required={this.props.field.required}
              className="form-check-input mr-2"
              disabled={this.attributes.disabled}
          />
          <label
              htmlFor={this.getFieldId(index)}
              className="form-label"
          >
            {this.props.field.label}
            {this.renderRequiredIndicator()}
          </label>
        </>
    );
  }

  // Override render for special checkbox layout
  render(): React.ReactNode {
    if (this.isMultiple) {
      return this.renderMultipleLayout();
    }

    // Special case for single checkbox - label comes after input
    return (
        <div className="frm-row">
          <div className="input-row">
            <div className="input-row-label"></div>
            <div className="input-row-input">
              <div>{this.renderSingleField(this.props.value)}</div>
              {this.renderHelpText()}
            </div>
          </div>
        </div>
    );
  }
}

export default CheckboxInput;
