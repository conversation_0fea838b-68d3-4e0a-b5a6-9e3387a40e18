import * as React from 'react';
import {
  BaseFormFieldProps,
  TextInput,
  TextareaInput,
  CheckboxInput,
  RadioInput,
  SelectInput,
  DateInput,
  TimeInput,
  EmailInput,
  NumberInput,
  PasswordInput,
  UrlInput,
  FileInput
} from './index';

interface FormFieldFactoryProps extends BaseFormFieldProps {
  // Additional props can be added here if needed
}

const FormFieldFactory: React.FC<FormFieldFactoryProps> = (props) => {
  const { field } = props;

  switch (field.type) {
    case 'text':
      return <TextInput {...props} />;

    case 'textarea':
      return <TextareaInput {...props} />;

    case 'checkbox':
      return <CheckboxInput {...props} />;

    case 'radio':
      return <RadioInput {...props} />;

    case 'select':
      return <SelectInput {...props} />;

    case 'date':
      return <DateInput {...props} />;

    case 'time':
      return <TimeInput {...props} />;

    case 'email':
      return <EmailInput {...props} />;

    case 'number':
      return <NumberInput {...props} />;

    case 'password':
      return <PasswordInput {...props} />;

    case 'url':
      return <UrlInput {...props} />;

    case 'file':
      return <FileInput {...props} />;

    default:
      return (
          <div className="frm-row">
            <div className="input-row">
              <div className="input-row-label">
                <label className="form-label">
                  {field.label}
                  {field.required && <span className="text-red-800"> *</span>}
                </label>
              </div>
              <div className="input-row-input">
                <div className="alert alert-warning">
                  Unsupported field type: {field.type}
                </div>
                {field.help_text && (
                    <small className="form-text text-muted">{field.help_text}</small>
                )}
              </div>
            </div>
          </div>
      );
  }
};

export default FormFieldFactory;
