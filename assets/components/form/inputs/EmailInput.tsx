import * as React from 'react';
import BaseForm<PERSON>ield, { BaseFormFieldProps } from './BaseFormField';

class EmailInput extends BaseFormField<string> {
  renderSingleField(value: string, index?: number): React.ReactNode {
    return (
        <input
            type="email"
            id={this.getFieldId(index)}
            value={value || ''}
            onChange={(e) => this.handleChange(e.target.value, index)}
            required={this.props.field.required}
            className="form-control"
            minLength={this.props.field.constraints.min_length || undefined}
            maxLength={this.props.field.constraints.max_length || undefined}
            disabled={this.attributes.disabled}
            readOnly={this.attributes.readonly}
            placeholder={this.props.field.help_text || 'Enter email address'}
        />
    );
  }
}

export default EmailInput;
