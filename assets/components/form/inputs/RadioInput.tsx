import * as React from 'react';
import <PERSON>Form<PERSON>ield, { BaseFormFieldProps } from './BaseFormField';

class RadioInput extends BaseForm<PERSON>ield<string> {
  private getRadioOptions() {
    return this.props.field.options.length > 0
        ? this.props.field.options.filter(option => option.active).sort((a, b) => a.ordinal - b.ordinal)
        : [
          { id: 'true', label: 'Yes', value: 'true', active: true, ordinal: 0 },
          { id: 'false', label: 'No', value: 'false', active: true, ordinal: 1 }
        ];
  }

  renderSingleField(value: string, index?: number): React.ReactNode {
    const radioOptions = this.getRadioOptions();
    const fieldName = this.isMultiple && index !== undefined ? `${this.fieldId}_${index}` : this.fieldId;

    return (
        <div className="radio-group">
          {radioOptions.map(option => (
              <div key={option.id} className="radio-option">
                <input
                    type="radio"
                    id={`${fieldName}_${option.id}`}
                    name={fieldName}
                    value={option.value}
                    checked={value === option.value}
                    onChange={() => this.handleChange(option.value, index)}
                    required={this.props.field.required}
                    className="form-check-input mr-2"
                    disabled={this.attributes.disabled}
                />
                <label htmlFor={`${fieldName}_${option.id}`}>
                  {option.label}
                </label>
              </div>
          ))}
        </div>
    );
  }
}

export default RadioInput;
