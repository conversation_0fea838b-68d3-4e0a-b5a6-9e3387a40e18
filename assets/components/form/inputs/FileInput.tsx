import * as React from 'react';
import BaseF<PERSON><PERSON>ield, { BaseFormFieldProps } from './BaseFormField';

class FileInput extends BaseFormField<File | null> {
    renderSingleField(value: File | null, index?: number): React.ReactNode {
        return (
            <div>
                <input
                    type="file"
                    id={this.getFieldId(index)}
                    onChange={(e) => {
                        const file = e.target.files?.[0] || null;
                        this.handleChange(file, index);
                    }}
                    required={this.props.field.required}
                    className="form-control"
                    disabled={this.attributes.disabled}
                    accept={this.attributes.accept}
                />
                {value && (
                    <small className="form-text text-muted">
                        Selected: {value.name} ({Math.round(value.size / 1024)}KB)
                    </small>
                )}
            </div>
        );
    }
}

export default FileInput;
