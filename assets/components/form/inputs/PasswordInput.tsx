import * as React from 'react';
import BaseForm<PERSON>ield, { BaseFormFieldProps } from './BaseFormField';

class PasswordInput extends BaseFormField<string> {
  renderSingleField(value: string, index?: number): React.ReactNode {
    return (
        <input
            type="password"
            id={this.getFieldId(index)}
            value={value || ''}
            onChange={(e) => this.handleChange(e.target.value, index)}
            required={this.props.field.required}
            className="form-control"
            minLength={this.props.field.constraints.min_length || undefined}
            maxLength={this.props.field.constraints.max_length || undefined}
            disabled={this.attributes.disabled}
            readOnly={this.attributes.readonly}
            placeholder={this.props.field.help_text || 'Enter password'}
        />
    );
  }
}

export default PasswordInput;
