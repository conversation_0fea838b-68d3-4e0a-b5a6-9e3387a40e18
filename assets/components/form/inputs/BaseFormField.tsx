import * as React from 'react';

export interface FormFieldData {
    id: string;
    label: string;
    help_text: string | null;
    type: string;
    required: boolean;
    constraints: {
        min_length: number | null;
        max_length: number | null;
        allow_multiple: boolean;
        min_count: number;
        max_count: number | null;
        accessibility: string;
    };
    options: Array<{
        id: string;
        label: string;
        value: string;
        active: boolean;
        ordinal: number;
    }>;
}

export interface BaseFormFieldProps {
    field: FormFieldData;
    value: any;
    attributes?: Record<string, any>;
    onChange: (value: any) => void;
    onMultipleValuesChange?: (index: number, value: any) => void;
    onAddMultipleValue?: () => void;
    onRemoveMultipleValue?: (index: number) => void;
}

export interface SingleFieldProps {
    field: FormFieldData;
    value: any;
    index?: number;
    attributes?: Record<string, any>;
    onChange: (value: any) => void;
}

export abstract class BaseFormField<T = any> extends React.Component<BaseFormFieldProps> {
    protected get isMultiple(): boolean {
        return this.props.field.constraints.allow_multiple;
    }

    protected get values(): T[] {
        return this.isMultiple ? (Array.isArray(this.props.value) ? this.props.value : []) : this.props.value;
    }

    protected get minCount(): number {
        return this.props.field.constraints.min_count || 0;
    }

    protected get fieldId(): string {
        return this.props.field.id;
    }

    protected get attributes(): Record<string, any> {
        return this.props.attributes || {};
    }

    componentDidMount() {
        this.initializeMinimumValues();
    }

    componentDidUpdate(prevProps: BaseFormFieldProps) {
        if (prevProps.field.constraints.min_count !== this.props.field.constraints.min_count) {
            this.initializeMinimumValues();
        }
    }

    private initializeMinimumValues() {
        if (this.isMultiple && (!this.values || this.values.length < this.minCount) && this.props.onAddMultipleValue) {
            const valuesToAdd = this.minCount - (this.values?.length || 0);
            for (let i = 0; i < valuesToAdd; i++) {
                this.props.onAddMultipleValue();
            }
        }
    }

    protected handleChange = (newValue: T, index?: number) => {
        if (this.isMultiple && this.props.onMultipleValuesChange && index !== undefined) {
            this.props.onMultipleValuesChange(index, newValue);
        } else {
            this.props.onChange(newValue);
        }
    };

    protected getFieldId(index?: number): string {
        return this.isMultiple && index !== undefined ? `${this.fieldId}_${index}` : this.fieldId;
    }

    protected renderRequiredIndicator(): React.ReactNode {
        return this.props.field.required ? <span className="text-red-800"> *</span> : null;
    }

    protected renderHelpText(): React.ReactNode {
        return this.props.field.help_text ? (
            <small className="form-text text-muted">{this.props.field.help_text}</small>
        ) : null;
    }

    protected renderMultipleFieldControls(): React.ReactNode {
        if (!this.isMultiple) return null;

        const values = this.values as T[];
        return (
            <>
                {values.map((val: T, index: number) => (
                    <div key={index} className="multiple-field-row">
                        {this.renderSingleField(val, index)}
                        {this.props.onRemoveMultipleValue && values.length > this.minCount && (
                            <button
                                type="button"
                                className="remove-field-btn"
                                onClick={() => this.props.onRemoveMultipleValue!(index)}
                            >
                                Remove
                            </button>
                        )}
                    </div>
                ))}

                {this.props.onAddMultipleValue &&
                    (!this.props.field.constraints.max_count || values.length < this.props.field.constraints.max_count) && (
                        <button
                            type="button"
                            className="add-field-btn"
                            onClick={this.props.onAddMultipleValue}
                        >
                            Add Another
                        </button>
                    )}
            </>
        );
    }

    protected renderStandardLayout(): React.ReactNode {
        return (
            <div className="frm-row">
                <div className="input-row">
                    <div className="input-row-label">
                        <label htmlFor={this.fieldId} className="form-label">
                            {this.props.field.label}
                            {this.renderRequiredIndicator()}
                        </label>
                    </div>
                    <div className="input-row-input">
                        <div>{this.renderSingleField(this.props.value)}</div>
                        {this.renderHelpText()}
                    </div>
                </div>
            </div>
        );
    }

    protected renderMultipleLayout(): React.ReactNode {
        return (
            <div className="frm-row">
                <div className="input-row">
                    <div className="input-row-label">
                        <label className="form-label">
                            {this.props.field.label}
                            {this.renderRequiredIndicator()}
                        </label>
                    </div>
                    <div className="input-row-input">
                        {this.renderMultipleFieldControls()}
                        {this.renderHelpText()}
                    </div>
                </div>
            </div>
        );
    }

    // Abstract method that each field type must implement
    abstract renderSingleField(value: T, index?: number): React.ReactNode;

    // Default render method - can be overridden for special layouts
    render(): React.ReactNode {
        if (this.isMultiple) {
            return this.renderMultipleLayout();
        }
        return this.renderStandardLayout();
    }
}

export default BaseFormField;
