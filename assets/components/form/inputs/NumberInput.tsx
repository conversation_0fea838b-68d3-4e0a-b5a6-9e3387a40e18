import * as React from 'react';
import BaseForm<PERSON>ield, { BaseFormFieldProps } from './BaseFormField';

class NumberInput extends BaseFormField<number> {
  renderSingleField(value: number, index?: number): React.ReactNode {
    return (
        <input
            type="number"
            id={this.getFieldId(index)}
            value={value || ''}
            onChange={(e) => this.handleChange(parseFloat(e.target.value) || 0, index)}
            required={this.props.field.required}
            className="form-control"
            min={this.props.field.constraints.min_length || undefined}
            max={this.props.field.constraints.max_length || undefined}
            disabled={this.attributes.disabled}
            readOnly={this.attributes.readonly}
            placeholder={this.props.field.help_text || 'Enter number'}
        />
    );
  }
}

export default NumberInput;
