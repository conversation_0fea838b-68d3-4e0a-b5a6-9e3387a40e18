# Form Components

A comprehensive form system with modular input components, form builder, and subscription-based field interactions.

## Architecture

### Core Components

```
assets/components/form/
├── Form.tsx                    # Main form component
├── FormField.tsx              # Field wrapper component
├── FormSection.tsx            # Section wrapper component
├── index.tsx                  # TypeScript exports
├── index.js                   # JavaScript entry point
├── README.md                  # This file
├── inputs/                    # Individual input components
│   ├── FormFieldFactory.tsx  # Factory for creating field components
│   ├── FormFieldBase.tsx     # Shared base component with common logic
│   ├── TextInput.tsx         # Text input field
│   ├── TextareaInput.tsx     # Textarea field
│   ├── EmailInput.tsx        # Email input field
│   ├── PasswordInput.tsx     # Password input field
│   ├── NumberInput.tsx       # Number input field
│   ├── UrlInput.tsx          # URL input field
│   ├── SelectInput.tsx       # Select dropdown field
│   ├── RadioInput.tsx        # Radio button field
│   ├── CheckboxInput.tsx     # Checkbox field
│   ├── DateInput.tsx         # Date picker field
│   ├── TimeInput.tsx         # Time picker field
│   └── FileInput.tsx         # File upload field
└── builder/                   # Form builder components
    ├── FormBuilder.tsx        # Main builder component
    ├── FormBuilderPalette.tsx # Draggable element palette
    ├── FormBuilderGrid.tsx    # Drop zone grid
    ├── FormBuilderProperties.tsx # Property editor
    ├── FormBuilderElement.tsx # Individual elements in builder
    ├── FormBuilderToolbar.tsx # Toolbar with actions
    ├── FormPreview.tsx        # Live form preview
    ├── types.ts              # TypeScript interfaces
    ├── index.tsx             # TypeScript exports
    ├── index.js              # JavaScript entry point
    └── README.md             # Builder documentation
```

## Features

### ✨ Form Rendering
- **Dynamic Forms**: Render forms from JSON configuration
- **Field Types**: Support for all common input types
- **Sections**: Group related fields together
- **Validation**: Built-in validation with constraints
- **Multiple Values**: Support for array-based fields
- **Subscriptions**: Dynamic field interactions

### 🎯 Field Types Supported
- **Text Inputs**: text, textarea, email, password, number, url
- **Selection**: select, radio, checkbox
- **Date/Time**: date, time
- **File Upload**: file input with preview

### ⚙️ Field Properties
- Label (required)
- Help text
- Required flag
- Type-specific constraints (min/max length, etc.)
- Multiple values support
- Options for select/radio fields
- Accessibility settings

### 🔄 Subscription System
Form fields can subscribe to changes in other fields with:
- **Listen Criteria**: Value changes, attribute changes, visibility changes
- **Actions**: Show/hide, enable/disable, set values, modify options
- **Default Actions**: Fallback behavior when no conditions match
- **Multiple Criteria**: Complex conditional logic

## Usage

### Basic Form Rendering

```tsx
import { Form } from './components/form';

<Form formId="contact-form" />
```

### Individual Field Components

```tsx
import { FormField } from './components/form';

<FormField
  field={{
    id: 'email',
    label: 'Email Address',
    type: 'email',
    required: true,
    // ... other properties
  }}
  value={email}
  onChange={setEmail}
/>
```

### Form Builder

```tsx
import { FormBuilder } from './components/form/builder';

<FormBuilder
  onSave={(form) => console.log('Saved:', form)}
  onPreview={(form) => showPreview(form)}
/>
```

## Data Format

### Form Structure
```json
{
  "id": "form-id",
  "name": "Contact Form",
  "description": "A simple contact form",
  "elements": [
    {
      "id": "field-id",
      "label": "Name",
      "type": "text",
      "required": true,
      "ordinal": 0,
      "constraints": {
        "min_length": 2,
        "max_length": 50,
        "allow_multiple": false
      },
      "_meta": {
        "_type": "field",
        "section_id": null
      }
    }
  ]
}
```

### Field Subscriptions
```json
{
  "subscriptions": {
    "show_when_premium": {
      "_listen": {
        "field_id": "membership_type",
        "value": "premium"
      },
      "_action": {
        "visible": true
      }
    }
  }
}
```

## Modular Architecture

### FormFieldBase Component
All input components extend `FormFieldBase` which provides:
- Consistent layout and styling
- Multiple values support
- Label and help text rendering
- Error handling
- Accessibility features

### FormFieldFactory
Routes field types to appropriate components:
```tsx
switch (field.type) {
  case 'text': return <TextInput {...props} />;
  case 'email': return <EmailInput {...props} />;
  // ... etc
}
```

### Individual Input Components
Each input type has its own component:
- Focused on single responsibility
- Consistent interface
- Easy to extend or customize
- Proper TypeScript typing

## Styling

The components use a combination of:
- **CSS Classes**: Bootstrap-compatible classes
- **Inline Styles**: For layout and spacing
- **Custom CSS**: Form-specific styling

Key CSS classes:
- `.frm-row` - Form row container
- `.input-row` - Input row wrapper
- `.input-row-label` - Label container
- `.input-row-input` - Input container
- `.form-control` - Input styling
- `.form-section` - Section container

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Dependencies

- **React 16.8+**: Hooks support required
- **Axios**: For API requests
- **react-dnd**: Drag and drop (builder only)
- **react-dnd-html5-backend**: HTML5 backend (builder only)

## Future Enhancements

- [ ] Advanced validation rules
- [ ] Custom field types
- [ ] Form analytics
- [ ] Accessibility improvements
- [ ] Mobile optimization
- [ ] Internationalization
- [ ] Form versioning
- [ ] Advanced subscription logic
