import * as React from 'react';
import FormFieldFactory from './inputs/FormFieldFactory';
import { FormFieldData } from './inputs/BaseFormField';

export interface FormFieldProps {
  field: FormFieldData;
  value: any;
  attributes?: Record<string, any>;
  onChange: (value: any) => void;
  onMultipleValuesChange?: (index: number, value: any) => void;
  onAddMultipleValue?: () => void;
  onRemoveMultipleValue?: (index: number) => void;
}

const FormField: React.FC<FormFieldProps> = (props) => {
  return <FormFieldFactory {...props} />;
};

export default FormField;
