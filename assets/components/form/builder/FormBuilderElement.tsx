import * as React from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { FormElement, FormSectionData, FormFieldData, DragItem } from './types';

interface FormBuilderElementProps {
  element: FormElement;
  index: number;
  isSelected: boolean;
  onSelect: (element: FormElement, type: 'field' | 'section') => void;
  onMove: (fromIndex: number, toIndex: number) => void;
  onDelete: () => void;
  onUpdate: (element: FormElement) => void;
  onDrop: (item: DragItem, targetIndex: number) => void;
}

const FormBuilderElement: React.FC<FormBuilderElementProps> = ({
  element,
  index,
  isSelected,
  onSelect,
  onMove,
  onDelete,
  onUpdate,
  onDrop
}) => {
  const isSection = element._meta._type === 'section';
  const section = isSection ? element as FormSectionData : null;
  const field = !isSection ? element as FormFieldData : null;

  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'form-element',
    item: {
      type: 'form-element',
      item: element,
      sourceIndex: index
    } as DragItem,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  const [{ isOver }, drop] = useDrop(() => ({
    accept: ['palette-item', 'form-element'],
    drop: (item: DragItem, monitor) => {
      if (!monitor.didDrop()) {
        onDrop(item, index);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
    }),
  }));

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect(element, isSection ? 'section' : 'field');
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete();
  };

  const getFieldIcon = (type: string): string => {
    const icons: Record<string, string> = {
      text: 'fas fa-font',
      textarea: 'fas fa-align-left',
      email: 'fas fa-envelope',
      password: 'fas fa-lock',
      number: 'fas fa-hashtag',
      url: 'fas fa-link',
      select: 'fas fa-caret-down',
      radio: 'fas fa-dot-circle',
      checkbox: 'fas fa-check-square',
      date: 'fas fa-calendar',
      time: 'fas fa-clock',
      file: 'fas fa-paperclip'
    };
    return icons[type] || 'fas fa-question';
  };

  const elementStyle: React.CSSProperties = {
    margin: '8px 0',
    padding: '16px',
    backgroundColor: isSelected ? '#e3f2fd' : '#ffffff',
    border: isSelected ? '2px solid #2196f3' : '1px solid #dee2e6',
    borderRadius: '4px',
    cursor: 'pointer',
    position: 'relative',
    opacity: isDragging ? 0.5 : 1,
    transition: 'all 0.2s ease'
  };

  if (isSection && section) {
    return (
      <div ref={(node) => drag(drop(node))}>
        {/* Drop indicator above */}
        {isOver && (
          <div style={{
            height: '4px',
            backgroundColor: '#007bff',
            borderRadius: '2px',
            margin: '4px 0',
            opacity: 0.7
          }} />
        )}
        
        <div style={elementStyle} onClick={handleClick}>
          {/* Section header */}
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'space-between',
            marginBottom: '12px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <i className="fas fa-folder" style={{ fontSize: '16px', color: '#6c757d' }}></i>
              <div>
                <div style={{ fontWeight: 'bold', fontSize: '16px' }}>
                  {section.label}
                </div>
                {section.description && (
                  <div style={{ fontSize: '14px', color: '#6c757d' }}>
                    {section.description}
                  </div>
                )}
              </div>
            </div>
            <div style={{ display: 'flex', gap: '8px' }}>
              <button
                onClick={handleDelete}
                style={{
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: '#dc3545',
                  padding: '4px'
                }}
                title="Delete section"
              >
                <i className="fas fa-trash"></i>
              </button>
            </div>
          </div>

          {/* Section fields */}
          <div style={{ 
            marginLeft: '24px',
            padding: '12px',
            backgroundColor: '#f8f9fa',
            borderRadius: '4px',
            minHeight: '60px'
          }}>
            {section.fields.length === 0 ? (
              <div style={{ 
                textAlign: 'center', 
                color: '#6c757d',
                padding: '20px',
                fontSize: '14px'
              }}>
                Drop fields here
              </div>
            ) : (
              section.fields.map((sectionField, fieldIndex) => (
                <div
                  key={sectionField.id}
                  style={{
                    padding: '8px 12px',
                    margin: '4px 0',
                    backgroundColor: '#ffffff',
                    border: '1px solid #dee2e6',
                    borderRadius: '4px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onSelect(sectionField, 'field');
                  }}
                >
                  <i className={getFieldIcon(sectionField.type)} style={{ fontSize: '14px', color: '#6c757d' }}></i>
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                      {sectionField.label}
                    </div>
                    <div style={{ fontSize: '12px', color: '#6c757d' }}>
                      {sectionField.type} {sectionField.required && '(required)'}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    );
  }

  if (field) {
    return (
      <div ref={(node) => drag(drop(node))}>
        {/* Drop indicator above */}
        {isOver && (
          <div style={{
            height: '4px',
            backgroundColor: '#007bff',
            borderRadius: '2px',
            margin: '4px 0',
            opacity: 0.7
          }} />
        )}
        
        <div style={elementStyle} onClick={handleClick}>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'space-between'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <i className={getFieldIcon(field.type)} style={{ fontSize: '16px', color: '#6c757d' }}></i>
              <div>
                <div style={{ fontWeight: 'bold', fontSize: '16px' }}>
                  {field.label}
                  {field.required && <span style={{ color: '#dc3545' }}> *</span>}
                </div>
                <div style={{ fontSize: '14px', color: '#6c757d' }}>
                  {field.type}
                  {field.help_text && ` • ${field.help_text}`}
                </div>
              </div>
            </div>
            <div style={{ display: 'flex', gap: '8px' }}>
              <button
                onClick={handleDelete}
                style={{
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: '#dc3545',
                  padding: '4px'
                }}
                title="Delete field"
              >
                <i className="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default FormBuilderElement;
