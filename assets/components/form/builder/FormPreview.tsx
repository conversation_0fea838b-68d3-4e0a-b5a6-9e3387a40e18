import * as React from 'react';
import { useState } from 'react';
import { FormData } from './types';
import FormField from '../FormField';
import FormSection from '../FormSection';

interface FormPreviewProps {
  form: FormData;
  onClose?: () => void;
  className?: string;
}

const FormPreview: React.FC<FormPreviewProps> = ({
  form,
  onClose,
  className = ''
}) => {
  const [formValues, setFormValues] = useState<Record<string, any>>({});
  const [visibleElements] = useState<Record<string, boolean>>(() => {
    // Initialize all elements as visible for preview
    const visibility: Record<string, boolean> = {};
    form.elements.forEach(element => {
      visibility[element.id] = true;
      if (element._meta._type === 'section') {
        const section = element as any;
        section.fields?.forEach((field: any) => {
          visibility[field.id] = true;
        });
      }
    });
    return visibility;
  });
  const [elementAttributes] = useState<Record<string, Record<string, any>>>(() => {
    // Initialize all elements with default attributes
    const attributes: Record<string, Record<string, any>> = {};
    form.elements.forEach(element => {
      attributes[element.id] = { disabled: false, readonly: false };
      if (element._meta._type === 'section') {
        const section = element as any;
        section.fields?.forEach((field: any) => {
          attributes[field.id] = { disabled: false, readonly: false };
        });
      }
    });
    return attributes;
  });

  const handleFieldChange = (fieldId: string, value: any) => {
    setFormValues(prev => ({
      ...prev,
      [fieldId]: value
    }));
  };

  const handleMultipleValuesChange = (fieldId: string, index: number, value: any) => {
    setFormValues(prev => {
      const values = [...(prev[fieldId] || [])];
      values[index] = value;
      return {
        ...prev,
        [fieldId]: values
      };
    });
  };

  const addMultipleValue = (fieldId: string) => {
    setFormValues(prev => {
      const values = [...(prev[fieldId] || []), ''];
      return {
        ...prev,
        [fieldId]: values
      };
    });
  };

  const removeMultipleValue = (fieldId: string, index: number) => {
    setFormValues(prev => {
      const values = [...(prev[fieldId] || [])];
      values.splice(index, 1);
      return {
        ...prev,
        [fieldId]: values
      };
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    alert('Form submitted! Check console for values.');
    console.log('Form values:', formValues);
  };

  // Sort elements by ordinal
  const sortedElements = [...form.elements].sort((a, b) => a.ordinal - b.ordinal);

  return (
    <div className={`form-preview ${className}`} style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: '#ffffff',
        borderRadius: '8px',
        padding: '24px',
        maxWidth: '800px',
        maxHeight: '90vh',
        width: '90%',
        overflow: 'auto',
        position: 'relative'
      }}>
        {/* Close button */}
        {onClose && (
          <button
            onClick={onClose}
            style={{
              position: 'absolute',
              top: '16px',
              right: '16px',
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#6c757d'
            }}
          >
            <i className="fas fa-times"></i>
          </button>
        )}

        {/* Preview header */}
        <div style={{
          marginBottom: '24px',
          paddingBottom: '16px',
          borderBottom: '1px solid #dee2e6'
        }}>
          <h2 style={{ margin: '0 0 8px 0', fontSize: '24px', fontWeight: 'bold' }}>
            Form Preview
          </h2>
          <p style={{ margin: 0, color: '#6c757d', fontSize: '14px' }}>
            This is how your form will appear to users
          </p>
        </div>

        {/* Form content */}
        <div className="dynamic-form">
          <h2>{form.name}</h2>
          {form.description && <p>{form.description}</p>}

          <form id={form.id} onSubmit={handleSubmit}>
            {sortedElements.map((element) => {
              if (element._meta._type === 'section') {
                // Render section
                return (
                  visibleElements[element.id] && (
                    <FormSection
                      key={element.id}
                      section={element as any}
                      formValues={formValues}
                      visibleElements={visibleElements}
                      elementAttributes={elementAttributes}
                      onFieldChange={handleFieldChange}
                      onMultipleValuesChange={handleMultipleValuesChange}
                      onAddMultipleValue={addMultipleValue}
                      onRemoveMultipleValue={removeMultipleValue}
                    />
                  )
                );
              } else {
                // Render standalone field
                return (
                  visibleElements[element.id] && (
                    <FormField
                      key={element.id}
                      field={element as any}
                      value={formValues[element.id]}
                      attributes={elementAttributes[element.id] || {}}
                      onChange={(value) => handleFieldChange(element.id, value)}
                      onMultipleValuesChange={(index, value) => handleMultipleValuesChange(element.id, index, value)}
                      onAddMultipleValue={() => addMultipleValue(element.id)}
                      onRemoveMultipleValue={(index) => removeMultipleValue(element.id, index)}
                    />
                  )
                );
              }
            })}

            <div className="frm-row">
              <div className="submit-row">
                <div className="input-row-label"></div>
                <div className="input-row-input">
                  <button
                    type="submit"
                    id={`${form.id}_submit_`}
                    name={`${form.id}[_submit_]`}
                    className="clubster-button"
                  >
                    Submit
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>

        {/* Form values display */}
        <div style={{
          marginTop: '24px',
          padding: '16px',
          backgroundColor: '#f8f9fa',
          borderRadius: '4px',
          border: '1px solid #dee2e6'
        }}>
          <h4 style={{ margin: '0 0 12px 0', fontSize: '16px', fontWeight: 'bold' }}>
            Current Form Values:
          </h4>
          <pre style={{
            margin: 0,
            fontSize: '12px',
            color: '#495057',
            maxHeight: '200px',
            overflow: 'auto'
          }}>
            {JSON.stringify(formValues, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default FormPreview;
