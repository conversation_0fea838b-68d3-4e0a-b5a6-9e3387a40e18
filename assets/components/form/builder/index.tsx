// Main form builder component
export { default as <PERSON>Builder } from './FormBuilder';

// Individual components for modular use
export { default as FormBuilderPalette } from './FormBuilderPalette';
export { default as FormBuilderGrid } from './FormBuilderGrid';
export { default as FormBuilderProperties } from './FormBuilderProperties';
export { default as FormBuilderToolbar } from './FormBuilderToolbar';
export { default as FormBuilderElement } from './FormBuilderElement';
export { default as FormPreview } from './FormPreview';

// Types and utilities
export * from './types';

// Re-export for convenience
export type {
  FormData,
  FormElement,
  FormFieldData,
  FormSectionData,
  OptionData,
  PaletteItem,
  DragItem,
  BuilderState
} from './types';
