import * as React from 'react';

interface FormBuilderToolbarProps {
  onSave: () => void;
  onLoad?: () => void;
  onPreview: () => void;
  onTest: () => void;
  onNew: () => void;
  previewMode: boolean;
  testMode: boolean;
}

const FormBuilderToolbar: React.FC<FormBuilderToolbarProps> = ({
  onSave,
  onLoad,
  onPreview,
  onTest,
  onNew,
  previewMode,
  testMode
}) => {
  const buttonStyle: React.CSSProperties = {
    padding: '8px 16px',
    margin: '0 4px',
    border: '1px solid #dee2e6',
    borderRadius: '4px',
    backgroundColor: '#ffffff',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: 'bold',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    gap: '6px'
  };

  const activeButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: '#007bff',
    color: '#ffffff',
    borderColor: '#007bff'
  };

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '12px 16px',
      backgroundColor: '#ffffff',
      borderBottom: '1px solid #dee2e6',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
    }}>
      {/* Left side - Main actions */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <button
          onClick={onNew}
          style={buttonStyle}
          onMouseEnter={(e) => {
            if (!e.currentTarget.disabled) {
              e.currentTarget.style.backgroundColor = '#e9ecef';
            }
          }}
          onMouseLeave={(e) => {
            if (!e.currentTarget.disabled) {
              e.currentTarget.style.backgroundColor = '#ffffff';
            }
          }}
        >
          <i className="fas fa-file"></i>
          New
        </button>

        {onLoad && (
          <button
            onClick={onLoad}
            style={buttonStyle}
            onMouseEnter={(e) => {
              if (!e.currentTarget.disabled) {
                e.currentTarget.style.backgroundColor = '#e9ecef';
              }
            }}
            onMouseLeave={(e) => {
              if (!e.currentTarget.disabled) {
                e.currentTarget.style.backgroundColor = '#ffffff';
              }
            }}
          >
            <i className="fas fa-folder-open"></i>
            Load
          </button>
        )}

        <button
          onClick={onSave}
          style={{
            ...buttonStyle,
            backgroundColor: '#28a745',
            color: '#ffffff',
            borderColor: '#28a745'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#218838';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = '#28a745';
          }}
        >
          <i className="fas fa-save"></i>
          Save
        </button>

        <div style={{
          width: '1px',
          height: '24px',
          backgroundColor: '#dee2e6',
          margin: '0 12px'
        }} />

        <button
          onClick={onPreview}
          style={previewMode ? activeButtonStyle : buttonStyle}
          onMouseEnter={(e) => {
            if (!previewMode) {
              e.currentTarget.style.backgroundColor = '#e9ecef';
            }
          }}
          onMouseLeave={(e) => {
            if (!previewMode) {
              e.currentTarget.style.backgroundColor = '#ffffff';
            }
          }}
        >
          <i className="fas fa-eye"></i>
          {previewMode ? 'Exit Preview' : 'Preview'}
        </button>

        <button
          onClick={onTest}
          style={testMode ? activeButtonStyle : buttonStyle}
          onMouseEnter={(e) => {
            if (!testMode) {
              e.currentTarget.style.backgroundColor = '#e9ecef';
            }
          }}
          onMouseLeave={(e) => {
            if (!testMode) {
              e.currentTarget.style.backgroundColor = '#ffffff';
            }
          }}
        >
          <i className="fas fa-vial"></i>
          {testMode ? 'Exit Test' : 'Test'}
        </button>
      </div>

      {/* Right side - Info */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '16px',
        fontSize: '14px',
        color: '#6c757d'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <i className="fas fa-info-circle"></i>
          <span>Drag elements from the palette to build your form</span>
        </div>
      </div>
    </div>
  );
};

export default FormBuilderToolbar;
