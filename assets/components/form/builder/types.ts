// Re-export form types for builder use
export interface FormData {
  id: string;
  name: string;
  description: string;
  active_at: string | null;
  deactivate_at: string | null;
  elements: Array<FormElement>;
  actions: FormAction[];
  _meta: MetaData;
}

export type FormElement = FormFieldData | FormSectionData;

export interface FormSectionData {
  id: string;
  label: string;
  description: string;
  ordinal: number;
  fields: FormFieldData[];
  subscriptions?: Record<string, SubscriptionData>;
  _meta: SectionMetaData;
}

export interface FormFieldData {
  id: string;
  label: string;
  help_text: string | null;
  type: string;
  required: boolean;
  constraints: {
    min_length: number | null;
    max_length: number | null;
    allow_multiple: boolean;
    min_count: number;
    max_count: number | null;
    accessibility: string;
  };
  ordinal: number;
  options: OptionData[];
  subscriptions: Record<string, SubscriptionData>;
  _meta: FieldMetaData;
}

export interface OptionData {
  id: string;
  label: string;
  value: string;
  active: boolean;
  ordinal: number;
  subscriptions: Record<string, SubscriptionData>;
  _meta: MetaData;
}

export interface SubscriptionData {
  id: string;
  action: SubscriptionAction[];
  producer: string;
  _meta: MetaData;
}

export interface SubscriptionAction {
  _listen?: Record<string, any>;
  _action: Record<string, any>;
  "!important"?: boolean;
  "!default"?: boolean;
}

export interface MetaData {
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
}

export interface FieldMetaData extends MetaData {
  _type: string;
  section_id: string | null;
}

export interface SectionMetaData extends MetaData {
  _type: string;
}

export interface FormAction {
  id: string;
  type: 'save_data' | 'send_email' | 'redirect' | 'webhook';
  config: Record<string, any>;
  active: boolean;
  ordinal: number;
}

// Builder-specific types
export interface PaletteItem {
  id: string;
  type: 'field' | 'section';
  fieldType?: string;
  label: string;
  icon: string;
  description: string;
}

export interface DragItem {
  type: 'palette-item' | 'form-element';
  item: PaletteItem | FormElement;
  sourceIndex?: number;
  sourceSection?: string;
}

export interface DropZone {
  type: 'form' | 'section';
  targetId?: string;
  targetIndex: number;
}

export interface BuilderState {
  form: FormData;
  selectedElement: FormElement | null;
  selectedElementType: 'form' | 'field' | 'section' | null;
  isDragging: boolean;
  dragItem: DragItem | null;
  previewMode: boolean;
  testMode: boolean;
}

export interface GridPosition {
  row: number;
  column: number;
}

export interface GridCell {
  id: string;
  position: GridPosition;
  element?: FormElement;
  isDropZone: boolean;
  isOccupied: boolean;
}

// Field type definitions for the palette
export const FIELD_TYPES = [
  'text', 'textarea', 'email', 'password', 'number', 'url',
  'select', 'radio', 'checkbox', 'date', 'time', 'file'
] as const;

export type FieldType = typeof FIELD_TYPES[number];

// Utility functions
export const generateId = (): string => {
  return 'id_' + Math.random().toString(36).substr(2, 9);
};

export const getCurrentTimestamp = (): string => {
  return new Date().toISOString();
};

export const createDefaultMeta = (type: string, sectionId?: string): FieldMetaData | SectionMetaData => {
  const baseMeta = {
    created_at: getCurrentTimestamp(),
    created_by: 'builder-user', // This should come from auth context
    updated_at: null,
    updated_by: null,
    deleted_at: null,
  };

  if (type === 'section') {
    return {
      ...baseMeta,
      _type: 'section',
    } as SectionMetaData;
  }

  return {
    ...baseMeta,
    _type: 'field',
    section_id: sectionId || null,
  } as FieldMetaData;
};
