import * as React from 'react';
import { useDrop } from 'react-dnd';
import { FormData, FormElement, FormSectionData, FormFieldData, DragItem, generateId, createDefaultMeta } from './types';
import FormBuilderElement from './FormBuilderElement';

interface FormBuilderGridProps {
  form: FormData;
  selectedElement: FormElement | null;
  onFormChange: (form: FormData) => void;
  onElementSelect: (element: FormElement | null, type: 'form' | 'field' | 'section' | null) => void;
  className?: string;
}

const FormBuilderGrid: React.FC<FormBuilderGridProps> = ({
  form,
  selectedElement,
  onFormChange,
  onElementSelect,
  className = ''
}) => {
  const createFieldFromPalette = (fieldType: string): FormFieldData => {
    return {
      id: generateId(),
      label: `New ${fieldType.charAt(0).toUpperCase() + fieldType.slice(1)} Field`,
      help_text: null,
      type: fieldType,
      required: false,
      constraints: {
        min_length: null,
        max_length: null,
        allow_multiple: false,
        min_count: 0,
        max_count: null,
        accessibility: 'public'
      },
      ordinal: 0, // Will be set when saving
      options: [],
      subscriptions: {},
      _meta: createDefaultMeta('field') as any
    };
  };

  const createSectionFromPalette = (): FormSectionData => {
    return {
      id: generateId(),
      label: 'New Section',
      description: '',
      ordinal: 0, // Will be set when saving
      fields: [],
      subscriptions: {},
      _meta: createDefaultMeta('section') as any
    };
  };

  const handleDrop = (item: DragItem, targetIndex: number) => {
    console.log('FormBuilderGrid - handleDrop called:', { item, targetIndex, currentElements: form.elements.length });
    
    const newElements = [...form.elements];

    if (item.type === 'palette-item') {
      // Adding new element from palette
      const paletteItem = item.item as any;
      let newElement: FormElement;

      console.log('Adding new element from palette:', paletteItem);

      if (paletteItem.type === 'section') {
        newElement = createSectionFromPalette();
      } else {
        newElement = createFieldFromPalette(paletteItem.fieldType);
      }

      console.log('Created new element:', newElement);
      newElements.splice(targetIndex, 0, newElement);
    } else if (item.type === 'form-element') {
      // Moving existing element
      const sourceIndex = item.sourceIndex!;
      const element = newElements[sourceIndex];
      
      console.log('Moving existing element:', { sourceIndex, targetIndex, element });
      
      // Remove from source
      newElements.splice(sourceIndex, 1);
      
      // Adjust target index if moving down
      const adjustedIndex = sourceIndex < targetIndex ? targetIndex - 1 : targetIndex;
      
      // Insert at target
      newElements.splice(adjustedIndex, 0, element);
    }

    console.log('New elements array:', newElements);
    
    const updatedForm = {
      ...form,
      elements: newElements
    };
    
    console.log('Calling onFormChange with:', updatedForm);
    onFormChange(updatedForm);
  };

  const handleElementMove = (fromIndex: number, toIndex: number) => {
    const newElements = [...form.elements];
    const [movedElement] = newElements.splice(fromIndex, 1);
    newElements.splice(toIndex, 0, movedElement);

    onFormChange({
      ...form,
      elements: newElements
    });
  };

  const handleElementDelete = (index: number) => {
    const newElements = [...form.elements];
    newElements.splice(index, 1);

    onFormChange({
      ...form,
      elements: newElements
    });

    // Clear selection if deleted element was selected
    if (selectedElement && form.elements[index].id === selectedElement.id) {
      onElementSelect(null, null);
    }
  };

  const handleElementUpdate = (index: number, updatedElement: FormElement) => {
    const newElements = [...form.elements];
    newElements[index] = updatedElement;

    onFormChange({
      ...form,
      elements: newElements
    });
  };

  const [{ isOver }, drop] = useDrop(() => ({
    accept: ['palette-item', 'form-element'],
    drop: (item: DragItem, monitor) => {
      if (!monitor.didDrop()) {
        // Dropped on the grid itself (at the end)
        handleDrop(item, form.elements.length);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
    }),
  }));

  return (
    <div
      ref={drop}
      className={`form-builder-grid ${className}`}
      style={{
        flex: 1,
        minHeight: '600px',
        backgroundColor: '#ffffff',
        border: '2px dashed #dee2e6',
        borderRadius: '8px',
        padding: '24px',
        position: 'relative',
        backgroundImage: `
          radial-gradient(circle, #dee2e6 1px, transparent 1px)
        `,
        backgroundSize: '20px 20px',
        backgroundPosition: '0 0',
        overflow: 'auto'
      }}
      onClick={() => onElementSelect(null, 'form')}
    >
      {/* Form header */}
      <div 
        style={{
          marginBottom: '32px',
          padding: '16px',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          border: '1px solid #dee2e6',
          borderRadius: '4px'
        }}
        onClick={(e) => {
          e.stopPropagation();
          onElementSelect(null, 'form');
        }}
      >
        <h2 style={{ margin: '0 0 8px 0', fontSize: '24px', fontWeight: 'bold' }}>
          {form.name || 'Untitled Form'}
        </h2>
        {form.description && (
          <p style={{ margin: 0, color: '#6c757d' }}>{form.description}</p>
        )}
      </div>

      {/* Form elements */}
      <div style={{ minHeight: '200px' }}>
        {(() => {
          console.log('FormBuilderGrid render - form.elements:', form.elements);
          console.log('FormBuilderGrid render - elements length:', form.elements.length);
          return null;
        })()}
        {form.elements.length === 0 ? (
          <div style={{
            textAlign: 'center',
            padding: '48px',
            color: '#6c757d',
            fontSize: '16px'
          }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>📝</div>
            <div>Drag form elements from the palette to start building your form</div>
          </div>
        ) : (
          form.elements.map((element, index) => (
            <FormBuilderElement
              key={element.id}
              element={element}
              index={index}
              isSelected={selectedElement?.id === element.id}
              onSelect={(element, type) => onElementSelect(element, type)}
              onMove={handleElementMove}
              onDelete={() => handleElementDelete(index)}
              onUpdate={(updatedElement) => handleElementUpdate(index, updatedElement)}
              onDrop={handleDrop}
            />
          ))
        )}
      </div>

      {/* Drop indicator */}
      {isOver && (
        <div style={{
          position: 'absolute',
          bottom: '24px',
          left: '24px',
          right: '24px',
          height: '4px',
          backgroundColor: '#007bff',
          borderRadius: '2px',
          opacity: 0.7
        }} />
      )}
    </div>
  );
};

export default FormBuilderGrid;
