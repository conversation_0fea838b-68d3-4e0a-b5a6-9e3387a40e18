import * as React from 'react';
import { useState } from 'react';
import { FormData, FormElement, FormFieldData, FormSectionData, OptionData, FIELD_TYPES, generateId } from './types';

interface FormBuilderPropertiesProps {
  form: FormData;
  selectedElement: FormElement | null;
  selectedElementType: 'form' | 'field' | 'section' | null;
  onFormChange: (form: FormData) => void;
  onElementChange: (element: FormElement) => void;
  className?: string;
}

const FormBuilderProperties: React.FC<FormBuilderPropertiesProps> = ({
  form,
  selectedElement,
  selectedElementType,
  onFormChange,
  onElementChange,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<'basic' | 'constraints' | 'options' | 'actions'>('basic');

  const renderFormProperties = () => (
    <div>
      <h3 style={{ marginBottom: '16px', fontSize: '18px', fontWeight: 'bold' }}>
        Form Properties
      </h3>
      
      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
          Name *
        </label>
        <input
          type="text"
          value={form.name}
          onChange={(e) => onFormChange({ ...form, name: e.target.value })}
          style={{
            width: '100%',
            padding: '8px',
            border: '1px solid #dee2e6',
            borderRadius: '4px'
          }}
        />
      </div>

      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
          Description
        </label>
        <textarea
          value={form.description}
          onChange={(e) => onFormChange({ ...form, description: e.target.value })}
          rows={3}
          style={{
            width: '100%',
            padding: '8px',
            border: '1px solid #dee2e6',
            borderRadius: '4px',
            resize: 'vertical'
          }}
        />
      </div>

      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
          Active At
        </label>
        <input
          type="datetime-local"
          value={form.active_at || ''}
          onChange={(e) => onFormChange({ ...form, active_at: e.target.value || null })}
          style={{
            width: '100%',
            padding: '8px',
            border: '1px solid #dee2e6',
            borderRadius: '4px'
          }}
        />
      </div>

      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
          Deactivate At
        </label>
        <input
          type="datetime-local"
          value={form.deactivate_at || ''}
          onChange={(e) => onFormChange({ ...form, deactivate_at: e.target.value || null })}
          style={{
            width: '100%',
            padding: '8px',
            border: '1px solid #dee2e6',
            borderRadius: '4px'
          }}
        />
      </div>
    </div>
  );

  const renderSectionProperties = () => {
    const section = selectedElement as FormSectionData;
    
    return (
      <div>
        <h3 style={{ marginBottom: '16px', fontSize: '18px', fontWeight: 'bold' }}>
          Section Properties
        </h3>
        
        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
            Label *
          </label>
          <input
            type="text"
            value={section.label}
            onChange={(e) => onElementChange({ ...section, label: e.target.value })}
            style={{
              width: '100%',
              padding: '8px',
              border: '1px solid #dee2e6',
              borderRadius: '4px'
            }}
          />
        </div>

        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
            Description
          </label>
          <textarea
            value={section.description}
            onChange={(e) => onElementChange({ ...section, description: e.target.value })}
            rows={3}
            style={{
              width: '100%',
              padding: '8px',
              border: '1px solid #dee2e6',
              borderRadius: '4px',
              resize: 'vertical'
            }}
          />
        </div>
      </div>
    );
  };

  const renderFieldProperties = () => {
    const field = selectedElement as FormFieldData;
    
    const renderBasicTab = () => (
      <div>
        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
            Label *
          </label>
          <input
            type="text"
            value={field.label}
            onChange={(e) => onElementChange({ ...field, label: e.target.value })}
            style={{
              width: '100%',
              padding: '8px',
              border: '1px solid #dee2e6',
              borderRadius: '4px'
            }}
          />
        </div>

        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
            Type
          </label>
          <select
            value={field.type}
            onChange={(e) => onElementChange({ ...field, type: e.target.value })}
            style={{
              width: '100%',
              padding: '8px',
              border: '1px solid #dee2e6',
              borderRadius: '4px'
            }}
          >
            {FIELD_TYPES.map(type => (
              <option key={type} value={type}>
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </option>
            ))}
          </select>
        </div>

        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
            Help Text
          </label>
          <input
            type="text"
            value={field.help_text || ''}
            onChange={(e) => onElementChange({ ...field, help_text: e.target.value || null })}
            style={{
              width: '100%',
              padding: '8px',
              border: '1px solid #dee2e6',
              borderRadius: '4px'
            }}
          />
        </div>

        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <input
              type="checkbox"
              checked={field.required}
              onChange={(e) => onElementChange({ ...field, required: e.target.checked })}
            />
            <span style={{ fontWeight: 'bold' }}>Required</span>
          </label>
        </div>
      </div>
    );

    const renderConstraintsTab = () => (
      <div>
        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
            Minimum Length
          </label>
          <input
            type="number"
            value={field.constraints.min_length || ''}
            onChange={(e) => onElementChange({
              ...field,
              constraints: {
                ...field.constraints,
                min_length: e.target.value ? parseInt(e.target.value) : null
              }
            })}
            style={{
              width: '100%',
              padding: '8px',
              border: '1px solid #dee2e6',
              borderRadius: '4px'
            }}
          />
        </div>

        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
            Maximum Length
          </label>
          <input
            type="number"
            value={field.constraints.max_length || ''}
            onChange={(e) => onElementChange({
              ...field,
              constraints: {
                ...field.constraints,
                max_length: e.target.value ? parseInt(e.target.value) : null
              }
            })}
            style={{
              width: '100%',
              padding: '8px',
              border: '1px solid #dee2e6',
              borderRadius: '4px'
            }}
          />
        </div>

        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <input
              type="checkbox"
              checked={field.constraints.allow_multiple}
              onChange={(e) => onElementChange({
                ...field,
                constraints: {
                  ...field.constraints,
                  allow_multiple: e.target.checked
                }
              })}
            />
            <span style={{ fontWeight: 'bold' }}>Allow Multiple Values</span>
          </label>
        </div>

        {field.constraints.allow_multiple && (
          <>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
                Minimum Count
              </label>
              <input
                type="number"
                value={field.constraints.min_count}
                onChange={(e) => onElementChange({
                  ...field,
                  constraints: {
                    ...field.constraints,
                    min_count: parseInt(e.target.value) || 0
                  }
                })}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #dee2e6',
                  borderRadius: '4px'
                }}
              />
            </div>

            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
                Maximum Count
              </label>
              <input
                type="number"
                value={field.constraints.max_count || ''}
                onChange={(e) => onElementChange({
                  ...field,
                  constraints: {
                    ...field.constraints,
                    max_count: e.target.value ? parseInt(e.target.value) : null
                  }
                })}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #dee2e6',
                  borderRadius: '4px'
                }}
              />
            </div>
          </>
        )}
      </div>
    );

    return (
      <div>
        <h3 style={{ marginBottom: '16px', fontSize: '18px', fontWeight: 'bold' }}>
          Field Properties
        </h3>
        
        <div style={{ marginBottom: '16px' }}>
          <div style={{ display: 'flex', borderBottom: '1px solid #dee2e6' }}>
            <button
              onClick={() => setActiveTab('basic')}
              style={{
                padding: '8px 16px',
                border: 'none',
                background: activeTab === 'basic' ? '#007bff' : 'transparent',
                color: activeTab === 'basic' ? 'white' : '#007bff',
                cursor: 'pointer',
                borderRadius: '4px 4px 0 0'
              }}
            >
              Basic
            </button>
            <button
              onClick={() => setActiveTab('constraints')}
              style={{
                padding: '8px 16px',
                border: 'none',
                background: activeTab === 'constraints' ? '#007bff' : 'transparent',
                color: activeTab === 'constraints' ? 'white' : '#007bff',
                cursor: 'pointer',
                borderRadius: '4px 4px 0 0'
              }}
            >
              Constraints
            </button>
            {(['select', 'radio'].includes(field.type)) && (
              <button
                onClick={() => setActiveTab('options')}
                style={{
                  padding: '8px 16px',
                  border: 'none',
                  background: activeTab === 'options' ? '#007bff' : 'transparent',
                  color: activeTab === 'options' ? 'white' : '#007bff',
                  cursor: 'pointer',
                  borderRadius: '4px 4px 0 0'
                }}
              >
                Options
              </button>
            )}
          </div>
        </div>

        {activeTab === 'basic' && renderBasicTab()}
        {activeTab === 'constraints' && renderConstraintsTab()}
        {activeTab === 'options' && (['select', 'radio'].includes(field.type)) && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                <span style={{ fontWeight: 'bold' }}>Options</span>
                <button
                  onClick={() => {
                    const newOption: OptionData = {
                      id: generateId(),
                      label: 'New Option',
                      value: 'new_option',
                      active: true,
                      ordinal: field.options.length,
                      subscriptions: {},
                      _meta: {
                        created_at: new Date().toISOString(),
                        created_by: 'builder-user',
                        updated_at: null,
                        updated_by: null,
                        deleted_at: null
                      }
                    };
                    onElementChange({
                      ...field,
                      options: [...field.options, newOption]
                    });
                  }}
                  style={{
                    padding: '4px 8px',
                    backgroundColor: '#007bff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Add Option
                </button>
              </div>
              
              {field.options.map((option, index) => (
                <div key={option.id} style={{ 
                  display: 'flex', 
                  gap: '8px', 
                  marginBottom: '8px',
                  alignItems: 'center'
                }}>
                  <input
                    type="text"
                    value={option.label}
                    onChange={(e) => {
                      const newOptions = [...field.options];
                      newOptions[index] = { ...option, label: e.target.value };
                      onElementChange({ ...field, options: newOptions });
                    }}
                    placeholder="Label"
                    style={{
                      flex: 1,
                      padding: '4px 8px',
                      border: '1px solid #dee2e6',
                      borderRadius: '4px',
                      fontSize: '14px'
                    }}
                  />
                  <input
                    type="text"
                    value={option.value}
                    onChange={(e) => {
                      const newOptions = [...field.options];
                      newOptions[index] = { ...option, value: e.target.value };
                      onElementChange({ ...field, options: newOptions });
                    }}
                    placeholder="Value"
                    style={{
                      flex: 1,
                      padding: '4px 8px',
                      border: '1px solid #dee2e6',
                      borderRadius: '4px',
                      fontSize: '14px'
                    }}
                  />
                  <button
                    onClick={() => {
                      const newOptions = field.options.filter((_, i) => i !== index);
                      onElementChange({ ...field, options: newOptions });
                    }}
                    style={{
                      padding: '4px',
                      backgroundColor: '#dc3545',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '12px'
                    }}
                  >
                    <i className="fas fa-trash"></i>
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`form-builder-properties ${className}`} style={{
      width: '320px',
      height: '100%',
      backgroundColor: '#ffffff',
      border: '1px solid #dee2e6',
      borderRadius: '4px',
      padding: '16px',
      overflowY: 'auto'
    }}>
      {selectedElementType === 'form' && renderFormProperties()}
      {selectedElementType === 'section' && selectedElement && renderSectionProperties()}
      {selectedElementType === 'field' && selectedElement && renderFieldProperties()}
      
      {!selectedElementType && (
        <div style={{ textAlign: 'center', color: '#6c757d', padding: '48px 16px' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>⚙️</div>
          <div>Select a form element to edit its properties</div>
        </div>
      )}
    </div>
  );
};

export default FormBuilderProperties;
