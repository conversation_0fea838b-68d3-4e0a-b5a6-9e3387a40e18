import * as React from 'react';
import { useDrag } from 'react-dnd';
import { PaletteItem, DragItem } from './types';

interface FormBuilderPaletteProps {
  className?: string;
}

const PALETTE_ITEMS: PaletteItem[] = [
  // Section
  {
    id: 'section',
    type: 'section',
    label: 'Section',
    icon: 'fas fa-folder',
    description: 'Group related fields together'
  },
  
  // Text inputs
  {
    id: 'text',
    type: 'field',
    fieldType: 'text',
    label: 'Text',
    icon: 'fas fa-font',
    description: 'Single line text input'
  },
  {
    id: 'textarea',
    type: 'field',
    fieldType: 'textarea',
    label: 'Textarea',
    icon: 'fas fa-align-left',
    description: 'Multi-line text input'
  },
  {
    id: 'email',
    type: 'field',
    fieldType: 'email',
    label: 'Email',
    icon: 'fas fa-envelope',
    description: 'Email address input'
  },
  {
    id: 'password',
    type: 'field',
    fieldType: 'password',
    label: 'Password',
    icon: 'fas fa-lock',
    description: 'Password input field'
  },
  {
    id: 'number',
    type: 'field',
    fieldType: 'number',
    label: 'Number',
    icon: 'fas fa-hashtag',
    description: 'Numeric input field'
  },
  {
    id: 'url',
    type: 'field',
    fieldType: 'url',
    label: 'URL',
    icon: 'fas fa-link',
    description: 'URL input field'
  },
  
  // Selection inputs
  {
    id: 'select',
    type: 'field',
    fieldType: 'select',
    label: 'Select',
    icon: 'fas fa-caret-down',
    description: 'Dropdown selection'
  },
  {
    id: 'radio',
    type: 'field',
    fieldType: 'radio',
    label: 'Radio',
    icon: 'fas fa-dot-circle',
    description: 'Single choice from options'
  },
  {
    id: 'checkbox',
    type: 'field',
    fieldType: 'checkbox',
    label: 'Checkbox',
    icon: 'fas fa-check-square',
    description: 'Boolean checkbox'
  },
  
  // Date/Time inputs
  {
    id: 'date',
    type: 'field',
    fieldType: 'date',
    label: 'Date',
    icon: 'fas fa-calendar',
    description: 'Date picker'
  },
  {
    id: 'time',
    type: 'field',
    fieldType: 'time',
    label: 'Time',
    icon: 'fas fa-clock',
    description: 'Time picker'
  },
  
  // File input
  {
    id: 'file',
    type: 'field',
    fieldType: 'file',
    label: 'File',
    icon: 'fas fa-paperclip',
    description: 'File upload field'
  }
];

interface PaletteItemComponentProps {
  item: PaletteItem;
}

const PaletteItemComponent: React.FC<PaletteItemComponentProps> = ({ item }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'palette-item',
    item: {
      type: 'palette-item',
      item: item
    } as DragItem,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  return (
    <div
      ref={drag}
      className={`palette-item ${isDragging ? 'dragging' : ''}`}
      style={{
        opacity: isDragging ? 0.5 : 1,
        cursor: 'grab',
        padding: '8px 12px',
        margin: '2px 0',
        backgroundColor: '#f8f9fa',
        border: '1px solid #dee2e6',
        borderRadius: '4px',
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        transition: 'all 0.2s ease',
        fontSize: '14px'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#e9ecef';
        e.currentTarget.style.borderColor = '#adb5bd';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = '#f8f9fa';
        e.currentTarget.style.borderColor = '#dee2e6';
      }}
    >
      <i className={item.icon} style={{ width: '16px', textAlign: 'center', color: '#6c757d' }}></i>
      <span style={{ fontWeight: '500' }}>{item.label}</span>
    </div>
  );
};

const FormBuilderPalette: React.FC<FormBuilderPaletteProps> = ({ className = '' }) => {
  const sectionItems = PALETTE_ITEMS.filter(item => item.type === 'section');
  const fieldItems = PALETTE_ITEMS.filter(item => item.type === 'field');

  return (
    <div className={`form-builder-palette ${className}`} style={{
      height: '100%',
      overflowY: 'auto',
      padding: '8px'
    }}>
      <div style={{ marginBottom: '16px' }}>
        <h4 style={{ 
          margin: '0 0 8px 0', 
          fontSize: '12px', 
          fontWeight: 'bold',
          color: '#6c757d',
          textTransform: 'uppercase',
          letterSpacing: '0.5px'
        }}>
          Layout
        </h4>
        {sectionItems.map(item => (
          <PaletteItemComponent key={item.id} item={item} />
        ))}
      </div>

      <div>
        <h4 style={{ 
          margin: '0 0 8px 0', 
          fontSize: '12px', 
          fontWeight: 'bold',
          color: '#6c757d',
          textTransform: 'uppercase',
          letterSpacing: '0.5px'
        }}>
          Fields
        </h4>
        {fieldItems.map(item => (
          <PaletteItemComponent key={item.id} item={item} />
        ))}
      </div>
    </div>
  );
};

export default FormBuilderPalette;
