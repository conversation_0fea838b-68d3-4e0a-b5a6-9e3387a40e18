import * as React from 'react';
import { useState, useCallback } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { FormData, FormElement, BuilderState, generateId, getCurrentTimestamp } from './types';
import FormBuilderPalette from './FormBuilderPalette';
import FormBuilderGrid from './FormBuilderGrid';
import FormBuilderProperties from './FormBuilderProperties';
import FormBuilderToolbar from './FormBuilderToolbar';

interface FormBuilderProps {
  initialForm?: FormData;
  onSave?: (form: FormData) => void;
  onLoad?: () => Promise<FormData>;
  onPreview?: (form: FormData) => void;
  onTest?: (form: FormData) => void;
  className?: string;
}

const createEmptyForm = (): FormData => ({
  id: generateId(),
  name: 'New Form',
  description: '',
  active_at: null,
  deactivate_at: null,
  elements: [],
  actions: [],
  _meta: {
    created_at: getCurrentTimestamp(),
    created_by: 'builder-user',
    updated_at: null,
    updated_by: null,
    deleted_at: null
  }
});

const FormBuilder: React.FC<FormBuilderProps> = ({
  initialForm,
  onSave,
  onLoad,
  onPreview,
  onTest,
  className = ''
}) => {
  const [builderState, setBuilderState] = useState<BuilderState>({
    form: initialForm || createEmptyForm(),
    selectedElement: null,
    selectedElementType: null,
    isDragging: false,
    dragItem: null,
    previewMode: false,
    testMode: false
  });

  const handleFormChange = useCallback((form: FormData) => {
    setBuilderState(prev => ({
      ...prev,
      form: {
        ...form,
        _meta: {
          ...form._meta,
          updated_at: getCurrentTimestamp()
        }
      }
    }));
  }, []);

  const handleElementSelect = useCallback((element: FormElement | null, type: 'form' | 'field' | 'section' | null) => {
    setBuilderState(prev => ({
      ...prev,
      selectedElement: element,
      selectedElementType: type
    }));
  }, []);

  const handleElementChange = useCallback((updatedElement: FormElement) => {
    const elementIndex = builderState.form.elements.findIndex(el => el.id === updatedElement.id);
    if (elementIndex !== -1) {
      const newElements = [...builderState.form.elements];
      newElements[elementIndex] = updatedElement;
      
      handleFormChange({
        ...builderState.form,
        elements: newElements
      });

      setBuilderState(prev => ({
        ...prev,
        selectedElement: updatedElement
      }));
    }
  }, [builderState.form, handleFormChange]);

  const generateFormJson = useCallback((): FormData => {
    // Generate ordinals for elements
    const elementsWithOrdinals = builderState.form.elements.map((element, index) => ({
      ...element,
      ordinal: index
    }));

    return {
      ...builderState.form,
      elements: elementsWithOrdinals
    };
  }, [builderState.form]);

  const handleSave = useCallback(() => {
    const formToSave = generateFormJson();
    if (onSave) {
      onSave(formToSave);
    } else {
      // Default save behavior - could save to localStorage or show JSON
      console.log('Form JSON:', JSON.stringify(formToSave, null, 2));
      alert('Form saved! Check console for JSON output.');
    }
  }, [generateFormJson, onSave]);

  const handleLoad = useCallback(async () => {
    if (onLoad) {
      try {
        const loadedForm = await onLoad();
        setBuilderState(prev => ({
          ...prev,
          form: loadedForm,
          selectedElement: null,
          selectedElementType: null
        }));
      } catch (error) {
        console.error('Failed to load form:', error);
        alert('Failed to load form');
      }
    }
  }, [onLoad]);

  const handlePreview = useCallback(() => {
    const formToPreview = generateFormJson();
    if (onPreview) {
      onPreview(formToPreview);
    } else {
      setBuilderState(prev => ({
        ...prev,
        previewMode: !prev.previewMode
      }));
    }
  }, [generateFormJson, onPreview]);

  const handleTest = useCallback(() => {
    const formToTest = generateFormJson();
    if (onTest) {
      onTest(formToTest);
    } else {
      setBuilderState(prev => ({
        ...prev,
        testMode: !prev.testMode
      }));
    }
  }, [generateFormJson, onTest]);

  const handleNewForm = useCallback(() => {
    if (confirm('Create a new form? This will clear the current form.')) {
      setBuilderState({
        form: createEmptyForm(),
        selectedElement: null,
        selectedElementType: null,
        isDragging: false,
        dragItem: null,
        previewMode: false,
        testMode: false
      });
    }
  }, []);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className={`form-builder ${className}`} style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
        backgroundColor: '#f8f9fa'
      }}>
        {/* Toolbar */}
        <FormBuilderToolbar
          onSave={handleSave}
          onLoad={onLoad ? handleLoad : undefined}
          onPreview={handlePreview}
          onTest={handleTest}
          onNew={handleNewForm}
          previewMode={builderState.previewMode}
          testMode={builderState.testMode}
        />

        {/* Main content area */}
        <div style={{
          display: 'flex',
          flex: 1,
          gap: '16px',
          padding: '16px',
          overflow: 'hidden'
        }}>
          {/* Palette */}
          <FormBuilderPalette />

          {/* Grid/Canvas */}
          <FormBuilderGrid
            form={builderState.form}
            selectedElement={builderState.selectedElement}
            onFormChange={handleFormChange}
            onElementSelect={handleElementSelect}
          />

          {/* Properties Panel */}
          <FormBuilderProperties
            form={builderState.form}
            selectedElement={builderState.selectedElement}
            selectedElementType={builderState.selectedElementType}
            onFormChange={handleFormChange}
            onElementChange={handleElementChange}
          />
        </div>
      </div>
    </DndProvider>
  );
};

export default FormBuilder;
