import * as React from 'react';
import FormField from './FormField';

interface FormSectionProps {
  section: {
    id: string;
    label: string;
    description: string;
    fields: Array<FormFieldData>;
  };
  formValues: Record<string, any>;
  visibleElements: Record<string, boolean>;
  elementAttributes: Record<string, Record<string, any>>;
  onFieldChange: (fieldId: string, value: any) => void;
  onMultipleValuesChange: (fieldId: string, index: number, value: any) => void;
  onAddMultipleValue: (fieldId: string) => void;
  onRemoveMultipleValue: (fieldId: string, index: number) => void;
}

interface FormFieldData {
  id: string;
  label: string;
  help_text: string | null;
  type: string;
  required: boolean;
  constraints: {
    min_length: number | null;
    max_length: number | null;
    allow_multiple: boolean;
    min_count: number;
    max_count: number | null;
    accessibility: string;
  };
  ordinal: number;
  options: any[];
  subscriptions: Record<string, any>;
}

const FormSection: React.FC<FormSectionProps> = ({
  section,
  formValues,
  visibleElements,
  elementAttributes,
  onFieldChange,
  onMultipleValuesChange,
  onAddMultipleValue,
  onRemoveMultipleValue
}) => {
  // Sort fields by ordinal
  const sortedFields = [...section.fields].sort((a, b) => a.ordinal - b.ordinal);

  return (
    <div className="frm-row">
      <div className="frm-section" id={section.id}>
        <div className="frm-section-label">{section.label}</div>
        {section.description && <div className="frm-section-description">{section.description}</div>}

        {sortedFields.map((field) => (
          visibleElements[field.id] && (
            <FormField
              key={field.id}
              field={field}
              value={formValues[field.id]}
              attributes={elementAttributes[field.id] || {}}
              onChange={(value) => onFieldChange(field.id, value)}
              onMultipleValuesChange={(index, value) => onMultipleValuesChange(field.id, index, value)}
              onAddMultipleValue={() => onAddMultipleValue(field.id)}
              onRemoveMultipleValue={(index) => onRemoveMultipleValue(field.id, index)}
            />
          )
        ))}
      </div>
    </div>
  );
};

export default FormSection;
