{% extends 'layout/auth.base.html.twig' %}

{% set isGroup = false %}

{% if group is defined %}
    {% set isGroup = true %}
{%  endif %}

{% if organization.orgContext == 'club'%}
    {% set context = 'Club' %}
{% elseif organization.orgContext == 'group' %}
    {% set context = 'Group' %}
{% endif %}

{% block cover %}
    <div id="cover-container">
        {% include 'components/cover/cover.html.twig' with {'user':app.user} %}
    </div>
{% endblock %}

{% block leftCol %}
    {% include 'organization/components/navigation/left_col.nav.html.twig' with {'organization': organization} %}

    {% if organization.parentOrganization %}
        <div class="content-box">
            <h1>{{ 'Parent Club'|trans }}</h1>
            <div>
                <a class="feed-link" href="{{ path('organization:index', {organization:organization.parentOrganization.slug}) }}">
                    <div class="row feed" id="club-{{ organization.parentOrganization.id }}">
                        {% include 'components/avatar.html.twig' with {avatar: organization.parentOrganization.avatar, type:'feed', size:32, initials:organization.parentOrganization.name[:1]} %}
                        <div class="feed-title stretch">
                            {{ organization.parentOrganization.name }}
                        </div>
                    </div>
                </a>
            </div>
        </div>
    {% else %}
        {% if resolve_setting('enable-club-groups', organization) %}
            {% include 'organization/components/group-list.html.twig' with {groups:organization.groups} %}
        {% endif %}
    {% endif %}

    {% set resourcemorepath = path('club:resources',{'organization':organization.slug}) %}
    {% if (strip_resource_slug(currpath) != resourcemorepath) and ((is_granted('ROLE_SUPER_ADMIN') and is_granted('PERMISSION:ADMIN:ORG:ACCESS')) or organization.meta.isUserMember or (organization.meta.isUserContact and organization.meta.isUserContactSuspended == false)) %}
        {% if sidebar.clubLinks is not empty %}
            <div class="content-box">
                <div  class="grid grid-cols-2 grid-flow-col">
                    <div>
                        <h1>{{ context~' Resources'|trans }}</h1>
                    </div>
                    <div>
                        <a href="{{ resourcemorepath }}" class="block hover:bg-lightBlue rounded-md overflow-hidden" style="display:inline-block;">
                            <div class="flex-grow flex px-4 items-center">
                                <div class="flex-shrink-0 h-8 w-6 justify-center align-middle flex flex-row"></div>
                                <div class="h-8 justify-center align-middle flex flex-row ml-2 capitalize font-medium text-sm text-right text-gray-800 items-center">
                                    View More <i class="ml-1 fas fa-angle-right"></i>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <ul class="organization-profile-menu left-col-menu">
                {% set linkcount = 0 %}
                {% for clubLinkCategory in sidebar.clubLinks %}
                    <div>
                        <a href="{{ resourcemorepath }}/{{ str_slug(clubLinkCategory.name) }}" 
                           title="View The {{ clubLinkCategory.name }} Resource Category">
                            <strong class="underline">{{ clubLinkCategory.name }}</strong>
                        </a>
                    </div>
                    {% for clubLink in clubLinkCategory.links %}
                        <a href="{{ clubLink.url }}" target="_blank" title="Open {{ clubLink.name }} In A New Tab"><li>{{ clubLink.name }}</li></a>
                        {% set linkcount = linkcount + 1 %}
                    {% endfor %}
                {% endfor %}
                </ul>
                {% if sidebar.clubLinks|length >= 2 or linkcount > 3 %}
                <a href="{{ resourcemorepath }}" class="block hover:bg-lightBlue rounded-md overflow-hidden" style="">
                    <div class="flex-grow flex px-4 items-center">
                        <div class="flex-shrink-0 h-8 w-14 justify-center align-middle flex flex-row"></div>
                        <div class="flex-grow ml-2 capitalize font-medium text-sm text-right text-gray-800 items-center">
                            View More <i class="fas fa-angle-right"></i>
                        </div>
                    </div>
                </a>
                {% endif %}
            </div>
        {% endif %}
    {% endif %}

    {% if organization.facilities is not empty and resolve_setting('enable-club-facilites', organization) %}
        <div class="content-box">
            <h1>{{ context~' Facilities'|trans }}</h1>
            <ul class="organization-profile-menu left-col-menu">
            {% for facility in organization.facilities %}
                <a href="{{ path('club:index',{'organization':organization.slug}) }}"><li>{{ facility.name }}</li></a>
            {% endfor %}
            </ul>
        </div>
    {% endif %}
{% endblock %}

{% block rightCol %}
    {% if organization.meta.isUserInvited and organization.meta.isUserSubscribed == false  %}
        <div class="info-block margin-bottom-large">
            <strong>You've been invited to follow {{ organization.name }} as a member on Clubster!</strong>
            <a href="{{ path('organization:follow', {'organization':organization.id}) }}" class="clubster-button margin-top-large">Follow {{ organization.name }}</a>
        </div>
    {% endif %}
    {{ parent() }}
{% endblock rightCol %}

{% block body %}
    {% if userHasAccess is defined and userHasAccess == false %}
        <div id="full-col">
            <div class="content-box">
                <p id="base" style="text-align: center">
                    <strong>{{ organization.name }}</strong> is a private {{ context }}. This {{ context }} can only be accessed by invitation.
                </p>
            </div>
        </div>
    {% else %}
        {% if app.user is not empty %}
            <!-- left menu is sticky -->
            <div id="left-col" {% if stickypage == true %} class="sticky" style="top:{{stickytopleft}};z-index:4000;"{% endif %}>
                {{ block('leftCol') }}
            </div>
            <div id="right-col">
                {{ block('rightCol') }}
            </div>
        {% else %}
            <div style="flex-grow:1;max-width:75%;margin:auto;">
            {% if _route is defined and _route != 'invitation:decline' and _route != 'notification:unsubscribe' %}
                <div class="info-block margin-bottom-large">
                    <div class="margin-bottom-medium">
                        <p style="font-weight: bold">You are not logged in. Please login if you have a Clubster account, or sign up for free!</p>
                    </div>
                    <div class="row margin-bottom-medium" style="max-width: 960px; margin: auto; flex-wrap: wrap">
                        <a title="Login At Clubster" class="clubster-button margin-bottom-small" style="max-width: 400px; margin: auto" href="{{ path('login') }}">Login</a>
                        <a title="Sign Up On Clubster" class="clubster-button margin-bottom-small" style="max-width: 400px; margin: auto" href="{{ path('register') }}">Sign Up</a>
                    </div>
                    <div id="club-info" class="margin-bottom-medium" style="display:none;" >
                        <p style="font-weight:bold">Have questions or need to contact {{ organization.name }}?</p>
                        <p id="club-phone-msg"  style="font-weight:bold;display:none;">You can call them at <a title="Make A Call To {{ organization.name }}" id="club-phone" class="underline underline-offset-4" href=""></a></p>
                        <p id="club-email-msg"  style="font-weight:bold;display:none;">You can email them at <a title="Send Email To {{ organization.name }}" id="club-email" class="underline underline-offset-4" href=""></a></p>
                        <p id="club-website-msg"  style="font-weight:bold;display:none;">You can visit their website at <a target="_blank" title="Visit Their Website {{ organization.name }}" id="club-website" class="underline underline-offset-4" href=""></a></p>
                    </div>
                </div>
                <script>
                    $(function() {
                        function formatPhoneNumberToE164(phone, humanReadable = true) {
                            const cleanNumber = phone.replace(/\D/g, '');
                            if(cleanNumber.length !== 10) {
                                return phone;
                            }
                            const e164Number = '+1' + cleanNumber;
                            if(!humanReadable) {
                                return e164Number;
                            }
                            return e164Number.replace(/(\+1)? ?\(?([\d]{3})\)?[-. ]?([\d]{3})[-. ]?([\d]{4})\b/, '$1 ($2) $3-$4');
                        }
                        fetch("{{ path('index:clubinfo', {'orgid':organization.id}) }}")
                        .then(res => res.json())
                        .then( function(formatPhoneNumberToE164, data) {
                            if(data['primary-phone'].value.value !== null && data['primary-phone'].value.value.length >= 10) {
                                $('#club-phone').attr('href','tel:'+data['primary-phone'].value.value);
                                $('#club-phone').text(formatPhoneNumberToE164(data['primary-phone'].value.value));
                                $('#club-phone-msg').show();
                                $('#club-info').show();
                            } else {
                                 $('#club-phone-msg').hide();
                            }
                            if(data['primary-email'].value.value !== null && data['primary-email'].value.value.length >= 1) {
                                $('#club-email').attr('href','mailto:'+data['primary-email'].value.value);
                                $('#club-email').text(data['primary-email'].value.value);
                                $('#club-email-msg').show();
                                $('#club-info').show();
                            } else {
                                $('#club-email-msg').hide();
                            }
                            if(data['website'].value.value !== null && data['website'].value.value.length >= 1) {
                                $('#club-website').attr('href',data['website'].value.value);
                                $('#club-website').text(data['website'].value.value);
                                $('#club-website-msg').show();
                                $('#club-info').show();
                            } else {
                                $('#club-email-msg').hide();
                            }
                            if($('#club-phone-msg').is(':hidden') && $('#club-email-msg').is(':hidden') && $('#club-website-msg').is(':hidden')) {
                                $('#club-info').hide();
                            } else {
                                $('#club-info').show();
                            }
                        }.bind(this, formatPhoneNumberToE164));
                    });
                </script>
            {% endif %}
                {{ block('rightCol') }}
            </div>
        {% endif %}
    {% endif %}
{% endblock %}
