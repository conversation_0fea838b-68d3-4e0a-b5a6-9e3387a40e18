<div id="group-{{ group.id }}" 
     x-cloak
{% if group.orientation == 'public' %}
     x-show="showOpen"
{% else %}
     x-show="showPrivate"
{% endif %}
     class="row horizontal-center">
    <div class="stretch">
        <a class="feed-link" href="{{ path('organization:index', {organization:group.slug}) }}">
            <div class="row feed">
                {% if group.parentOrganization is not empty %}
                    {% include 'components/avatar.html.twig' with {avatar: group.parentOrganization.avatar, type:'club', size:32, initials:group.parentOrganization.name[:1]} %}
                {% endif%}

                {% include 'components/avatar.html.twig' with {avatar: group.avatar, type:'feed', size:32, initials: group.name[:1]} %}

                <div class="feed-title-left stretch">
                    <div>{{ group.name }}</div>
                    {% if group.parentOrganization is not empty %}
                        <div class="subtext margin-left-small">{{ group.parentOrganization.name }}</div>
                    {% endif %}
                </div>
            </div>
        </a>
    </div>
    <div class="text-right" style="width:7rem;">
    {% if group.orientation == 'public' %}
        <span class="label-orgcontext-open" style="top:0px;"><i class="fas fa-lock-open"></i> Open</span>
    {% else %}
        <span class="label-orgcontext-private" style="top:0px;"><i class="fas fa-lock"></i> Private</span>
    {% endif %}
    </div>
    <div class="text-right mx-4" style="width:7rem;">
        <div class="label-orgcontext-open" style="top:0px;">{{ group.memberCount }} Members</div>
    </div>
    <div class="text-right w-40">
        <div class="split-button-group">
        {% if group.meta.isUserContact and (is_granted("PERMISSION:ORG:ADMINISTRATION:#{group.id}") or is_granted("PERMISSION:ORG:GROUPS:EDIT:#{group.id}") or is_granted("PERMISSION:ORG:POST:CREATE:#{group.id}") or is_granted("PERMISSION:ORG:POST:EDIT:ALL:#{group.id}")) and group.meta.isUserContactSuspended == false %}
            <a class="clubster-button small hollow" href="{{ path('organization:admin:index', {organization:group.slug}) }}">Manage</a>
        {% endif %}
        {% if group.meta.isUserMember or 
            (group.parentOrganization and 
                (group.parentOrganization.meta.isUserMember or (group.parentOrganization.meta.isUserContact and not group.meta.isUserContact))
            ) 
        %}
            {% if group.meta.isUserSubscribed %}
            <a class="clubster-button small hollow" href="{{ path('organization:unfollow', {organization:group.slug}) }}">Unfollow</a>
            {% else %}
            <a class="clubster-button small hollow" href="{{ path('organization:follow', {organization:group.slug}) }}">Follow</a>
            {% endif %}
        {% endif %}
        </div>
    </div>
</div>
 