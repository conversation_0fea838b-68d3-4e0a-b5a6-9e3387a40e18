{% extends 'organization/components/org.base.html.twig' %}
{% block stylesheets %}
    {{ parent() }}
    <style>
        #recurring-tab-group sl-tab::part(base) {
            padding-top: .25rem;
            padding-bottom: .25rem;
        }
    </style>
{% endblock stylesheets %}

{% block rightCol %}
    {{ parent() }}
    <iframe src="about:blank" style="display:none" name="blank_iframe"></iframe>
    <script src="{{ asset('assets/tinymce/tinymce.js') }}"></script>
    <script src="{{ asset('assets/js/post-utility.js') }}"></script>
    <form action="about:blank" target="blank_iframe">
        <div class="content-box">
            <div id="datadiv" class="event-form" x-data="PostUtility.data"
                 x-init="
                    timezone.load;
                    endpoint='';
                    post_type='event';

                    $watch('recurring_settings.enabled', (value) => validateRecurring());
                    $watch('recurring_settings.weekly.byday', (value) => validateRecurring());
                    $watch('recurring_settings.continuance.type', (value) => validateRecurring());
                    $watch('recurring_settings.continuance.until', (value) => validateRecurring());
                    $watch('recurring_settings.frequency', (value) => validateRecurring());

                    $watch('event_schedule.start_date', (value) => event_schedule.bad_dates = (event_schedule.end_date || event_schedule.end_time)?event_schedule.build_moment(event_schedule.start_date, event_schedule.start_time, event_schedule.all_day).isSameOrAfter(event_schedule.build_moment(event_schedule.end_date??event_schedule.start_date, event_schedule.end_time, event_schedule.all_day)):false);
                    $watch('event_schedule.start_date', (value) => !value ? recurring_settings.enabled = rsvp_settings.enabled = false : false);
                    $watch('event_schedule.start_time', (value) => event_schedule.bad_dates = (event_schedule.end_date || event_schedule.end_time)?event_schedule.build_moment(event_schedule.start_date, event_schedule.start_time, event_schedule.all_day).isSameOrAfter(event_schedule.build_moment(event_schedule.end_date??event_schedule.start_date, event_schedule.end_time, event_schedule.all_day)):false);
                    $watch('event_schedule.end_date', (value) => event_schedule.bad_dates = !event_schedule.all_day && (event_schedule.end_date || event_schedule.end_time)?event_schedule.build_moment(event_schedule.start_date, event_schedule.start_time, event_schedule.all_day).isSameOrAfter(event_schedule.build_moment(event_schedule.end_date??event_schedule.start_date, event_schedule.end_time, event_schedule.all_day)):false);
                    $watch('event_schedule.end_time', (value) => event_schedule.bad_dates = (event_schedule.end_date || event_schedule.end_time)?event_schedule.build_moment(event_schedule.start_date, event_schedule.start_time, event_schedule.all_day).isSameOrAfter(event_schedule.build_moment(event_schedule.end_date??event_schedule.start_date, event_schedule.end_time, event_schedule.all_day)):false);
                    $watch('event_schedule.all_day', (value) => event_schedule.bad_dates = !value && (event_schedule.end_date || event_schedule.end_time)?event_schedule.build_moment(event_schedule.start_date, event_schedule.start_time, event_schedule.all_day).isSameOrAfter(event_schedule.build_moment(event_schedule.end_date??event_schedule.start_date, event_schedule.end_time, event_schedule.all_day)):false);
                    $watch('event_schedule.end_time', (value) => event_schedule.bad_end = (!event_schedule.all_day?event_schedule.build_moment((!event_schedule.end_date?event_schedule.now_date:event_schedule.end_date), event_schedule.end_time, false).isSameOrBefore(event_schedule.build_moment(event_schedule.rightNowDate(), event_schedule.rightNowTime(), false)):event_schedule.bad_end));
                    $watch('event_schedule.end_date', (value) => event_schedule.bad_end = (event_schedule.end_time && !event_schedule.all_day?event_schedule.build_moment(event_schedule.end_date, event_schedule.end_time, false).isSameOrBefore(event_schedule.build_moment(event_schedule.rightNowDate(), event_schedule.rightNowTime(), false)):event_schedule.bad_end));
                    $watch('event_schedule.all_day',  (value) => event_schedule.bad_end = (value?false:event_schedule.build_moment(event_schedule.end_date, event_schedule.end_time, false).isSameOrBefore(event_schedule.build_moment(event_schedule.rightNowDate(), event_schedule.rightNowTime(), false))));
                    $watch('event_schedule.start_time',  (value) => event_schedule.bad_dates = (!value?false:(!event_schedule.end_time?false:event_schedule.build_moment((event_schedule.start_date?event_schedule.start_date:event_schedule.rightNowDate()), event_schedule.end_time, false).isSameOrBefore(event_schedule.build_moment((event_schedule.start_date?event_schedule.start_date:event_schedule.rightNowDate()), event_schedule.start_time, false)))));

                    $watch('recurring_settings.enabled', (value) => value ? validateRSVPIntervals() : validateRSVPDates());

                    $watch('rsvp_settings.schedule.open.enabled', (value) => recurring_settings.enabled ? validateRSVPIntervals() : validateRSVPDates());
                    $watch('rsvp_settings.schedule.open.interval', (value) => validateRSVPIntervals());
                    $watch('rsvp_settings.schedule.open.interval_type', (value) => validateRSVPIntervals());
                    $watch('rsvp_settings.schedule.open.date', (value) => validateRSVPDates());
                    $watch('rsvp_settings.schedule.open.time', (value) => validateRSVPDates());

                    $watch('rsvp_settings.schedule.close.enabled', (value) => recurring_settings.enabled ? validateRSVPIntervals() : validateRSVPDates());
                    $watch('rsvp_settings.schedule.close.interval', (value) => validateRSVPIntervals());
                    $watch('rsvp_settings.schedule.close.interval_type', (value) => validateRSVPIntervals());
                    $watch('rsvp_settings.schedule.close.date', (value) => validateRSVPDates());
                    $watch('rsvp_settings.schedule.close.time', (value) => validateRSVPDates());

                    $watch('event_schedule.start_time', (value) => recurring_settings.enabled ? validateRSVPIntervals() : validateRSVPDates());
                    $watch('event_schedule.end_time', (value) => recurring_settings.enabled ? validateRSVPIntervals() : validateRSVPDates());
                    $watch('event_schedule.start_date', (value) => recurring_settings.enabled ? validateRSVPIntervals() : validateRSVPDates());
                    $watch('event_schedule.end_date', (value) => recurring_settings.enabled ? validateRSVPIntervals() : validateRSVPDates());
                    $watch('event_schedule.start_date', (value) => validateRecurring());
                    {% if defaultTimezone??false %} timezone.selected = '{{ defaultTimezone }}'{% endif %}
                    ">
                {% include 'components/post-form/messages/basic-fields.html.twig' with {basicFieldsType:'event'} %}

                <div id="options-event-schedule" class="section2" x-bind:class="{'border-none':event_schedule.start_date, 'm-0':event_schedule.start_date}">
                    <div class="row2">
                        <div class="title2"></div>
                        <div id="options-event-schedule-checkboxes" class="row2">
                            <span class="check-button" data-state="" x-on:click="event_schedule.all_day = !event_schedule.all_day;" x-bind:data-state="event_schedule.all_day?'checked':'unchecked'">
                                <input type="checkbox" x-model="event_schedule.all_day">
                                <span>This Event is All Day</span>
                            </span>
                            <span class="check-button" disabled x-bind:disabled="!event_schedule.start_date" data-state="" x-on:click="event_schedule.start_date != null?recurring_settings.enabled = !recurring_settings.enabled:false" x-bind:data-state="recurring_settings.enabled?'checked':'unchecked'">
                                <input type="checkbox" disabled x-bind:disabled="!event_schedule.start_date" x-model="recurring_settings.enabled">
                                <span>This Event Repeats</span>
                            </span>
                            <span class="check-button" disabled x-bind:disabled="!event_schedule.start_date" data-state="" x-on:click="rsvp_settings.enabled = !rsvp_settings.enabled;" x-bind:data-state="rsvp_settings.enabled?'checked':'unchecked'">
                                <input type="checkbox" disabled x-bind:disabled="!event_schedule.start_date" x-model="rsvp_settings.enabled">
                                <span>Enable RSVPs</span>
                            </span>
                        </div>
                    </div>
                    <div class="row2" x-cloak x-show="event_schedule.bad_end">
                        <div class="title2"></div>
                        <div class="w-full">
                            <div class="callout error-callout small">This Event you are creating ends in the past. Please change your End Date or End Time.</div>
                        </div>
                    </div>
                    <div class="row2" x-cloak x-show="event_schedule.bad_dates">
                        <div class="title2"></div>
                        <div class="w-full">
                            <div class="callout error-callout small">End date must come after start date.</div>
                        </div>
                    </div>

                    <div class="row2">
                        <div class="title2"></div>
                        <div class="flex-grow column2">
                            <div id="options-event-schedule-dates-times" class="row2">
                                <div class="flex-grow" style="width: 50%">
                                    <div class="row2" x-bind:class="{'text-red-900':event_schedule.bad_dates}">
                                        <div class="flex-grow text-sm" style="width: 50%">Event Start Date<span class="text-red-700">*</span></div>
                                        <div class="flex-grow text-sm" x-show="!event_schedule.all_day" style="width: 50%">Event Start Time</div>
                                    </div>
                                    <div class="row2">
                                        <div class="input-container" x-bind:class="{'border-red-900':event_schedule.bad_dates}">
                                            <span class="flex-grow" style="width: 50%">
                                                <input type="date" class="w-full" 
                                                       x-model="event_schedule.start_date" 
                                                       x-bind:min="event_schedule.now_date" 
                                                       onkeydown="return false;" 
                                                       onclick="this.showPicker()">
                                            </span>
                                            <span class="flex-grow" x-show="!event_schedule.all_day" style="width: 50%">
                                                <input type="time" class="w-full" 
                                                       x-model="event_schedule.start_time" 
                                                       x-bind:min="event_schedule.start_date === event_schedule.now_date?event_schedule.now_time:''">
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex-grow" style="width: 50%">
                                    <div class="row2" x-bind:class="{'text-red-900':event_schedule.bad_dates}">
                                        <div class="flex-grow text-sm" style="width: 50%">Event End Date</div>
                                        <div class="flex-grow text-sm" x-show="!event_schedule.all_day" style="width: 50%">Event End Time</div>
                                    </div>
                                    <div class="row2">
                                        <div class="input-container" x-bind:class="{'border-red-900':event_schedule.bad_dates}">
                                            <span class="flex-grow" style="width: 50%">
                                                <input id="edate" type="date" class="w-full" 
                                                       x-model="event_schedule.end_date" 
                                                       x-bind:min="event_schedule.start_date??event_schedule.now_date" 
                                                       onkeydown="return false;" 
                                                       onchange="eventPickerChange.call(this, event)"
                                                       onclick="eventPickerClick.call(this, event)">
                                            </span>
                                            <span class="flex-grow" x-show="!event_schedule.all_day" style="width: 50%">
                                                <input id="etime" type="time" class="w-full" 
                                                       x-model="event_schedule.end_time" 
                                                       x-bind:min="(event_schedule.end_date === '' || event_schedule.end_date === event_schedule.start_date) && event_schedule.start_time !== ''?event_schedule.start_time:''">
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="" class="mb-4 mt-2" x-cloak x-show="event_schedule.start_date">
                    <div class="callout info-callout flex-grow mt-0 mb-2">
                        <div class="flex flex-row">
                            <div class="callout-icon-container">
                {% if is_granted("ROLE_SUPER_ADMIN") or api_env == 'cardinal' %}
                                <i style="cursor:pointer;" x-on:click="debug()" class="fa fa-info-circle"></i>
                {% else %}
                                <i class="fa fa-info-circle"></i>
                {% endif %}
                            </div>
                            <div class="flex-grow">
                                <div>Your event<span x-cloak x-show="recurring_settings.enabled">s</span> will appear on the <span class="underline">Calendar and Upcoming Events</span> <strong x-text="post_schedule.publish_at.formatted_date"></strong></div>
                            </div>
                        </div>
                    </div>
                </div>

                {% include 'components/post-form/options/timezone.html.twig' %}

                <div id="options-recurring" class="section2" x-cloak x-show="recurring_settings.enabled">
                    <div id="" class="mb-4 mt-2" x-cloak x-show="recurring_settings.bad_recurring">
                        <div class="callout error-callout flex-grow mt-0 mb-2">
                            <div class="flex flex-row">
                                <div class="callout-icon-container">
                {% if is_granted("ROLE_SUPER_ADMIN") or api_env == 'cardinal' %}
                                    <i style="cursor:pointer;" x-on:click="debug()" class="fa fa-exclamation-circle"></i>
                {% else %}
                                    <i class="fa fa-exclamation-circle"></i>
                {% endif %}
                                </div>
                                <div class="flex-grow">
                                    <div>Please select the recurring options</div>
                                </div>
                            </div>
                        </div>
                    </div>
    
                    <div id="" class="mb-4 mt-2" x-cloak x-show="recurring_settings.bad_through">
                        <div class="callout error-callout flex-grow mt-0 mb-2">
                            <div class="flex flex-row">
                                <div class="callout-icon-container">
                {% if is_granted("ROLE_SUPER_ADMIN") or api_env == 'cardinal' %}
                                    <i style="cursor:pointer;" x-on:click="debug()" class="fa fa-exclamation-circle"></i>
                {% else %}
                                    <i class="fa fa-exclamation-circle"></i>
                {% endif %}
                                </div>
                                <div class="flex-grow">
                                    <div>The Continue "through" Date Must Come After The Event Start</div>
                                </div>
                            </div>
                        </div>
                    </div>
    
                    <div class="row2">
                        <div class="title2">Recurring Options</div>
                        <div class="flex-grow column2">
                            <sl-tab-group placement="start" noScrollControls=true id="recurring-tab-group" @sl-tab-show="recurring_settings.frequency = $event.detail.name">
                                <sl-tab slot="nav" panel="daily">Daily</sl-tab>
                                <sl-tab slot="nav" panel="weekly">Weekly</sl-tab>
                                <sl-tab slot="nav" panel="monthly">Monthly</sl-tab>
                                <sl-tab slot="nav" panel="yearly">Yearly</sl-tab>
<!-- daily -->
                                <sl-tab-panel id="options-recurring-daily" name="daily">
                                    <div class="row2 text-sm mb-1">
                                        <div class="flex-grow h-8">Repeat Every 
                                            <span class="input-container" style="display: inline">
                                                <input type="number" class="w-12" 
                                                       x-model="recurring_settings.daily.interval" 
                                                       min="1" max="365">
                                            </span> day<span x-cloak x-show="recurring_settings.daily.interval > 1">s</span> beginning <span x-text="event_schedule.beginning_string"></span>
                                            <span class="font-bold text-red-600" x-cloak x-show="recurring_settings.daily.interval < 1 || recurring_settings.daily.interval > 365">&nbsp;- Must Be 1 through 365</span>
                                        </div>
                                    </div>
                                    <div class="row2 text-sm">
                                        <div class="flex-grow h-8">Continuing
                                            <span class="input-container" style="display: inline">
                                                <select name="" id="" x-model="recurring_settings.continuance.type" class="outline-none h-auto border-none" style="padding-right: 28px;">
                                                    <option value="forever">forever</option>
                                                    <option value="count">for</option>
                                                    <option value="until">through</option>
                                                </select>
                                            </span>
                                            <span x-show="recurring_settings.continuance.type==='count'">
                                                <span class="input-container" style="display: inline">
                                                    <input type="number" class="w-12 outline-none" 
                                                           min="1" 
                                                           x-model="recurring_settings.continuance.count">
                                                </span>
                                                occurrences
                                                <span class="font-bold text-red-600" x-cloak x-show="recurring_settings.continuance.count < 1">&nbsp;- Must Be 1 or greater</span>
                                            </span>
                                            <span x-show="recurring_settings.continuance.type==='until'">
                                                <span class="input-container" style="display: inline">
                                                    <input onkeydown="return false;" onclick="this.showPicker()" type="date" class="w-36 outline-none" x-bind:min="event_schedule.start_date" x-model="recurring_settings.continuance.until">
                                                </span>
                                            </span>
                                        </div>
                                    </div>
                                </sl-tab-panel>
<!-- weekly -->
                                <sl-tab-panel id="options-recurring-weekly" name="weekly">
                                    <div class="row2 text-sm mb-1">
                                        <div class="flex-grow h-8">Repeat Every <span class="input-container" style="display: inline">
                                            <input type="number" class="w-12" x-model="recurring_settings.weekly.interval" 
                                                   min="1" max="52"></span> week<span x-cloak x-show="recurring_settings.weekly.interval > 1">s</span> 
                                                   beginning <span x-text="event_schedule.weekly_beginning_string"></span>
                                            <span class="font-bold text-red-600" x-cloak x-show="recurring_settings.weekly.interval < 1 || recurring_settings.weekly.interval > 52">&nbsp;- Must Be 1 through 52</span>
                                        </div>
                                    </div>
                                    <div class="row2 text-sm mb-1">
                                        <div class="flex-grow h-8">
                                            <div class="flex flex-row items-center">
                                                <span>On</span>
                                                <span class="flex flex-row items-center mx-2"><input type="checkbox" value="SU" x-model="recurring_settings.weekly.byday" id="dow-box-0" class="dow-boxes"><span class="ml-0.5">Sun</span></span>
                                                <span class="flex flex-row items-center mx-2"><input type="checkbox" value="MO" x-model="recurring_settings.weekly.byday" id="dow-box-1" class="dow-boxes"><span class="ml-0.5">Mon</span></span>
                                                <span class="flex flex-row items-center mx-2"><input type="checkbox" value="TU" x-model="recurring_settings.weekly.byday" id="dow-box-2" class="dow-boxes"><span class="ml-0.5">Tue</span></span>
                                                <span class="flex flex-row items-center mx-2"><input type="checkbox" value="WE" x-model="recurring_settings.weekly.byday" id="dow-box-3" class="dow-boxes"><span class="ml-0.5">Wed</span></span>
                                                <span class="flex flex-row items-center mx-2"><input type="checkbox" value="TH" x-model="recurring_settings.weekly.byday" id="dow-box-4" class="dow-boxes"><span class="ml-0.5">Thu</span></span>
                                                <span class="flex flex-row items-center mx-2"><input type="checkbox" value="FR" x-model="recurring_settings.weekly.byday" id="dow-box-5" class="dow-boxes"><span class="ml-0.5">Fri</span></span>
                                                <span class="flex flex-row items-center mx-2"><input type="checkbox" value="SA" x-model="recurring_settings.weekly.byday" id="dow-box-6" class="dow-boxes"><span class="ml-0.5">Sat</span></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row2 text-sm">
                                        <div class="flex-grow h-8">Continuing
                                            <span class="input-container" style="display: inline">
                                                <select name="" id="" x-model="recurring_settings.continuance.type" class="outline-none h-auto border-none" style="padding-right: 28px;">
                                                    <option value="forever">forever</option>
                                                    <option value="count">for</option>
                                                    <option value="until">through</option>
                                                </select>
                                            </span>
                                            <span x-show="recurring_settings.continuance.type==='count'">
                                                <span class="input-container" style="display: inline">
                                                    <input type="number" min="1" 
                                                           class="w-12 outline-none" 
                                                           x-model="recurring_settings.continuance.count">
                                                </span>
                                                occurrences
                                                <span class="font-bold text-red-600" x-cloak x-show="recurring_settings.continuance.count < 1">&nbsp;- Must Be 1 or greater</span>
                                            </span>
                                            <span x-show="recurring_settings.continuance.type==='until'">
                                                <span class="input-container" style="display: inline">
                                                    <input onkeydown="return false;" onclick="this.showPicker()" type="date" class="w-36 outline-none" x-bind:min="event_schedule.start_date" x-model="recurring_settings.continuance.until">
                                                </span>
                                            </span>
                                        </div>
                                    </div>
                                </sl-tab-panel>
<!-- monthly -->
                                <sl-tab-panel id="options-recurring-monthly" name="monthly">
                                    <div class="row2 text-sm mb-1">
                                        <div class="flex-grow h-8">
                                            Repeat Every <span class="input-container" style="display: inline">
                                            <input type="number" class="w-12" x-model="recurring_settings.monthly.interval" min="1" max="12"></span> month<span x-cloak x-show="recurring_settings.monthly.interval > 1">s</span> beginning <span x-text="event_schedule.beginning_string"></span>
                                            <span class="font-bold text-red-600" x-cloak x-show="recurring_settings.monthly.interval < 1 || recurring_settings.monthly.interval > 12">&nbsp;- Must Be 1 through 12</span>
                                        </div>
                                    </div>
                                    <div class="row2 text-sm mb-1">
                                        <div class="flex-grow h-8">
                                            On The <span class="input-container" style="display: inline">
                                                <select name="" id="" class="outline-none h-auto border-none" style="padding-right: 28px;" x-model="recurring_settings.monthly.type">
                                                    <template x-for="recur in recurring_settings.monthly.recur_options">
                                                        <template x-if="recur.label">
                                                            <option x-text="recur.label" x-bind:value="recur.type"></option>
                                                        </template>
                                                    </template>
                                                </select></span> of each month
                                        </div>
                                    </div>
                                    <div class="row2 text-sm">
                                        <div class="flex-grow h-8">Continuing
                                            <span class="input-container" style="display: inline">
                                                <select name="" id="" x-model="recurring_settings.continuance.type" class="outline-none h-auto border-none" style="padding-right: 28px;">
                                                    <option value="forever">forever</option>
                                                    <option value="count">for</option>
                                                    <option value="until">through</option>
                                                </select>
                                            </span>
                                            <span x-show="recurring_settings.continuance.type==='count'">
                                                <span class="input-container" style="display: inline">
                                                    <input type="number" min="1" 
                                                           class="w-12 outline-none" 
                                                           x-model="recurring_settings.continuance.count">
                                                </span>
                                                occurrences
                                                <span class="font-bold text-red-600" x-cloak x-show="recurring_settings.continuance.count < 1">&nbsp;- Must Be 1 or greater</span>
                                            </span>
                                            <span x-show="recurring_settings.continuance.type==='until'">
                                                <span class="input-container" style="display: inline">
                                                    <input onkeydown="return false;" onclick="this.showPicker()" type="date" class="w-36 outline-none" x-bind:min="event_schedule.start_date" x-model="recurring_settings.continuance.until">
                                                </span>
                                            </span>
                                        </div>
                                    </div>
                                </sl-tab-panel>
<!-- yearly -->
                                <sl-tab-panel id="options-recurring-yearly" name="yearly">
                                    <div class="row2 text-sm mb-1">
                                        <div class="flex-grow h-8">Repeat Every <span class="input-container" style="display: inline">
                                            <input type="number" class="w-12" x-model="recurring_settings.yearly.interval" min="1"></span> year<span x-cloak x-show="recurring_settings.yearly.interval > 1">s</span> beginning <span x-text="event_schedule.beginning_string"></span>
                                            <span class="font-bold text-red-600" x-cloak x-show="recurring_settings.yearly.interval < 1">&nbsp;- Must Be 1 Or Greater</span>
                                        </div>
                                    </div>
                                    <div class="row2 text-sm mb-1">
                                        <div class="flex-grow h-8">
                                            On The <span class="input-container" style="display: inline">
                                                <select name="" id="" class="outline-none h-auto border-none" style="padding-right: 28px;" x-model="recurring_settings.yearly.type">
                                                    <template x-for="recur in recurring_settings.yearly.recur_options">
                                                        <option x-text="recur.label" x-bind:value="recur.type"></option>
                                                    </template>
                                                </select></span> of each year
                                        </div>
                                    </div>
                                    <div class="row2 text-sm">
                                        <div class="flex-grow h-8">Continuing
                                            <span class="input-container" style="display: inline">
                                                <select name="" id="" x-model="recurring_settings.continuance.type" class="outline-none h-auto border-none" style="padding-right: 28px;">
                                                    <option value="forever">forever</option>
                                                    <option value="count">for</option>
                                                    <option value="until">through</option>
                                                </select>
                                            </span>
                                            <span x-show="recurring_settings.continuance.type==='count'">
                                                <span class="input-container" style="display: inline">
                                                    <input type="number" min="1" 
                                                           class="w-12 outline-none" 
                                                           x-model="recurring_settings.continuance.count">
                                                </span>
                                                occurrences
                                                <span class="font-bold text-red-600" x-cloak x-show="recurring_settings.continuance.count < 1">&nbsp;- Must Be 1 or greater</span>
                                            </span>
                                            <span x-show="recurring_settings.continuance.type==='until'">
                                                <span class="input-container" style="display: inline">
                                                    <input onkeydown="return false;" onclick="this.showPicker()" type="date" class="w-36 outline-none" x-bind:min="event_schedule.start_date" x-model="recurring_settings.continuance.until">
                                                </span>
                                            </span>
                                        </div>
                                    </div>
                                </sl-tab-panel>
                            </sl-tab-group>
                        </div>
                    </div>
                </div>

                {% include 'components/post-form/options/media.html.twig' %}

                {% include 'components/post-form/options/attachments.html.twig' with {postType: 'event'} %}

                <div id="options-rsvp" class="section2" x-cloak x-show="rsvp_settings.enabled">
                    <div class="row2">
                        <div class="title2">RSVP Settings</div>
                        <div class="flex-grow column2">

{% include 'organization/event/components/event.errmsgs.html.twig' with {use_msgs: 'rsvp_open'} %}

                            <div id="options-rsvp-schedule-open" class="row2 items-center">
                                <label class="check-label my-1.5 w-64">
                                    <input type="checkbox" x-model="rsvp_settings.schedule.open.enabled">
                                    <span>Set RSVP Open Date</span>
                                </label>
                                <div class="row2 items-center flex-grow" x-show="rsvp_settings.schedule.open.enabled" x-cloak>
                                    <div x-show="recurring_settings.enabled === false" x-cloak class="row2 flex-grow items-center">
                                        <div class="text-sm text-right w-32">Open RSVPs On</div>
                                        <span class="input-container">
                                            <span class="flex-grow" style="width: 50%">
                                                <input type="date" class="w-full"
                                                       x-bind:max="event_schedule.start_date" 
                                                       x-model="rsvp_settings.schedule.open.date"
                                                       onkeydown="return false;" onclick="this.showPicker()">
                                            </span>
                                            <span class="flex-grow" style="width: 50%">
                                                <input type="time" class="w-full" 
                                                       x-model="rsvp_settings.schedule.open.time">
                                            </span>
                                        </span>
                                    </div>

                                    <div x-show="recurring_settings.enabled === true" x-cloak class="row2 flex-grow items-center">
                                        <div class="text-sm text-right w-32">Open RSVPs </div>
                                        <div class="flex flex-row flex-grow w-full items-center">
                                            <span class="input-container w-auto flex-grow-0">
                                                <input id="rsvp-open-interval" type="number" min="1" 
                                                       class="w-12 outline-none"
                                                       x-model="rsvp_settings.schedule.open.interval">
                                            </span>
                                            <span class="input-container w-auto flex-grow-0">
                                                <select name="" id="rsvp-open-interval-select" x-model="rsvp_settings.schedule.open.interval_type" class="outline-none h-auto border-none" style="padding-right: 28px;">
                                                    <option data-hours-per="1"   value="hours" x-text="rsvp_settings.schedule.open.interval==1?'Hour':'Hours'"></option>
                                                    <option data-hours-per="24"  value="day"   x-text="rsvp_settings.schedule.open.interval==1?'Day':'Days'"></option>
                                                    <option data-hours-per="168" value="week"  x-text="rsvp_settings.schedule.open.interval==1?'Week':'Weeks'"></option>
                                                    <option data-hours-per="720" value="month" x-text="rsvp_settings.schedule.open.interval==1?'Month':'Months'"></option>
                                                </select>
                                            </span>
                                            <div class="text-sm flex-grow pl-1">Before Each Event Occurrence Starts<span class="font-bold text-red-600" x-cloak x-show="rsvp_settings.schedule.open.interval < 1">&nbsp;Must Be 1 Or Greater</span></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

{% include 'organization/event/components/event.errmsgs.html.twig' with {use_msgs: 'rsvp_close'} %}

                            <div id="options-rsvp-schedule-close" class="row2 items-center">
                                <label class="check-label my-1.5 w-64">
                                    <input type="checkbox" x-model="rsvp_settings.schedule.close.enabled">
                                    <span>Set RSVP Close Date</span>
                                </label>
                                <div class="row2 items-center flex-grow" x-show="rsvp_settings.schedule.close.enabled" x-cloak>
                                    <div x-show="recurring_settings.enabled === false" x-cloak class="row2 flex-grow items-center">
                                        <div class="text-sm text-right w-32">Close RSVPs On</div>
                                        <span class="input-container">
                                            <span class="flex-grow" style="width: 50%">
                                                <input class="w-full" type="date" 
                                                       x-bind:min="rsvp_settings.schedule.open.date" 
                                                       x-bind:max="event_schedule.end_date??event_schedule.start_date" 
                                                       x-model="rsvp_settings.schedule.close.date"
                                                       onkeydown="return false;" onclick="this.showPicker()">
                                            </span>
                                            <span class="flex-grow" style="width: 50%">
                                                <input type="time" class="w-full" 
                                                       x-model="rsvp_settings.schedule.close.time">
                                            </span>
                                        </span>
                                    </div>

                                    <div x-show="recurring_settings.enabled === true" x-cloak class="row2 flex-grow items-center">
                                        <div class="text-sm text-right w-32">Close RSVPs </div>
                                        <div class="flex flex-row flex-grow w-full items-center">
                                            <span class="input-container w-auto flex-grow-0">
                                                <input id="rsvp-close-interval" type="number" min="1" 
                                                       class="w-12 outline-none" 
                                                       x-model="rsvp_settings.schedule.close.interval">
                                            </span>
                                            <span class="input-container w-auto flex-grow-0">
                                                <select name="" id="rsvp-close-interval-select" x-model="rsvp_settings.schedule.close.interval_type" class="outline-none h-auto border-none" style="padding-right: 28px;">
                                                    <option data-hours-per="1"   value="hours" x-text="rsvp_settings.schedule.close.interval==1?'Hour':'Hours'"></option>
                                                    <option data-hours-per="24"  value="day"   x-text="rsvp_settings.schedule.close.interval==1?'Day':'Days'"></option>
                                                    <option data-hours-per="168" value="week"  x-text="rsvp_settings.schedule.close.interval==1?'Week':'Weeks'"></option>
                                                    <option data-hours-per="720" value="month" x-text="rsvp_settings.schedule.close.interval==1?'Month':'Months'"></option>
                                                </select>
                                            </span>
                                            <div class="text-sm flex-grow pl-1">Before Each Event Occurrence Starts<span class="font-bold text-red-600" x-cloak x-show="rsvp_settings.schedule.close.interval < 1">&nbsp;Must Be 1 Or Greater</span></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="options-rsvp-limit-total-attendees" class="row2 items-center">
                                <label class="check-label w-64 my-1.5">
                                    <input type="checkbox" x-model="rsvp_settings.limit_attendees">
                                    <span>Limit Total Attendees</span>
                                </label>
                                <div class="row2 items-center flex-grow" x-show="rsvp_settings.limit_attendees" x-cloak>
                                    <div class="input-container w-16">
                                        <input type="number" min="1" class="w-full" 
                                               x-model="rsvp_settings.maximum_total_attendees">
                                    </div>
                                    <div class="text-sm">Maximum Attendees<span class="font-bold text-red-600" x-cloak x-show="rsvp_settings.maximum_total_attendees < 1">&nbsp;Must Be 1 Or Greater</span></div>
                                </div>
                            </div>
    {#                        <div id="options-rsvp-limit-total-parties" class="row2 items-center">#}
    {#                            <label class="check-label w-64 my-1.5">#}
    {#                                <input type="checkbox" x-model="rsvp_settings.limit_rsvp">#}
    {#                                <span>Limit Total Parties</span>#}
    {#                            </label>#}
    {#                            <div class="row2 items-center flex-grow" x-show="rsvp_settings.limit_rsvp" x-cloak>#}
    {#                                <div class="input-container w-16">#}
    {#                                    <input type="number" class="w-full" x-model="rsvp_settings.maximum_total_parties">#}
    {#                                </div>#}
    {#                                <div class="text-sm">Maximum Parties</div>#}
    {#                            </div>#}
    {#                        </div>#}
                            <div id="options-rsvp-limit-attendees-per-party" class="row2 items-center">
                                <label class="check-label w-64 my-1.5">
                                    <input type="checkbox" x-model="rsvp_settings.limit_rsvp_attendees">
                                    <span>Limit Attendees Per Party</span>
                                </label>
                                <div class="row2 items-center flex-grow" x-show="rsvp_settings.limit_rsvp_attendees" x-cloak>
                                    <div class="input-container w-16">
                                        <input type="number" min="1" 
                                               class="w-full" 
                                               x-model="rsvp_settings.maximum_rsvp_attendees">
                                    </div>
                                    <div class="text-sm">Maximum Attendees Per Party<span class="font-bold text-red-600" x-cloak x-show="rsvp_settings.maximum_rsvp_attendees < 1">&nbsp;Must Be 1 Or Greater</span></div>
                                </div>
                            </div>
                            <div id="options-rsvp-enable-waitlist" class="row2 items-center" x-cloak x-show="rsvp_settings.limit_attendees||rsvp_settings.limit_rsvp">
                                <label class="check-label w-64 my-1.5">
                                    <input type="checkbox" x-model="rsvp_settings.waitlist.enabled">
                                    <span>Enable Waitlist</span>
                                </label>
                            </div>
                            <div id="options-rsvp-allow-party-notes" class="row2 items-center">
                                <label class="check-label w-64 my-1.5">
                                    <input type="checkbox" x-model="rsvp_settings.form.allow_notes">
                                    <span>Allow Notes For Full Party</span>
                                </label>
                            </div>
                            <div id="options-rsvp-require-name" class="row2 items-center">
                                <label class="check-label w-64 my-1.5">
                                    <input type="checkbox" x-model="rsvp_settings.form.require_name">
                                    <span>Require Attendee Name</span>
                                </label>
                            </div>
                            <div id="options-rsvp-request-email" class="row2 items-center">
                                <label class="check-label w-64 my-1.5">
                                    <input type="checkbox" x-model="rsvp_settings.form.require_email">
                                    <span>Request Attendee Email</span>
                                </label>
                            </div>
    {#                        <div id="options-rsvp-allow-attendee-notes" class="row2 items-center">#}
    {#                            <label class="check-label w-64 my-1.5">#}
    {#                                <input type="checkbox" x-model="rsvp_settings.form.allow_attendee_notes">#}
    {#                                <span>Allow Notes For Each Attendee</span>#}
    {#                            </label>#}
    {#                        </div>#}
                            <div id="options-rsvp-request-age-group" class="row2 items-start">
                                <label class="check-label w-64 my-1.5">
                                    <input type="checkbox" x-model="rsvp_settings.form.collect_age_groups">
                                    <span>Request Attendee Age Group</span>
                                </label>
                                <div class="column2 flex-grow mt-1.5" x-show="rsvp_settings.form.collect_age_groups">
                                    <div class="row2 items-center">
                                        <label class="check-label m-0">
                                            <input type="checkbox" x-model="rsvp_settings.form.allow_adults" x-on:change="!rsvp_settings.form.allow_adults && !rsvp_settings.form.allow_children?rsvp_settings.form.allow_children = true:null">
                                            <span>Allow Adults</span>
                                        </label>
                                        <label class="check-label m-0">
                                            <input type="checkbox" x-model="rsvp_settings.form.allow_children" x-on:change="!rsvp_settings.form.allow_adults && !rsvp_settings.form.allow_children?rsvp_settings.form.allow_adults = true:null">
                                            <span>Allow Children</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {% include 'components/post-form/options/location.html.twig' %}

                {% if is_granted("ROLE_SUPER_ADMIN") or api_env == 'cardinal' %}
                    <div id="admin-tools" class="section2">
                        <div class="border-2 border-solid border-purple-700 rounded-md mt-3 p-3 text-sm text-purple-700">
                            <div class="row2">
                                <div class="title2">Admin Utilities</div>
                                <div class="column2 flex-grow">
                                    <div id="options-post-schedule-publish" class="row2 h-8">
                                        <label class="check-label m-0 w-full">
                                            <input type="checkbox" x-model="dry_run">
                                            <span>Dry Run (won't actually create events)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}

                <div id="submit-buttons" class="section2">
                    <div class="row2 items-stretch">
                        <div class="callout info-callout w-full items-center">
                            <div class="callout-icon-container">
                {% if is_granted("ROLE_SUPER_ADMIN") or api_env == 'cardinal' %}
                                <i style="cursor:pointer;" x-on:click="debug()" class="fa fa-info-circle"></i>
                {% else %}
                                <i class="fa fa-info-circle"></i>
                {% endif %}
                            </div>
                            <div>Event Notifications can be sent by clicking the "Promote Event" button after your event has been published.</div>
                        </div>
                    </div>

                    <div class="row2 items-stretch">
                        <button @click="action = 'post'; submit()" type="submit" 
                                class="w-full p-2 bg-clubster-300 text-white border-none" 
                                x-bind:disabled="bad_file_link || /* event create only */(recurring_settings.enabled && (recurring_settings.bad_recurring || recurring_settings.bad_through)) || (recurring_settings.enabled && recurring_settings.frequency == 'daily' && (recurring_settings.daily.interval < 1 || recurring_settings.daily.interval > 365)) || (recurring_settings.enabled && recurring_settings.frequency == 'weekly' && (recurring_settings.weekly.interval < 1 || recurring_settings.weekly.interval > 52)) || (recurring_settings.enabled && recurring_settings.frequency == 'monthly' && (recurring_settings.monthly.interval < 1 || recurring_settings.monthly.interval > 12)) || (recurring_settings.enabled && recurring_settings.frequency == 'yearly' && recurring_settings.yearly.interval < 1) || (recurring_settings.enabled && recurring_settings.continuance.count < 1) || /* needed in edit when repeat is enabled */(recurring_settings.enabled && (rsvp_settings.enabled && rsvp_settings.bad_interval)) || (!recurring_settings.enabled && (rsvp_settings.enabled && (rsvp_settings.bad_close_date || rsvp_settings.bad_open_date))) || /* rsvp is enabled */(rsvp_settings.enabled && ((rsvp_settings.bad_before_event || rsvp_settings.bad_same_date || rsvp_settings.bad_after_event || rsvp_settings.bad_before_now) || (rsvp_settings.schedule.open.enabled && rsvp_settings.schedule.open.interval < 1) || (rsvp_settings.schedule.close.enabled && rsvp_settings.schedule.close.interval < 1) || (rsvp_settings.limit_attendees && rsvp_settings.maximum_total_attendees < 1) || (rsvp_settings.limit_rsvp_attendees && rsvp_settings.maximum_rsvp_attendees < 1))) || /* everything else */(submitting || uploading > 0 || event_schedule.bad_end || event_schedule.bad_dates || event_schedule.start_date==null || event_schedule.start_date=='' || (title.length == 0 && body.length==0))">
                            <i class="fas fa-spinner fa-spin" x-cloak x-show="submitting && action=='post'"></i>
                            <span x-text="post_schedule.publish_at.enabled?'Schedule':'Publish'">Publish</span> Event
                        </button>
                        <button @click="action = 'preview'; submit()" type="submit" 
                                class="w-full p-2 bg-yellow-100 text-yellow-900 border-none" 
                                x-bind:disabled="bad_file_link || /* event create only */(recurring_settings.enabled && (recurring_settings.bad_recurring || recurring_settings.bad_through)) || (recurring_settings.enabled && recurring_settings.frequency == 'daily' && (recurring_settings.daily.interval < 1 || recurring_settings.daily.interval > 365)) || (recurring_settings.enabled && recurring_settings.frequency == 'weekly' && (recurring_settings.weekly.interval < 1 || recurring_settings.weekly.interval > 52)) || (recurring_settings.enabled && recurring_settings.frequency == 'monthly' && (recurring_settings.monthly.interval < 1 || recurring_settings.monthly.interval > 12)) || (recurring_settings.enabled && recurring_settings.frequency == 'yearly' && recurring_settings.yearly.interval < 1) || (recurring_settings.enabled && recurring_settings.continuance.count < 1) || /* needed in edit when repeat is enabled */(recurring_settings.enabled && (rsvp_settings.enabled && rsvp_settings.bad_interval)) || (!recurring_settings.enabled && (rsvp_settings.enabled && (rsvp_settings.bad_close_date || rsvp_settings.bad_open_date))) || /* rsvp is enabled */(rsvp_settings.enabled && ((rsvp_settings.bad_before_event || rsvp_settings.bad_same_date || rsvp_settings.bad_after_event || rsvp_settings.bad_before_now) || (rsvp_settings.schedule.open.enabled && rsvp_settings.schedule.open.interval < 1) || (rsvp_settings.schedule.close.enabled && rsvp_settings.schedule.close.interval < 1) || (rsvp_settings.limit_attendees && rsvp_settings.maximum_total_attendees < 1) || (rsvp_settings.limit_rsvp_attendees && rsvp_settings.maximum_rsvp_attendees < 1))) || /* everything else */(submitting || uploading > 0 || event_schedule.bad_end || event_schedule.bad_dates || event_schedule.start_date==null || event_schedule.start_date=='' || (title.length == 0 && body.length==0))">
                            <i class="fas fa-spinner fa-spin" x-cloak x-show="submitting && action=='preview'"></i>
                            Preview & Save
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>

    {% include 'post/components/storage/restoremodal.html.twig' with {postType: 'event'} %}

{% endblock rightCol %}

{% block javascripts %}

    {% include 'components/storage/localstore.html.twig' %}

<script>
{% if (eventCopy??false) %}
    let mode = 'COPY';
{% else %}
    let mode = 'RESTORE';
{% endif %}

let saveId = 'evntsave-'+userId+'-'+orgId;

    // event create & edit - end date
    function eventPickerClick(ev) {
        if(typeof eventPickerClick.dirty === 'undefined' ) this.value = this.min;
        document.getElementById('datadiv')._x_dataStack[0].event_schedule.end_date = this.value;
        this.showPicker();
    };
    function eventPickerChange(ev) {
        eventPickerClick.dirty = true;
    };

{% if eventCopy??false %}
    let copyData = {
        event_copy: {{eventCopy|json_encode|raw}},
    };
{% else %}
    let copyData = {
        event_copy: null,
    };
    let restData = getStorage(saveId);
    if(typeof(restData) !== 'undefined' && restData !== null) {
        copyData.event_copy = JSON.parse(restData);
    }
{% endif %}

    // checked in post-utility.js:submit()
    let changeFlag = false;

    const allDay = '{{allDay??0}}';
    const evStart = '{{start??''}}';
    const evEnd = '{{end??''}}';
    let dateStart = '';
    let timeStart = '';
    let dateEnd = '';
    let timeEnd = '';
    let externalCreate = false;

    function preventUnload(evt) {
        if(changeFlag === false) {
            // unloading will proceed w/o prompting
            return undefined;
        } else {
            //  save data here
            if(canSave) {
                if(copyData.event_copy === null) copyData.event_copy = {};

                copyData.event_copy.action    = $('#datadiv')[0]._x_dataStack[0].action;
                copyData.event_copy.post_type = $('#datadiv')[0]._x_dataStack[0].post_type;
                copyData.event_copy.title     = $('#datadiv')[0]._x_dataStack[0].title;
                copyData.event_copy.body      = EditorUtility.getActiveEditorContent();
                copyData.event_copy.location  = $('#datadiv')[0]._x_dataStack[0].location;

                copyData.event_copy.media = Object.values($('#datadiv')[0]._x_dataStack[0].media.list).map((element) => {
                    if(element.mime.split('/')[1] === 'youtube') {
                        return {
                            key: element.key,
                            name: element.name,
                            mime: element.mime,
                            base_mime: element.base_mime,
                            imageData: element.imageData,
                            videoData: null,
                            status: element.status,
                            percent: element.percent,
                        };
                    } else {
                        const vthumb = element.image_url.split('/')[0]+'//'+element.image_url.split('/')[1]+element.image_url.split('/')[2]+'/'+element.image_url.split('/')[3].split('.')[0]+'.0000001.jpg';
                        return {
                            key: element.key,
                            name: element.name,
                            mime: element.mime,
                            base_mime: (element.mime.split('/')[0] === 'video' ? 'image' : element.base_mime),
                            imageData: null,
                            videoData: null,
                            status: element.status,
                            percent: element.percent,
                            id: element.id,
                            image_url: (element.mime.split('/')[0] === 'video' ? vthumb : element.image_url),
                        };
                    }
                });

                copyData.event_copy.attachments = Object.values($('#datadiv')[0]._x_dataStack[0].attachments.list).map((element) => {
                    return {
                        icon: element.icon,
                        id: element.id,
                        key: element.key,
                        mime: element.mime,
                        name: element.name,
                        percent: element.percent,
                        status: element.status,
                    };
                });

                copyData.event_copy.timezone       = $('#datadiv')[0]._x_dataStack[0].timezone.selected;

                copyData.event_copy.event_schedule  = {
                    all_day: $('#datadiv')[0]._x_dataStack[0].event_schedule.all_day,
                    end_date: $('#datadiv')[0]._x_dataStack[0].event_schedule.end_date,
                    end_time: $('#datadiv')[0]._x_dataStack[0].event_schedule.end_time,
                    start_date: $('#datadiv')[0]._x_dataStack[0].event_schedule.start_date,
                    start_time: $('#datadiv')[0]._x_dataStack[0].event_schedule.start_time,
                };

                copyData.event_copy.recurring_settings  = {
                    continuance: {
                        type: $('#datadiv')[0]._x_dataStack[0].recurring_settings.continuance.type,
                        until: $('#datadiv')[0]._x_dataStack[0].recurring_settings.continuance.until,
                        count: $('#datadiv')[0]._x_dataStack[0].recurring_settings.continuance.count,
                    },
                    daily: {
                        interval: $('#datadiv')[0]._x_dataStack[0].recurring_settings.daily.interval,
                    },
                    enabled: $('#datadiv')[0]._x_dataStack[0].recurring_settings.enabled,
                    frequency: $('#datadiv')[0]._x_dataStack[0].recurring_settings.frequency,
                    monthly: {
                        byday: $('#datadiv')[0]._x_dataStack[0].recurring_settings.monthly.byday,
                        bymonthday: $('#datadiv')[0]._x_dataStack[0].recurring_settings.monthly.bymonthday,
                        bysetpos: $('#datadiv')[0]._x_dataStack[0].recurring_settings.monthly.bysetpos,
                        interval: $('#datadiv')[0]._x_dataStack[0].recurring_settings.monthly.interval,
                        type: $('#datadiv')[0]._x_dataStack[0].recurring_settings.monthly.type,
                    },
                    weekly: {
                        byday: $('#datadiv')[0]._x_dataStack[0].recurring_settings.weekly.byday,
                        interval: $('#datadiv')[0]._x_dataStack[0].recurring_settings.weekly.interval,
                        type: $('#datadiv')[0]._x_dataStack[0].recurring_settings.weekly.type,
                    },
                    yearly: {
                        byday: $('#datadiv')[0]._x_dataStack[0].recurring_settings.yearly.byday,
                        bymonth: $('#datadiv')[0]._x_dataStack[0].recurring_settings.yearly.bymonth,
                        bymonthday: $('#datadiv')[0]._x_dataStack[0].recurring_settings.yearly.bymonthday,
                        bysetpos: $('#datadiv')[0]._x_dataStack[0].recurring_settings.yearly.bysetpos,
                        interval: $('#datadiv')[0]._x_dataStack[0].recurring_settings.yearly.interval,
                        type: $('#datadiv')[0]._x_dataStack[0].recurring_settings.yearly.type,
                    }
                };

                copyData.event_copy.rsvp_settings  = {
                    enabled: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.enabled,
                    limit_attendees: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.limit_attendees,
                    limit_rsvp: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.limit_rsvp,
                    limit_rsvp_attendees: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.limit_rsvp_attendees,
                    maximum_rsvp_attendees: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.maximum_rsvp_attendees,
                    maximum_total_attendees: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.maximum_total_attendees,
                    schedule: {
                        close: {
                            date: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.close.date,
                            enabled: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.close.enabled,
                            interval: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.close.interval,
                            interval_type: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.close.interval_type,
                            time: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.close.time,
                        },
                        open: {
                            date: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.open.date,
                            enabled: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.open.enabled,
                            interval: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.open.interval,
                            interval_type: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.open.interval_type,
                            time: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.open.time,
                        }
                    },
                    form: {
                        require_name: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.form.require_name,
                        require_email: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.form.require_email,
                        allow_attendee_notes: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.form.allow_attendee_notes,
                        allow_notes: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.form.allow_notes,
                        collect_age_groups: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.form.collect_age_groups,
                        allow_adults: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.form.allow_adults,
                        allow_children: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.form.allow_children,
                    },
                    waitlist: {
                        enabled: $('#datadiv')[0]._x_dataStack[0].rsvp_settings.waitlist.enabled,
                    },
                };

                clearStorage(saveId);
                setStorage(saveId, copyData.event_copy)
            }

            // unloading will proceed w/o prompting
            return undefined;
        }
    };

    function inputsChanged(evt) {
        if(evt.target.id === 'search-input-field') return;

        if(changeFlag === false) {
            changeFlag = true;
            $(window).on('beforeunload', preventUnload);
        }
    };

    function getData() {
        $('#datadiv')[0]._x_dataStack[0].title = (mode === 'RESTORE' ? copyData.event_copy.title : copyData.event_copy.subject);
        $('#datadiv')[0]._x_dataStack[0].body = copyData.event_copy.body;
        EditorUtility.setActiveEditorContent(copyData.event_copy.body);
        inputsChanged({target:{id:'getData'}});
        $('#datadiv')[0]._x_dataStack[0].location = copyData.event_copy.location;

        if(mode === 'RESTORE') {
            $('#datadiv')[0]._x_dataStack[0].action = copyData.event_copy.action;
            $('#datadiv')[0]._x_dataStack[0].post_type = copyData.event_copy.post_type;

            $('#datadiv')[0]._x_dataStack[0].timezone.selected = copyData.event_copy.timezone;

            $('#datadiv')[0]._x_dataStack[0].event_schedule.all_day = copyData.event_copy.event_schedule.all_day;
            $('#datadiv')[0]._x_dataStack[0].event_schedule.end_date = copyData.event_copy.event_schedule.end_date;
            $('#datadiv')[0]._x_dataStack[0].event_schedule.end_time = copyData.event_copy.event_schedule.end_time;
            $('#datadiv')[0]._x_dataStack[0].event_schedule.start_date = copyData.event_copy.event_schedule.start_date;
            $('#datadiv')[0]._x_dataStack[0].event_schedule.start_time = copyData.event_copy.event_schedule.start_time;

            $('#datadiv')[0]._x_dataStack[0].recurring_settings.continuance.type = copyData.event_copy.recurring_settings.continuance.type;
            $('#datadiv')[0]._x_dataStack[0].recurring_settings.continuance.until = copyData.event_copy.recurring_settings.continuance.until;
            $('#datadiv')[0]._x_dataStack[0].recurring_settings.continuance.count = copyData.event_copy.recurring_settings.continuance.count;

            $('#datadiv')[0]._x_dataStack[0].recurring_settings.daily.interval = copyData.event_copy.recurring_settings.daily.interval;

            $('#datadiv')[0]._x_dataStack[0].recurring_settings.enabled = copyData.event_copy.recurring_settings.enabled; 
            $('#datadiv')[0]._x_dataStack[0].recurring_settings.frequency = copyData.event_copy.recurring_settings.frequency;

            $('#datadiv')[0]._x_dataStack[0].recurring_settings.monthly.byday = copyData.event_copy.recurring_settings.monthly.byday;
            $('#datadiv')[0]._x_dataStack[0].recurring_settings.monthly.bymonthday = copyData.event_copy.recurring_settings.monthly.bymonthday;
            $('#datadiv')[0]._x_dataStack[0].recurring_settings.monthly.bysetpos = copyData.event_copy.recurring_settings.monthly.bysetpos;
            $('#datadiv')[0]._x_dataStack[0].recurring_settings.monthly.interval = copyData.event_copy.recurring_settings.monthly.interval;
            $('#datadiv')[0]._x_dataStack[0].recurring_settings.monthly.type = copyData.event_copy.recurring_settings.monthly.type;

            $('#datadiv')[0]._x_dataStack[0].recurring_settings.weekly.byday = copyData.event_copy.recurring_settings.weekly.byday;
            $('#datadiv')[0]._x_dataStack[0].recurring_settings.weekly.interval = copyData.event_copy.recurring_settings.weekly.interval;
            $('#datadiv')[0]._x_dataStack[0].recurring_settings.weekly.type = copyData.event_copy.recurring_settings.weekly.type;

            $('#datadiv')[0]._x_dataStack[0].recurring_settings.yearly.byday = copyData.event_copy.recurring_settings.yearly.byday;
            $('#datadiv')[0]._x_dataStack[0].recurring_settings.yearly.bymonth = copyData.event_copy.recurring_settings.yearly.bymonth;
            $('#datadiv')[0]._x_dataStack[0].recurring_settings.yearly.bymonthday = copyData.event_copy.recurring_settings.yearly.bymonthday;
            $('#datadiv')[0]._x_dataStack[0].recurring_settings.yearly.bysetpos = copyData.event_copy.recurring_settings.yearly.bysetpos;
            $('#datadiv')[0]._x_dataStack[0].recurring_settings.yearly.interval = copyData.event_copy.recurring_settings.yearly.interval;
            $('#datadiv')[0]._x_dataStack[0].recurring_settings.yearly.type = copyData.event_copy.recurring_settings.yearly.type;

            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.enabled = copyData.event_copy.rsvp_settings.enabled;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.limit_attendees = copyData.event_copy.rsvp_settings.limit_attendees;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.limit_rsvp = copyData.event_copy.rsvp_settings.limit_rsvp;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.limit_rsvp_attendees = copyData.event_copy.rsvp_settings.limit_rsvp_attendees;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.maximum_rsvp_attendees = copyData.event_copy.rsvp_settings.maximum_rsvp_attendees;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.maximum_total_attendees = copyData.event_copy.rsvp_settings.maximum_total_attendees;

            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.close.date = copyData.event_copy.rsvp_settings.schedule.close.date;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.close.enabled = copyData.event_copy.rsvp_settings.schedule.close.enabled;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.close.interval = copyData.event_copy.rsvp_settings.schedule.close.interval;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.close.interval_type = copyData.event_copy.rsvp_settings.schedule.close.interval_type;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.close.time = copyData.event_copy.rsvp_settings.schedule.close.time;

            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.open.date = copyData.event_copy.rsvp_settings.schedule.open.date;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.open.enabled = copyData.event_copy.rsvp_settings.schedule.open.enabled;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.open.interval = copyData.event_copy.rsvp_settings.schedule.open.interval;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.open.interval_type = copyData.event_copy.rsvp_settings.schedule.open.interval_type;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.schedule.open.time = copyData.event_copy.rsvp_settings.schedule.open.time;

            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.form.require_name = copyData.event_copy.rsvp_settings.form.require_name;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.form.require_email = copyData.event_copy.rsvp_settings.form.require_email;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.form.allow_attendee_notes = copyData.event_copy.rsvp_settings.form.allow_attendee_notes;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.form.allow_notes = copyData.event_copy.rsvp_settings.form.allow_notes;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.form.collect_age_groups = copyData.event_copy.rsvp_settings.form.collect_age_groups;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.form.allow_adults = copyData.event_copy.rsvp_settings.form.allow_adults;
            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.form.allow_children = copyData.event_copy.rsvp_settings.form.allow_children;

            $('#datadiv')[0]._x_dataStack[0].rsvp_settings.waitlist.enabled = copyData.event_copy.rsvp_settings.waitlist.enabled;

            Object.values(copyData.event_copy.media).forEach(function (mediaItem) {
                if(mediaItem.mime.split('/')[1] === 'youtube') {
                    $('#datadiv')[0]._x_dataStack[0].media.list[mediaItem.key] = {
                        base_mime: mediaItem.base_mime,
                        id: mediaItem.id,
                        image: mediaItem.image,
                        imageData: mediaItem.imageData,
                        image_url: mediaItem.image_url,
                        key: mediaItem.key,
                        mime: mediaItem.mime,
                        name: mediaItem.name,
                        percent: mediaItem.percent,
                        status: mediaItem.status,
                        url: mediaItem.url,
                        videoData: mediaItem.videoData,
                    };
                } else {
                    $('#datadiv')[0]._x_dataStack[0].media.list[mediaItem.key] = {
                        key: mediaItem.key,
                        name: mediaItem.name,
                        mime: mediaItem.mime,
                        base_mime: mediaItem.base_mime,
                        imageData: mediaItem.imageData,
                        videoData: mediaItem.videoData,
                        status: mediaItem.status,
                        percent: mediaItem.percent,
                        id: mediaItem.id,
                        image_url: mediaItem.image_url,
                    };
                }
            });
        } else {
            Object.values(copyData.event_copy.media).forEach(function (mediaItem) {
                let mimeType = (typeof mediaItem.mime === 'undefined' ? mediaItem.mime_type : mediaItem.mime);
                let baseMime = mimeType.split('/')[0];
                if(mimeType.split('/')[1] === 'youtube') {
                    $('#datadiv')[0]._x_dataStack[0].media.list[mediaItem.id] = {
                        base_mime: 'image',
                        image_url: mediaItem.image,
                        key: mediaItem.id,
                        ordinal: mediaItem.ordinal,
                        mime: mimeType,
                        name: mediaItem.url,
                        percent: 100,
                        status: 'complete',
                    };
                } else {
                    $('#datadiv')[0]._x_dataStack[0].media.list[mediaItem.id] = {
                        key: mediaItem.id,
                        id: mediaItem.id,
                        ordinal: mediaItem.ordinal,
                        name: mediaItem.id,
                        mime: mimeType,
                        base_mime: 'image',
                        image_url: mediaItem.image,
                        status: 'complete',
                        percent: 100,
                    };
                }
            });
        }

        let imgCount = 0;
        Object.values(copyData.event_copy.attachments).forEach(function (attachment) {
            let icons = {
                'application/pdf': 'fa-file-pdf',
    
                'application/excel': 'fa-file-excel',
                'application/x-excel': 'fa-file-excel',
                'application/vnd.ms-excel': 'fa-file-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'fa-file-excel',
                'text/csv': 'fa-file-excel',
                'application/csv': 'fa-file-excel',
                'text/x-comma-separated-values': 'fa-file-excel',
                'text/x-csv': 'fa-file-excel',
    
                'application/msword': 'fa-file-word',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fa-file-word',
    
                'application/vnd.ms-powerpoint': 'fa-file-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'fa-file-powerpoint',
    
                'text/plain': 'fa-file'
            };
            let mimeType = (mode === 'RESTORE' ? attachment.mime??'text/plain' : attachment.mime_type??'text/plain');
            let baseType = mimeType.split('/')[0];
            let icon = icons[mimeType] ?? 'fa-file-' + baseType;
            $('#datadiv')[0]._x_dataStack[0].attachments.list[attachment.id] = {
                key: (mode === 'RESTORE' ? attachment.key : attachment.id),
                id: attachment.id,
                name: attachment.name??'pending file',
                mime: mimeType,
                base_mime: baseType??'image',
                icon: icon,
                status: 'complete',
                percent: 100,
            };
            if(baseType === 'image' || baseType === 'video') {
                $('#datadiv')[0]._x_dataStack[0].attachments.imgAttachmentWarning(attachment.id, imgCount+=1);
            }
        });
    };

    $(function() {

        EditorUtility.initTinyMCE('create', inputsChanged);

        $('input').change(inputsChanged);
        $('input').keyup(inputsChanged);
        $('textarea').change(inputsChanged);
        $('select').change(inputsChanged);

// parse evStart & evEnd, separate the date & time 
        if(evStart !== '') {
            if(evStart.includes('T')) {
                let start = evStart.split('T');
                $('#datadiv')[0]._x_dataStack[0].event_schedule.start_date = start[0];
                $('#datadiv')[0]._x_dataStack[0].event_schedule.start_time = start[1];
            } else {
                $('#datadiv')[0]._x_dataStack[0].event_schedule.start_date = evStart;
                $('#datadiv')[0]._x_dataStack[0].event_schedule.all_day = !!parseInt(allDay);
            }
            externalCreate = true;
        }
        if(evEnd !== '') {
            if(evEnd.includes('T')) {
                let end = evEnd.split('T');
                $('#datadiv')[0]._x_dataStack[0].event_schedule.end_date = end[0];
                $('#datadiv')[0]._x_dataStack[0].event_schedule.end_time = end[1];
            } else {
                $('#datadiv')[0]._x_dataStack[0].event_schedule.end_date = evEnd;
            }
        }

        if(typeof copyData !== 'undefined' && copyData.event_copy !== null) {
            //console.log('we are a '+mode+' - EVENT!');
            if(mode === 'RESTORE') {
                if(!externalCreate) showRestoreModal(getData, saveId);
            } else {
                getData();
            }
        }
    });
</script>
{% endblock javascripts %}
