{% extends 'organization/admin/components/org.admin.base.html.twig' %}

{% form_theme form 'components/form/fields.tailwind.html.twig' %}

{% block rightCol %}
    <div class="content-box">
        <div class="frm-row">
            <div class="frm-section">
            {% if organization.isClub %}
                <div class="frm-section-label">Create Bulk Staff Invitations</div>
            {% else %}
                <div class="frm-section-label">Add Bulk Administrators</div>
            {% endif %}
                <div>
                    {% if subset == true %}
                    <div class="bg-yellow-100 p-4 m-2 my-4 text-center border-1 border-solid border-yellow-700 round-md text-yellow-700">
                        We're only showing the first 10 records of your file. All records will be processed
                    </div>
                    {% endif %}
                    <div class="bg-lightBlue p-4 m-2 my-4 text-center border-1 border-solid border-medBlue round-md text-baseBlue">
                        Please use the dropdown boxes below to select the data type for each column.
                    </div>
    
                    <div id="emcol_msg" class="bg-yellow-100 p-4 m-2 my-4 text-center border-1 border-solid border-yellow-700 round-md text-yellow-700">
                        <strong>Note:</strong> You must select "Email" as the data type for one column.
                    </div>
    
                    {{ form_start(form) }}
                    {{ form_row(form.hasHeader) }}
                    <table class="table-auto w-full">
                        <tr class="border-b-4 border-double border-gray-500 pb-3">
                            {% for col in data.0 %}
                                <td class="border-1 border-solid border-gray-200">
                                    {% set col = 'column_type_' ~ loop.index0 %}
                                    {{ form_widget(form.offsetGet(col)) }}
                                </td>
                            {% endfor %}
                        </tr>
                        {% for row in data %}
                            <tr {% if loop.first %}data-potential-header="true"{% endif %}>
                                {% for column in row %}
                                    <td class="p-2 border-1 border-solid border-gray-200">{{ column }}</td>
                                {% endfor %}
                            </tr>
                        {% endfor %}
                    </table>
    
                    <div class="mt-4">
                        {{ form_widget(form.save) }}
                    </div>
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    <script>
        $(function () {
            let colsel = [];
            // class added in form build
            $('.colhead-selector').each(function (idx, ele) {
                colsel[ele.id] = {idx:ele.selectedIndex,val:ele.selectedOptions[0].value};
            });

            $('.colhead-selector').on('change', function(event){
                //console.log(event.target.id + ' = ' + event.target.selectedOptions[0].value);

                if(event.target.selectedOptions[0].value === 'email') {
                    // enable the button
                    $('#form_save').prop('disabled',false);
                    $('#emcol_msg').prop('hidden',true);
                } else {
                    if(($('#form_save').prop('disabled') === false) && (colsel[event.target.id].val === 'email')) {
                        // email was selected, now it isn't
                        $('#form_save').prop('disabled',true);
                        $('#emcol_msg').prop('hidden',null);
                    }
                }

                $('.colhead-selector').each(function (idx, ele) {
                    if(ele.id !== event.target.id) {
                        if(event.target.selectedOptions[0].value !== 'ignore') {
                            //console.log('making '+event.target.selectedOptions[0].value+' disappear in ' + ele.id);
                            $(ele.options[event.target.selectedIndex]).prop('hidden',true);
                        }
                        //console.log('making '+colsel[event.target.id].val+' reappear in ' + ele.id);
                        $(ele.options[colsel[event.target.id].idx]).prop('hidden',null);
                    }
                });
                colsel[event.target.id].val = event.target.selectedOptions[0].value;
                colsel[event.target.id].idx = event.target.selectedIndex;
            });

            $('#form_hasHeader').on('change', function(event){
                var potentialHeader = $('[data-potential-header]');
                if ($(this).is(':checked')) {
                    potentialHeader.hide();
                }else{
                    potentialHeader.show();
                }
            });
        });
    </script>
{% endblock javascripts %}
