{% extends 'organization/admin/components/org.admin.base.html.twig' %}

{% block rightCol %}
    {{ parent() }}

    <div x-data="{
            sort:'name',
            direction:'asc',
            selectionMade: false,
            selectedTotal: 0,
            selectedAll: false,
            showSuspended: false,
            isFilterOpen: false,
            startingSlots: {% if organization.servicePlan.userPool.balance??false and organization.isPersonalGroup %}{{ organization.servicePlan.userPool.balance }}{% else %}true{% endif %},
            remainingSlots: {% if organization.servicePlan.userPool.balance??false and organization.isPersonalGroup %}{{ organization.servicePlan.userPool.balance }}{% else %}true{% endif %}
        }"
         x-effect="console.log(remainingSlots)"
         id="member-admin-utility">

        <form action="{{ path('organization:admin:contacts:group-invite',{'organization':organization.slug}) }}" class="" name="selected-members-actions" id="selected-members-actions" method="post"></form>
        <div {% if orggroupinvitecontpath == currpath %} class="sticky bg-base-100 pb-2" style="top:{{stickytoporggroupinvite}};z-index:4000;"{% endif %}>
    {% if organization.isPersonalGroup %}
            <div class="message info">
                <div class="text-base">Your group has 
                    <strong id="remaining-slots-display" x-text="remainingSlots"></strong> 
                    user slots remaining of the
                    <strong>{{ resolve_setting('service-plan-default-user-limit',organization.servicePlan) }}</strong>
                    allowed
                </div>
            </div>
    {% endif %}
            <div class="shadow-clubster border-b border-gray-200 border-solid rounded-lg mb-4 ">
                <div class="flex flex-row relative divide-solid divide-gray-300 divide-x-1">
                    <div class="flex-none pl-3 pr-3 flex items-center pointer-events-none bg-lightBlue text-xs text-baseBlue font-bold">
                        Search Administrators
                    </div>
                    <div class="flex-grow flex items-center flex relative">
                        <input type="text" name="member-search" id="member-search" autocomplete="off"
                            class="flex-grow block w-full p-2 sm:text-sm border-gray-300" 
                            placeholder="Search by {% if organization.isPersonalGroup %}Name{% else %}Name or Email{% endif %}"
                        >
                        <div id="clear-search-btn"
                            class="bg-white m-2 text-gray-700 xl:hover:text-gray-900 absolute right-0"
                            style="align-self: center; display:none">
                            <i class="fas fa-times cursor-pointer" id="clear-search"></i>
                        </div>
                    </div>
                </div>
            </div>
    {% if organization.isPersonalGroup == false %}
            <div class="w-full grid grid-cols-3 grid-rows-1 grid-flow-col gap-x-3 mb-4">
                <div class="label-info text-sm items-center shadow-clubster">
                    <input checked type="checkbox" id="show-staff" 
                        class="statusFilter bg-blue-100 border border-blue-800 mr-3" 
                        x-on:click="showStaff($event)" 
                    />
                    <label for="show-staff">Show Club Staff</label>
                </div>
                <div class="label-success label-border label-hollow text-sm items-center shadow-clubster">
                    <input type="checkbox" id="show-members" 
                        class="statusFilter bg-grey-100 border border-grey-800 mr-3" 
                        x-on:click="showMembers($event)" 
                    />
                    <label for="show-members">Show Club Members</label>
                </div>
                <div class="label-success text-sm items-center shadow-clubster">
                    <input checked type="checkbox" id="show-existing-admin" 
                        class="statusFilter bg-green-100 border border-green-800 mr-3" 
                        x-on:click="showExistingAdmin($event)" 
                    />
                    <label for="show-existing-admin">Show Existing Group Administrators</label>
                </div>
            </div>
    {% endif %}
            <div class="mb-4">
                <button id="add-selected" x-bind:disabled="!selectionMade" 
                        name="action" value="invite"
                        x-bind:class="{disabled:!selectionMade}" class="member-add-button disabled clubster-button small" form="selected-members-actions">
                    Add Selected {% if organization.isPersonalGroup %}Friends{% endif %} as Administrators
                </button>
            </div>
            <div id="total-selected" class="w-full grid grid-cols-3 grid-rows-1 grid-flow-col gap-x-3">
                <div></div>
                <div></div>
                <div class="text-right">
                    <div class="font-bold text-baseBlue">
                        <span id="selected-total" class="selected-total"
                              x-text="selectedTotal">0</span>&nbsp;Selected
                    </div>
                </div>
            </div>
        </div>
        <div class="shadow-clubster overflow-hidden border-b border-gray-200 rounded-lg">
            <table class="min-w-full divide-y-1 divide-x-1" id="member-table">
                <thead class="bg-lightBlue">
                <tr class="ignore-tr divide-solid divide-x-1 divide-gray-300">
                    <th scope="col" 
                        class="px-2 align-middle w-4" 
                        style="border:transparent"
                        >
                        <input type="checkbox" class="rounded" id="select-all-checkbox"
                               x-bind:title="(selectedAll?'Clear This Box to Unselect All':'Check This Box to Select All')"
    {% if organization.isPersonalGroup %}
                               x-bind:disabled="remainingSlots===0"
    {% endif %}
                               x-on:click="selectAllMembers($event);selectedAll=$event.target.checked;"
                               x-on:change="selectionMade = ($('.memberSelect:checked').length > 0)">
                    </th>
                    <th scope="col" 
                        x-on:click="sort='name'; direction=(direction=='asc'?'desc':'asc');console.log(sort);filterContacts({sort:sort,direction:direction})"
                        class="px-6 py-3 pl-1 text-left text-xs font-bold text-baseBlue uppercase tracking-wider border-l-0 hover:bg-medBlue hover:text-white cursor-pointer"
                        style="width: 370px; border-left:transparent"
                        >
                        Name
                        <i class="ml-0.5 fas fa-chevron-up" x-show="sort=='name'&&direction=='asc'" style="display:none"></i>
                        <i class="ml-0.5 fas fa-chevron-down" x-show="sort=='name'&&direction=='desc'" style="display:none"></i>
                    </th>
                </tr>
                </thead>
                <tbody id="member-search-loading">
                <tr>
                    <td colspan="6">
                        <div class="p-4 m-4 bg-lightBlue border-baseBlue border-1 border-solid text-xs font-bold text-baseBlue uppercase tracking-wider text-center">
                            <i class="fas fa-spinner fa-pulse"></i> Loading Member Roster. Please Wait.
                        </div>
                    </td>
                </tr>
                </tbody>
                <tbody id="member-search-empty" style="display: none">
                <tr>
                    <td colspan="6">
                        <div class="p-4 m-4 bg-lightBlue border-baseBlue border-1 border-solid text-xs font-bold text-baseBlue uppercase tracking-wider text-center">
                            No results.
                        </div>
                    </td>
                </tr>
                </tbody>
                <tbody class="bg-white divide-y-1 divide-solid divide-gray-200" id="member-search-results" style="display: none">
                </tbody>
            </table>
        </div>
    </div>

{% endblock rightCol %}

{% block stylesheets %}
    {{ parent() }}
    <style>

    </style>
{% endblock stylesheets %}

{%- block javascripts -%}
<script>
    let existingMembers = {{ existingMembers|json_encode|raw }};
    let suspendedMembers = {{ suspendedMembers|json_encode|raw }};

    let memberSearchXHR = null;

    let loadMembers = (query, statusFilters, sort) => {
    	$('#member-search-results').hide();
    	$('#member-search-empty').hide();
    	$('#member-search-loading').show();

    	if (memberSearchXHR != null) {
    		memberSearchXHR.abort();
    	}
    	memberSearchXHR = $.ajax({
    		type: 'POST',
    		url: "{{ path('organization:admin:contacts:searchNewAdmins', {'organization':organization.slug}) }}",
    		data: {
    			search: query,
    			order: sort.sort,
    			orderDirection: sort.direction,
    		},
    		dataType: "json",
    		encode: true,
    		async: true,
{% if api_env == 'prod' or api_env == 'bluejay' %}
    		timeout: 60000,
{% endif %}
    		error: function(xhr, status, error) {
                $('#select-all-checkbox').prop('checked', false);
                selectAllMembers({target:{checked:false}});
    			console.log('contact/groupinvite - status:[' + status + ']  responseText:[' + xhr.responseText + ']  error:[' + error + ']');
    		},
    		success: function(members) {
    			$('#member-search-results').empty();
                $('#select-all-checkbox').prop('checked', false);
                selectAllMembers({target:{checked:false}});

    			$('#member-search-loading').hide();
    			if (members.length == 0) {
    				$('#member-search-empty').show();
    				return;
    			}

    			$('#member-search-results').show();
    			$.each(members, function(k, member) {
    				if (member.hasOwnProperty('friend')) {
    					member.user = member.friend;
    				}

    				let newrow = renderMemberRow(member);

{% if organization.isPersonalGroup == false %}
    				$(newrow).hide();
    				if ($('#show-existing-admin').is(':checked') && $(newrow).hasClass('is-existing-admin')) {
    					$(newrow).show();
    				}
    				if ($('#show-staff').is(':checked') && $(newrow).hasClass('is-staff')) {
    					$(newrow).show();
    				}
    				if ($('#show-members').is(':checked') && $(newrow).hasClass('is-member')) {
    					$(newrow).not('.is-existing-admin').not('.is-staff').show();
    				} 
{% endif %}
    				$('#member-search-results').append(newrow);
    			});
    			timeago();
    		}
    	});
    }

    let filterContacts = () => {
    	let query = null;
    	let statusFilters = [];
    	if ($('#member-search').length) {
    		query = $('#member-search').val();
    		if (query.length > 0) {
    			$('#clear-search-btn').show();
    			$('#member-search').attr('data-query', query);
    		} else {
    			$('#clear-search-btn').hide();
    		}

    		$('.statusFilter').each(function(k, elm) {
    			elm = $(elm);
    			if (elm.is(':checked')) {
    				statusFilters.push(elm.val());
    			}
    		});

    		if (query.length === 0) {
    			query = null;
    		}
    	}

    	let sort = {
    		sort: "name",
    		direction: "asc"
    	};
    	if ($('#member-admin-utility')[0]._x_dataStack != undefined) {
    		sort = {
    			sort: $('#member-admin-utility')[0]._x_dataStack[0].sort,
    			direction: $('#member-admin-utility')[0]._x_dataStack[0].direction
    		};
    	}
    	loadMembers(query, statusFilters, sort);
    }

    $('#member-search').on('keyup', () => {
    	filterContacts();
    });

    $('#clear-search').on('click', function() {
    	$('#member-search').val('');
    	$('#member-search').trigger('keyup');
    });

    function disableControls() {
    	$('.member-add-button').attr('disabled', 'disabled');
    	$('.member-add-button').prop('disabled', 'disabled');
    	$('input.memberSelect').attr('disabled', 'disabled');
    	$('input.memberSelect').prop('disabled', 'disabled');
    	$('input#select-all-checkbox').attr('disabled', 'disabled');
    	$('input#select-all-checkbox').prop('disabled', 'disabled');
    };

    $('#add-selected').on('click', function(e) {
    	// must use setTimeout(), that's because if you don't 
    	// the form never gets submitted
    	//disableButton('#'+e.target.id);
    	setTimeout(disableControls, 100);
    	let actionform = document.getElementById('selected-members-actions');
    	actionform.submit();
    });

{% if organization.isPersonalGroup == false %}
    let showStaff = (event) => {
    	if (event.target.checked) {
    		$('.is-staff').not('.is-existing-admin').show();
    	} else {
    		$('.is-staff').not('.is-existing-admin').children('td').children('.memberSelect').prop('checked', false);
    		$('.is-staff').not('.is-existing-admin').hide();
    	}
    	$('#member-admin-utility')[0]._x_dataStack[0].selectedTotal = $('tr:visible .memberSelect:checked').length;
        let tmp = (
    			$('tr:visible .memberSelect:checked').length === $('tr:visible .memberSelect').length
    		) && (
    			$('#show-staff').prop('checked') || $('#show-members').prop('checked')
        );

    	$('#member-admin-utility')[0]._x_dataStack[0].selectedAll = tmp;
    	$('#select-all-checkbox').prop('checked', tmp);
    }

    let showMembers = (event) => {
    	if (event.target.checked) {
    		if ($('#show-staff').is(':checked')) {
    			$('tr').not('.ignore-tr').not('.is-existing-admin').show();
    		} else {
    			$('tr').not('.ignore-tr').not('.is-existing-admin').not('.is-staff').show();
    		}
    	} else {
    		$('tr').not('.ignore-tr').not('.is-existing-admin').not('.is-staff').children('td').children('.memberSelect').prop('checked', false);
    		$('tr').not('.ignore-tr').not('.is-existing-admin').hide();

    		if ($('#show-staff').is(':checked')) {
    			$('.is-staff').not('.is-existing-admin').show();
    			if ($('#show-existing-admin').is(':checked')) {
    				$('.is-staff.is-existing-admin').show();
    			}
    		}
    	}
    	$('#member-admin-utility')[0]._x_dataStack[0].selectedTotal = $('tr:visible .memberSelect:checked').length;
        let tmp = (
    			$('tr:visible .memberSelect:checked').length === $('tr:visible .memberSelect').length
    		) && (
    			$('#show-staff').prop('checked') || $('#show-members').prop('checked')
        );
    	$('#member-admin-utility')[0]._x_dataStack[0].selectedAll = tmp;
    	$('#select-all-checkbox').prop('checked', tmp);
    }

    let showExistingAdmin = (event) => {
    	if (event.target.checked) {
    		$('.is-existing-admin').show();
    	} else {
    		$('.is-existing-admin').hide();
    	}
    };
{% endif %}

    let elmChecked = (ev) => {
        let tmp = ($('tr:visible .memberSelect:checked').length === $('tr:visible .memberSelect').length);
    	$('#member-admin-utility')[0]._x_dataStack[0].selectedAll = tmp;
    	$('#select-all-checkbox').prop('checked', tmp);
    	$('#member-admin-utility')[0]._x_dataStack[0].selectedTotal = $('tr:visible .memberSelect:checked').length;
    	let remainingSlots = $('#member-admin-utility')[0]._x_dataStack[0].remainingSlots;
    	let remainingSlotsType = typeof remainingSlots;
    	if(ev.target.checked) {
            if(remainingSlotsType === 'number') {
                remainingSlots -= 1;
            }
        } else {
            if(remainingSlotsType === 'number') {
                remainingSlots += 1;
            }
        }
        $('#member-admin-utility')[0]._x_dataStack[0].remainingSlots = remainingSlots;
    };

    let selectAllMembers = (event) => {
    	let remainingSlots = $('#member-admin-utility')[0]._x_dataStack[0].remainingSlots;
    	let remainingSlotsType = typeof remainingSlots;
    	if (event.target.checked) {
    		for(indx = 0, len = $('.memberSelect').length; indx < len; indx++) {
    			let el = $('.memberSelect')[indx];
    			if (el.dataset.existing) {
    				continue;
    			}
    			if (remainingSlots > 0 || remainingSlots === true) {
    				if ($(el).is(':hidden') || $(el).is(':disabled')) {
    					continue;
    				}
    				if (remainingSlotsType === 'number') {
    					remainingSlots -= 1;
                        $(el).prop('checked', true)
                        $('#member-admin-utility')[0]._x_dataStack[0].remainingSlots = remainingSlots;
    				}
    			}
    		}
    	} else {
    		$('.memberSelect').map((indx, el) => {
    			if (el.dataset.existing) {
    				return;
    			}
                $(el).prop('checked', false)
    		});
    		if (remainingSlotsType === 'number') {
    			$('#member-admin-utility')[0]._x_dataStack[0].remainingSlots = $('#member-admin-utility')[0]._x_dataStack[0].startingSlots;
    		}
    	}
{% if organization.isPersonalGroup == false %}
    	$('tr:visible .memberSelect').prop('checked', event.target.checked);
{% endif %}
    	$('#member-admin-utility')[0]._x_dataStack[0].selectedTotal = $('tr:visible .memberSelect:checked').length;
    }

    function renderMemberRow(member) {
    	let row = document.createElement('tr');

    	let avatarImage = member.user.avatar.image.uri ?? '';
    	let firstName = member.user.first_name ?? member.user.firstName ?? '';
    	let lastName = member.user.last_name ?? member.user.lastName ?? '';
    	let intials = member.user.avatar.initials ?? '??';
    	if (firstName.length == 0 && lastName.length == 0) {
    		firstName = 'Unknown Name';
    	}

    	if (member.user.id != null) {
    		member.id = member.user.id;
    	}

    	let suspended = (suspendedMembers.indexOf(member.id) !== -1);

    	if (member.type === 'member') {
    		row.classList.add('is-member');
    	} else {
    		row.classList.add('is-staff');
    	}

    	if (existingMembers.indexOf(member.id) !== -1) {
    		row.classList.add('is-existing-admin');
    	}

    	let avatar = '<sl-avatar style="--size: 2.5rem" class="sl-avatar" image="' + avatarImage + '" initials="' + intials + '"></sl-avatar>';

    	row.innerHTML = `
        <tr>
            <td class="px-2 whitespace-nowrap w-4 align-middle text-center">
                    <input type="checkbox" name="selectedMembers[]"
                    value="${member.id}"
                    ${existingMembers.indexOf(member.id) == -1 ?
                    'x-on:change="elmChecked($event);' +
                    'selectionMade = ($(\'.memberSelect:checked\').length > 0);"' +

                    'x-bind:disabled="(remainingSlots===0 && $el.checked == false) || '+ suspended + '" ' +
                    'form="selected-members-actions"' +
                    'class="memberSelect rounded focus:ring-medBlue text-medBlue"'
                    :'disabled="disabled" checked="checked" data-existing=true class="rounded focus:ring-medBlue text-medBlue"'
                    }
                    >
            </td>
            <td class="px-6 pl-1 py-2 whitespace-nowrap align-middle">
                <div class="flex items-center align-middle">
                    <div class="flex-shrink-0 h-10 w-10 align-middle">
                        ${avatar}
                    </div>
                    <div class="ml-4" style="width: 400px">
                        <div class="text-sm font-medium text-gray-900 truncate">${ firstName } ${ lastName }</div>
{% if organization.isPersonalGroup == false %}
                        <div class="text-sm font-medium text-gray-500 truncate">${ member.user.email }</div>
{% endif %}
                    </div>
                    <div class="stretch text-right ml-4" style="width:26px;">
                        ${member.type === 'contact'?'<span class="label-info text-center inline-block" title="">Club Staff</span>':''}
                    </div>
                    <div class="stretch text-right ml-4" style="width:26px;">
                        ${existingMembers.indexOf(member.id) > -1 ? '<span class="label-success text-center inline-block" title="Already In Group">Group Administrator</span>'
                        :(suspended ? '<span class="label-danger text-center inline-block" title="Suspended From Group">Suspended From Group</span>' : '')}
                    </div>
                </div>
            </td>
        </tr>`;
    	return row;
    }
    filterContacts(); 
</script>
{%- endblock javascripts -%}
