{% extends 'layout/auth.base.html.twig' %}

{% block title %}Form Builder - Clubster{% endblock %}

{% block leftCol %}
    <div class="content-box">
        <div id="form-builder-palette">
            {# Form builder palette will be rendered here #}
        </div>
    </div>
    <div class="content-box">
        <div id="form-builder-properties">
            {# Form element properties editor will be rendered here #}
        </div>
    </div>
{% endblock %}

{% block rightCol %}
    <div class="content-box">
        <div id="form-builder-grid" style="min-height: 600px;">
            {# Form builder grid will be rendered here #}
        </div>

        {# Loading indicator #}
        <div id="form-builder-loading" class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-clubster-500"></div>
            <p class="mt-2 text-gray-600">Loading Form Builder...</p>
        </div>
    </div>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .form-builder-grid {
            background-image: radial-gradient(circle, #dee2e6 1px, transparent 1px);
            background-size: 20px 20px;
            min-height: 500px;
        }
        
        .palette-item {
            transition: all 0.2s ease;
        }
        
        .palette-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        #form-builder-loading {
            display: none;
        }
        
        .form-builder-loaded #form-builder-loading {
            display: block;
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('js/form-builder') }}
{% endblock javascripts %}
