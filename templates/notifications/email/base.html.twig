{% macro resize(src, width, height) %}
{#    {% if width > 0 and height > 0 %}#}
{#        {% set ratio = height/width %}#}
{#        {% set newHeight = ratio * 600 %}#}
        <img src="{{src}}" alt="" style="display: block; outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; border: 0;" width="600" norescale="norescale"/>
{#    {% endif %}#}
{% endmacro %}

{% import _self as self %}
<!DOCTYPE html>
<html lang="en" xmlns:v="urn:schemas-microsoft-com:vml">
<head>
    <meta charset="utf-8">
    <meta name="x-apple-disable-message-reformatting">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no">
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings xmlns:o="urn:schemas-microsoft-com:office:office">
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <style>
        td,th,div,p,a,h1,h2,h3,h4,h5,h6 {font-family: "Segoe UI", sans-serif; mso-line-height-rule: exactly;}
        img:not([norescale]) {max-width: 600px !important; height: auto !important;}
    </style>
    <![endif]-->
    <!--[if gte mso 7]><xml>
        <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
    </xml><![endif]-->
    <title>{{ title??'' }}</title>
    <style>
        .visited-text-lightBlue:visited {
            color: #eff4f9 !important;
        }
        .hover-bg-blue-600:hover {
            background-color: #2563eb !important;
        }
        @media (max-width: 600px) {
            .sm-w-full {
                width: 100% !important;
            }
            .sm-py-32 {
                padding-top: 32px !important;
                padding-bottom: 32px !important;
            }
            .sm-px-24 {
                padding-left: 24px !important;
                padding-right: 24px !important;
            }
            .sm-leading-32 {
                line-height: 32px !important;
            }
        }
    </style>
</head>
<body style="margin: 0; width: 100%; padding: 0; word-break: break-word; -webkit-font-smoothing: antialiased; background-color: #325c84;">
<div style="display: none;">{{preheader??''}}&#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &zwnj;
    &#160;&#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &#847; &zwnj;
    &#160;&#847; &#847; &#847; &#847; &#847; </div>
<div role="article" aria-roledescription="email" aria-label="{{title??''}}" lang="en">
    <table style="width: 100%; font-family: ui-sans-serif, system-ui, -apple-system, 'Segoe UI', sans-serif;" cellpadding="0" cellspacing="0" role="presentation">
        <tr>
            <td align="center">
                <table class="sm-w-full" style="width: 100%;" cellpadding="0" cellspacing="0" role="presentation">
                    <tr>
                        <td class="sm-py-32 sm-px-24" style="background-image: url('{{organization.cover.image.uri??'https://media.clubster.com/mute_geo_bg.jpg'}}'); background-size: cover; padding: 64px; text-align: center; ">
                            <table cellpadding="0" cellspacing="0" border="0" width="100%" style="width:100%">
                                <tr>
                                    <td valign="top">
                                        <!--[if gte mso 9]>
                                        <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="mso-width-percent:1000;">
                                            <v:fill type="frame" src="{{organization.cover.image.uri??'https://media.clubster.com/mute_geo_bg.jpg'}}" color="#325C84" size="100%,100%" />
                                            <v:textbox style="mso-fit-shape-to-text:true" inset="0,0,0,0">
                                        <![endif]-->
                                        <div>
                                            <table style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-collapse: collapse; border-spacing: 0px;" width="100%" cellspacing="0" cellpadding="0" bgcolor="transparent" align="center">
                                                <tr style="border-collapse: collapse;">
                                                    <td align="left" style="margin: 0; padding: 20px 20px 0;">
                                                        <table width="100%" cellspacing="0" cellpadding="0" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-collapse: collapse; border-spacing: 0px;">
                                                            <tr style="border-collapse: collapse;">
                                                                <td width="100%" valign="top" align="center" style="margin: 0; padding: 0;">
                                                                    <table width="100%" cellspacing="0" cellpadding="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-collapse: collapse; border-spacing: 0px;">
                                                                        <tr style="border-collapse: collapse;">
                                                                            <td style="font-size: 0px; margin: 0; padding: 0;" align="center">
                                                                                <a target="_blank" href="{{organization.url??'https://www.clubster.com'}}" style="-webkit-text-size-adjust: none; -ms-text-size-adjust: none; mso-line-height-rule: exactly; font-family: roboto, 'helvetica neue', helvetica, arial, sans-serif; font-size: 14px; text-decoration: underline; color: #1376C8;">
                                                                                    <img src="{{organization.avatar.image.uri??'https://media.clubster.com/clubster_logo.png'}}" alt="" style="display: block; outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; width: 192px; height: 192px; border: 0; border-radius: 200px" width="192" height="192"/>
                                                                                </a>
                                                                            </td>
                                                                        </tr>
                                                                        <tr style="border-collapse: collapse;">
                                                                            <td height="48" align="center" style="margin: 0; padding: 0;"></td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <!--[if gte mso 9]>
                                        </v:textbox>
                                        </v:rect>
                                        <![endif]-->
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
                <table class="sm-w-full" style="margin-top: 8px; width: 600px;" cellpadding="0" cellspacing="0" role="presentation">
                    <tr>
                        <td align="center" class="sm-px-24">
                            <table style="width: 100%;" cellpadding="0" cellspacing="0" role="presentation"> <tr>
                                    <td class="sm-px-24" style="border-top-left-radius: 4px; border-top-right-radius: 4px; background-color: #ffffff; padding: 48px; padding-bottom: 0; text-align: left; font-size: 16px; line-height: 24px; color: #1f2937;">
                                        <p class="sm-leading-32" style="margin: 0; margin-bottom: 24px; font-size: 24px; font-weight: 600; color: #325c84;">{{headline}}</p>
                                        <p style="margin: 0; margin-bottom: 24px;">{{ body }}</p>
                                        {% if (media.totalMedia??0|number_format) > 1 %}
                                            <p style="margin: 0; margin-bottom: 24px;">
                                                <br>
                                                To view all the <strong>photos{% if attachments.totalAttachments??0|number_format > 0 %}, attachments,{% endif %}</strong> and the entire post, click the "View More" button below.
                                            </p>
                                        {% else %}
                                            {% if attachments.totalAttachments??0|number_format > 0 %}
                                            <p style="margin: 0; margin-bottom: 24px;">
                                                <br>
                                                This post contains <strong>attachments</strong>. To view them and the entire post, click the "View More" button below.
                                            </p>
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if image??false %}
                                <tr>
                                    <td style="margin: 0; margin-bottom: 24px;">
                                        <table width="100%" cellspacing="0" cellpadding="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-collapse: collapse; border-spacing: 0px;">
                                            <tr style="border-collapse: collapse;">
                                                <td style="font-size: 0px; margin: 0; padding: 0;" align="center">
                                                    {% if notificationUrl??false %}
                                                    <a href="{{ notificationUrl }}" class="hover-bg-blue-600" style="display: inline-block; border-radius: 4px; background-color: #3b82f6; padding-top: 16px; padding-bottom: 16px; padding-left: 24px; padding-right: 24px; text-align: center; font-size: 16px; font-weight: 600; color: #ffffff; text-decoration: none;">
                                                    {% endif %}
                                                    {{ self.resize(media.first.file.uri, media.first.file.width, media.first.file.height) }}
                                                    {% if notificationUrl??false %}
                                                    </a>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                {% endif %}

                                {% if notificationUrl??false %}
                                <tr>
                                    <td class="sm-px-24" style="border-bottom-right-radius: 4px; border-bottom-left-radius: 4px; background-color: #ffffff; padding: 48px; padding-top: 0; text-align: center; font-size: 16px; line-height: 24px; color: #1f2937;">
                                        <div style="line-height: 100%; margin-top: 24px">
                                            <a href="{{ notificationUrl }}" class="hover-bg-blue-600" style="display: inline-block; border-radius: 4px; background-color: #3b82f6; padding-top: 16px; padding-bottom: 16px; padding-left: 24px; padding-right: 24px; text-align: center; font-size: 16px; font-weight: 600; color: #ffffff; text-decoration: none;">
                                                <!--[if mso]><i style="letter-spacing: 24px; mso-font-width: -100%; mso-text-raise:30px;">&#8202;</i><![endif]-->
                                                {% if media.totalMedia??0|number_format > 1 %}
                                                <span style="mso-text-raise: 16px;">View {{ media.totalMedia }} More Photos &rarr;</span>
                                                {% else %}
                                                <span style="mso-text-raise: 16px;">View More On Clubster &rarr;</span>
                                                {% endif %}
                                                <!--[if mso]><i style="letter-spacing: 24px; mso-font-width: -100%;">&#8202;</i><![endif]-->
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endif %}

                                <tr>
                                    <td style="height: 48px;"></td>
                                </tr>

                                <tr>
                                    <td style="padding-left: 24px; padding-right: 24px; text-align: center; font-size: 12px; color: #9ca3af;">
                                        {%if unsubscribeLink??false %}
                                            <p style="margin: 0; margin-bottom: 4px; color: #95b5d4;"><a href="{{ unsubscribeLink }}" class="visited-text-lightBlue" style="color: #eff4f9;">Unsubscribe From {{ organization.name }} Emails</a></p>
                                        {% endif %}
                                        <p style="margin: 0; margin-bottom: 4px; color: #95b5d4;">This email was sent by <a href="https://www.clubster.com" class="visited-text-lightBlue" style="color: #eff4f9;">clubster.com</a></p>

                                        <br />
                                        <p style="-webkit-text-size-adjust: none; -ms-text-size-adjust: none; mso-line-height-rule: exactly; font-size: 12px; font-family: roboto, 'helvetica neue', helvetica, arial, sans-serif; line-height: 50px; color: #9FC5E8; margin: 0; height:50px">
                                            <a href="https://apps.apple.com/us/app/clubster/id1488696130?itsct=apps_box&itscg=30200" style="display:inline-block; height:50px" height="50">
                                                <img src="https://media.clubster.com/apple_appstore.png" alt="Download on the App Store" style="height: 40px;" height="40">
                                            </a>
                                            <span>&nbsp;&nbsp;</span>
                                            <a href="https://play.google.com/store/apps/details?id=com.clubster.clubsterbeta&pcampaignid=pcampaignidMKT-Other-global-all-co-prtnr-py-PartBadge-Mar2515-1" style="display:inline-block; height:50px" height="50">
                                                <img alt="Get it on Google Play" src="https://media.clubster.com/google_play.png" style="height: 40px;" height="40">
                                            </a>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
</body>
</html>
