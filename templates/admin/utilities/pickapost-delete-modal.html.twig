        <div x-bind:id="type+'-remove-modal'" class="fixed z-10 inset-0 overflow-y-auto"
             x-cloak
             x-show="showDeleteModal">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 transition-opacity" aria-hidden="true"
                     x-cloak
                     x-show="showDeleteModal"
                     x-transition:enter="ease-out duration-300"
                     x-transition:enter-start="opacity-0"
                     x-transition:enter-end="opacity-100"
                     x-transition:leave="ease-in duration-200"
                     x-transition:leave-start="opacity-100"
                     x-transition:leave-end="opacity-0"
                >
                    <div class="absolute inset-0 bg-gray-800 opacity-75"></div>
                </div>

                <!-- This element is to trick the browser into centering the modal contents. -->
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

                <div x-cloak
                     x-show="showDeleteModal"
                     x-transition:enter="ease-out duration-300"
                     x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                     x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                     x-transition:leave="ease-in duration-200"
                     x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                     x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                     class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full" role="dialog" aria-modal="true" aria-labelledby="modal-headline">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">
                                    Delete Selected <span class="capitalize" x-text="type"></span>
                                </h3>
                                <div class="mt-2">
                                    <p class="text-gray-500">
                                        You have selected <span x-text="selectionCount" x-bind:id="type+'-remove-modal-selected'"></span>
                                        <span x-text="type"></span>.
                                    </p>
                                    <p class="text-gray-500">
                                        Are you sure you want to delete the selected 
                                        <span x-text="type"></span>?
                                        <br><br>
                                        Deletion is permanent. If you save it before deleting it can be uploaded if necessary.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="px-4 py-3 sm:px-6 sm:flex">
                        <button type="button" x-on:click="disableOnClick($event, type);deleteSelectedItems($event, type);showDeleteModal = false" 
                                value="delete"
                                class="w-full inline-flex justify-center rounded-md clubster-danger-button hollow mr-2">
                            Delete&nbsp;<span class="capitalize" x-text="type"></span>
                        </button>
                        <button type="button" x-on:click="showDeleteModal = false" 
                            class="w-full inline-flex justify-center rounded-md clubster-button ml-2">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>

