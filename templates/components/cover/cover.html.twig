{% if app.user is not empty %}
    {% set isAnon = false %}
{% else %}
    {% set isAnon = true %}
{% endif %}
{% if user is defined %}
    {% set entity = user %}
{%  endif %}

{% if org is defined %}
    {% set entity = org %}
{%  endif %}

{% if organization is defined %}
    {% set entity = organization %}
{%  endif %}

{% if group is defined %}
    {% set entity = group %}
{%  endif %}

{# jxmot 20250717 - not used #}
{% if subOrganization is defined %}
    {% set subEntity = organization %}
{%  endif %}

{% if showControls is not defined %}
    {% if isAnon == true %}
        {% set showControls = false %}
    {% else %}
        {% set showControls = true %}
    {%  endif %}
{%  endif %}

{% set cover = entity.cover??false %}
{% set avatar = entity.avatar??false %}
{% set baseURL = false %}

{% if entity is user or (user is defined and entity == user) %}
    {% set name %}{{entity.firstName??entity.first_name}} {{entity.lastName??entity.last_name}}{% endset %}
    {% set initials %}{{(entity.firstName??entity.first_name)[:1]|upper}}{{(entity.lastName??entity.last_name)[:1]|upper}}{% endset %}
    {% if showControls %}
        {% set controls %}{% endset %}
        {% if entity.id??false and entity.id == app.user.id %}
            {% set controls %}
                <a href="{{ path('post:create') }}">Create Status Update</a>
                <a href="{{ path('user:account:profile:edit') }}">Manage Account</a>
            {% endset %}
        {% elseif friendship is defined and friendship is not empty and friendship.status == 'accepted' %}
            {% set controls %}
                {% if friendship.subscription.allow_feed %}
                    <a href="{{ path('user:unfollow', {'userId':friendship.friend.id}) }}">Unfollow</a>
                {% else %}
                    <a href="{{ path('user:follow', {'userId':friendship.friend.id}) }}">Follow</a>
                {% endif %}
            {% endset %}
        {% endif %}

        {% set controls %}
            {{ controls }}
            {% if (friendship is defined and friendship is not empty and (friendship.status == 'accepted' or friendship.status == 'pending') and get_setting_value(entity,'profile-allowed-viewer') == 'friends') or get_setting_value(entity,'profile-allowed-viewer') == 'everybody' %}
                {% if has_attribute_value(entity,'facebook', false) %}
                    <a href="{{ get_attribute_value(entity,'facebook') }}" target="_blank" class="clear-style text-white p-0 bg-transparent text-4xl inline-block" style="margin-top: -4px"><i class="fab fa-facebook-square"></i></a>
                {% endif %}

                {% if has_attribute_value(entity,'linkedin', false)  %}
                    <a href="{{ get_attribute_value(entity,'linkedin') }}" target="_blank" class="clear-style text-white p-0 bg-transparent text-4xl inline-block" style="margin-top: -4px"><i class="fab fa-linkedin"></i></a>
                {% endif %}

                {% if has_attribute_value(entity,'twitter', false) %}
                    <a href="{{ get_attribute_value(entity,'twitter') }}" target="_blank" class="clear-style text-white p-0 bg-transparent text-4xl inline-block" style="margin-top: -4px"><i class="fab fa-twitter-square"></i></a>
                {% endif %}

                {% if has_attribute_value(entity,'instagram', false) %}
                    <a href="{{ get_attribute_value(entity,'instagram') }}" target="_blank" class="clear-style text-white p-0 bg-transparent text-4xl inline-block" style="margin-top: -4px"><i class="fab fa-instagram-square"></i>></a>
                {% endif %}
            {% endif %}
        {% endset %}
    {% endif %}
{% endif %}

{# {% if entity is organization  %}
#}
{% if organization??false  %}
    {% set name %}{{organization.name ?? organization.title ?? 'Feed'}}{% endset %}
    {% set initials %}{{organization.name[:1]|upper}}{% endset %}
    {% if organization.parentOrganization %}
        {% set subname %}{{organization.parentOrganization.name ?? organization.parentOrganization.title}}{% endset %}
    {% endif %}
    {% set baseURL = path('club:index', {organization:organization.slug})%}
    {% if showControls %}
        {% set controls %}
            {% include 'organization/components/cover/controls.html.twig' with {organization:organization, entity:organization} %}
        {% endset %}
    {% endif %}
{% endif %}

<div id="cover" style="{% if cover %}background-image: url({{ cover.image.uri }}){% endif %}">
    <div id="cover-content">
        {#<div class="avatar cover-avatar">#}
            {% if baseURL and app.user.id??false == true %}<a href="{{ baseURL }}">{% endif %}
            {% if (entity is user or (user is defined and entity == user)) and entity.id??false and entity.id == app.user.id %}
            <div class="relative">
                <a title="View Your Profile" href="{{ path('user:index', {userId: app.user.id}) }}">
            {% endif %}
                {% include 'components/avatar.html.twig' with {avatar: avatar, type:'cover', size:220, initials:initials??'??'} %}
            {% if baseURL and app.user.id??false == true %}</a>{% endif %}
            {% if (entity is user or (user is defined and entity == user)) and entity.id??false and entity.id == app.user.id %}
                </a>
                {% set mngprofpath = path('user:account:profile:edit') %}
                {% if currpath != mngprofpath %}
                <div class="group absolute bottom-0 right-0 text-white z-1 bg-baseBlue text-center rounded-lg p-2 pl-6 leading-3 shadow-clubster border-1 border-white border-solid">
                    <a href="{{ mngprofpath ~ '#profile_virtual_top' }}">
                        <i class="fas fa-edit absolute" style="left:10px; top: 10px"></i>
                        <span style="height:1.25rem;" class="inline-block overflow-hidden whitespace-nowrap leading-4 w-0 pl-0.5 group-hover:w-40 transition-all text-sm">
                            &nbsp;Manage Profile Photo
                        </span>
                    </a>
                </div>
                {% endif %}
            </div>
            {% endif %}
            {% if entity.parentOrganization??false %}
            <div class="-ml-16 -mt-2 rounded-full ring-3 ring-white shadow-cover">
                <a href="{{ path('club:index', {organization:entity.parentOrganization.slug}) }}">
                    {% include 'components/avatar.html.twig' with {avatar: entity.parentOrganization.avatar??false, type:'sub-cover', size:56, initials:entity.parentOrganization.name[:1]} %}
                </a>
            </div>
{#                {% set subname %}{{entity.parentOrganization.name ?? entity.parentOrganization.title}}{% endset %}#}
            {% endif %}
        {#</div>#}
        <div id="cover-details">
            <div id="cover-name" class="text-4xl font-black">
            {% if entity is user or (user is defined and entity == user) %}
                {% set lname = entity.lastName??entity.last_name %}
                {% set fname = entity.firstName??entity.first_name %}
                {% if (fname|length + lname|length) > 38 %}
                <div title="{{ fname }} {{ lname }}" class="pb-4 truncate" style="max-width:60rem;line-height:2.5rem;">{{ fname }}</div>
                <div title="{{ fname }} {{ lname }}" class="text-xl truncate" style="max-width:60rem;">{{ lname }}</div>
                {% else %}
                    {{ fname }} {{ lname }}
                {% endif %}
            {% else %}
                {% set nlen = name|length %}
                {% if nlen > 38 %}
                    {% if (nlen - 38) > 10 %}
                        {% set lines = break_string(name, 38) %}
                        {% if lines[0]??false %}
                <div title="{{ name }}" class="pb-4 truncate" style="max-width:60rem;line-height:2.5rem;">{{ lines[0]|raw }}</div>
                        {% endif %}
                        {% if lines[1]??false %}
                <div title="{{ name }}" class="text-xl truncate" style="max-width:60rem;">{{ lines[1]|raw }}</div>
                        {% endif %}
                    {% else %}
                <div title="{{ name }}" class="pb-4 truncate" style="max-width:60rem;line-height:2.5rem;">{{ name }}</div>
                    {% endif %}
                {% else %}
                    {{name}}
                {% endif %}
            {% endif %}
                {% if subname is defined %}
                <div id="cover-sub-name">
                    {% if entity.orientation == 'public' %}
                        An <span class="label-orgcontext-open label-orgcontext-shadow"><i class="fas fa-lock-open"></i> Open</span>
                    {% else %}
                        A <span class="label-orgcontext-private label-orgcontext-shadow"><i class="fas fa-lock"></i> Private</span>
                    {% endif %}
                    Group Of <a href="{{ path('club:index', {organization:entity.parentOrganization.slug}) }}">{{ subname }}</a>
                </div>
                {% endif %}
            </div>
            {% if entity.orgContext is defined and entity.orgContext == 'group' and entity.parentOrganization == null %}
            <div class="text-left mb-4">
                <span class="label-orgcontext-personal label-orgcontext-shadow">
                    <i class="fas fa-lock"></i> Personal Group
                </span>
            </div>
            {% endif %}
            {% if controls is defined and controls|trim is not empty %}
            <div id="cover-controls" class="flex flex-row items-center gap-2">
                {{ controls }}
            </div>
            {% endif %}

            {% if callout is defined and callout is not empty %}
            <div id="cover-callout">
                {{ callout }}
            </div>
            {% endif %}
        </div>
    </div>
</div>
