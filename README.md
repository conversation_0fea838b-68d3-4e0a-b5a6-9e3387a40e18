# Clubster

## Get Started
```shell
git clone https://github.com/clubster/clubster.git
cd clubster
git submodule update --init --recursive
```

## Services

### All Services
```shell
docker compose build
docker compose up
```

### cadvisor

CAdvisor (Container Advisor) is an open-source container monitoring tool built by Google. It provides detailed
information about resource usage and performance characteristics of running containers. It is specifically designed to
support container platforms like Docker and Kubernetes. It gathers and processes data related to memory, file I/O,
network I/O, and CPU usage, making it a valuable tool for developers who need to monitor their applications and
troubleshoot issues.

```shell
docker compose build cadvisor
docker compose up cadvisor
```

### clubster-api

The Clubster API is a backend service in the Clubster project. It is responsible for handling the legacy business
logic including but not limited to user management, club management, and all other key functionalities of Clubster.

Future status of the Clubster API is to be deprecated in favor of Clubster Falcon. The Clubster API will be used as a
authentication and authorization service for Clubster.

```shell
docker compose build clubster-api
docker compose up clubster-api
```

### clubster-falcon

The Clubster Falcon is the modern API for Clubster. This service handles most of the functionalities of the
current Clubster API. Clubster Falcon is poised to offer improved performance and more advanced
features. Falcon does not provide authentication or authorization services.

```shell
docker compose build clubster-falcon
docker compose up clubster-falcon
```

### clubster-mockingbird

Clubster Mockingbird is a real-time communication service. It is responsible for handling instant messaging

```shell
docker compose build clubster-mockingbird
docker compose up clubster-mockingbird
```

### clubster-nightingale

Clubster Nightingale is a high performance notification queue.

```shell
docker compose build clubster-nightingale
docker compose up clubster-nightingale
```

### clubster-puffin

Clubster Puffin is a service that handles statistics and analytics for Clubster.
    
```shell
docker compose build clubster-puffin
docker compose up clubster-puffin
```

### clubster-web

The Clubster Web is a frontend service in the Clubster. It is responsible for handling the web based user interface.

```shell
docker compose build clubster-web
docker compose up clubster-web
```

### prometheus

Prometheus is an open-source systems monitoring and alerting toolkit. It is designed for ollecting metrics from configured targets at given intervals, evaluating rule expressions, displaying results, and triggering alerts if certain conditions are observed. Prometheus stores all its data as time series, allowing for powerful queries and visualizations. It is widely used for monitoring applications, infrastructure, and services.

```shell
docker compose build prometheus
docker compose up prometheus
```

### redis

Redis is an open-source, in-memory data structure store used as a database, cache, and message broker. It supports various data structures such as strings, hashes, lists, sets, and more. Redis is known for its high performance, flexibility, and ease of use, making it suitable for real-time applications, session management, and caching.

```shell
docker compose build redis
docker compose up redis
```

### centrifugo

Centrifugo is an open-source real-time messaging server. It is designed to handle high-scale WebSocket connections, providing features like pub/sub messaging, presence information, and history storage. Centrifugo is often used to build real-time applications such as chat systems, live notifications, and collaborative tools. It supports various backends and can be integrated with existing applications to add real-time capabilities.

```shell
docker compose build centrifugo
docker compose up centrifugo
```

### mongodb

MongoDB is an open-source NoSQL database that uses a document-oriented data model. It is designed for high availability, horizontal scaling, and ease of development. MongoDB stores data in flexible, JSON-like documents, making it suitable for a wide range of use cases, including content management, real-time analytics, and mobile applications.

```shell
docker compose build mongodb
docker compose up mongodb
```

## Cheatsheet

**tail logs of a container**
```shell
docker compose logs -f <service name>
```

**stop all containers**
```shell
docker compose down
```

**stop a container**
```shell
docker compose stop <service name>
```

**start a container**
```shell
docker compose start <service name>
```

**remove a container**
```shell
docker compose rm <service name>
```

**remove all containers**
```shell
docker compose rm
```

**list all containers**
```shell
docker compose ps
```

# Configuration

## Environment Variables
The Clubster project uses environment variables to configure the services. The environment variables are defined in the `.env` file in the root directory of the project. The environment variables are used to configure the services and set the necessary parameters for the services to run correctly. 

### Service Environment Variables
The service environment variables are used to configure the shared configuration of the services. The stack environment variables are defined in the `.env` file in the root directory of the project
```dotenv
SERVICE_API_ENDPOINT=https://<api.url.com>/
SERVICE_WEB_ENDPOINT=https://<web.url.com>/
SERVICE_FALCON_ENDPOINT=https://<falcon.url.com>:3255
SERVICE_NIGHTINGALE_ENDPOINT=http://<nightingale.url.com>:3255
SERVICE_MOCKINGBIRD_ENDPOINT=wss://<mockingbird.url.com>:6625
SERVICE_CENTRIFUGO_ENDPOINT=<centrifugo.url.com>:8000
SERVICE_DATABASE_ENDPOINT=pgsql://<username>:<password>@<database.url.com>:5432/<database_name>
SERVICE_REDIS_ENDPOINT=redis://<redis.url.com>
SERVICE_MONGODB_ENDPOINT=mongodb://<username>:<password>@<mongodb.url.com>:27017/?authSource=admin
SERVICE_MONGODB_DATABASE=<mongodb_database_name>
SERVICE_TIMESCALE_ENDPOINT=pgsql://<username>:<password>@<timescale.url.com>:5432/<timescale_database_name>
```

# Debugging

## Debugging Clubster API
to debug a specific service, you can set the `XDEBUG_CONFIG` env variable and the debug flag for the specific service:

```dotenv
XDEBUG_CONFIG="idekey=PHPSTORM client_host=<Your Workstation IP> client_port=9003"
```
# Preparing the Development Environment
```shell
git config --global --add safe.directory '%(prefix)///*************/root/var/www/clubster'
```


# notes

## clubster-web initialization
```shell
cd <project-root>/services/clubster-web/application

composer install --ignore-platform-reqs --no-scripts
# install node and npm and n (node version manager)
curl -fsSL https://raw.githubusercontent.com/tj/n/master/bin/n | bash -s lts
npm install -g n

#install yarn
npm install -g yarn

# install dependencies
npm install

# run encore
yarn encore dev
```

