.idea/
.github/
.docker-compose.yml
.dockerignore
qodana.yaml
cache/container-cache.php
cache/container-cache.php.xml
cron/
deployment/
vendor/*
.gitignore
appspec.yml
buildspec.yml
buildspec.docker.yml
composer.phar
create_sns_events.php
documentdb_tunnel.sh
falcon.iml
react.php
temporary-www-key.pem
websocket.php
Dockerfile
composer.lock
.git
/vendor/
cmake-build-*/
*.iws
out/
atlassian-ide-plugin.xml
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties
.build/
.run/
deployment/
!deployment/php/clubster-php-fpm.conf
.dev.env
temporary-www-key.pem
