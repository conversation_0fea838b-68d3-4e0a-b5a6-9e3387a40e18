<?php

use Aws\Ses\SesClient;
use Clubster\Nightingale\Infrastructure\Notifications\Processor\EmailProcessor;
use Clubster\Nightingale\Infrastructure\Notifications\Processor\InternalProcessor;
use Clubster\Nightingale\Infrastructure\Notifications\Processor\PushProcessor;
use Clubster\Nightingale\Infrastructure\Notifications\QueueProcessor;
use Clubster\Nightingale\Infrastructure\Queue\MongoQueueConsumer;
use Clubster\Nightingale\Infrastructure\Queue\QueueConsumerInterface;
use Clubster\Nightingale\Infrastructure\Template\TwigRenderer;
use Monolog\Logger;
use PgAsync\Client;
use Psr\Log\LoggerInterface;
use Ramsey\Uuid\Uuid;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;
use function Symfony\Component\DependencyInjection\Loader\Configurator\service;

return function (ContainerConfigurator $container) {
    $container->parameters()
        ->set('nightingale.queue_name', $_ENV['TASK_ID']??Uuid::uuid4()->toString())
        ->set('nightingale.interval_sleep_time', 5)
        ->set('nightingale.active_notification_processors', [
            'emailProcessor'=>(bool)$_ENV['NOTIFICATION_PROCESSOR_EMAIL'],
            'pushProcessor'=>(bool)$_ENV['NOTIFICATION_PROCESSOR_PUSH'],
            'internalProcessor'=>(bool)$_ENV['NOTIFICATION_PROCESSOR_INTERNAL'],
        ])
        ->set('socketUrl',$_ENV['SOCKET_URL'])
        ->set('socketApiKey',$_ENV['SOCKET_API_KEY'])
        ->set('socketSecretKey',$_ENV['SOCKET_SECRET_KEY'])
        ->set('mongoDbUri',$_ENV['MONGO_DB_URI'])
        ->set('mongoDbName',$_ENV['MONGO_DB_NAME'])
        ->set('mongoCollectionName',$_ENV['MONGO_COLLECTION_NAME'])

    ;

    $services = $container->services()
                            ->defaults()
                            ->autowire()
                            ->autoconfigure()
                            ->public();
    $services->set(\Clubster\Nightingale\Nightingale::class)->share()->public();
    $services->set(LoggerInterface::class, Logger::class)->arg('$name', 'nightingale');
    $services->set('emailProcessor', EmailProcessor::class);
    $services->set('pushProcessor', PushProcessor::class);
    $services->set('internalProcessor', InternalProcessor::class);
    $services->set(\Carbon\Carbon::class);
    $services->set(TwigRenderer::class)->share();
    $services->set(MongoQueueConsumer::class)->share();
    $services->set(QueueProcessor::class)
        ->args([service(LoggerInterface::class)])
        ->share();
    $services->set(SesClient::class)
        ->args([
            [
                'region'  => $_ENV['AWS_SES_REGION'], // change to your region
                'version' => 'latest',
            ]
        ]);
    $services->set(Client::class)
        ->args([[
            'host'=>$_ENV['DB_HOST'],
            'port'=>$_ENV['DB_PORT'],
            'user'=>$_ENV['DB_USERNAME'],
            'password'=>$_ENV['DB_PASSWORD'],
            'database'=>$_ENV['DB_NAME'],
            'max_connections'=>10
        ]]);
    $services->set(\React\EventLoop\LoopInterface::class)->factory([\React\EventLoop\Loop::class,'get']);
    $services->set(Drift\DBAL\Driver\PostgreSQL\PostgreSQLDriver::class)->share();
    $services->set(\Doctrine\DBAL\Platforms\PostgreSQLPlatform::class)->share();
    $services->set(\Drift\DBAL\Credentials::class)->args([
        $_ENV['DB_HOST'],
        $_ENV['DB_PORT'],
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        $_ENV['DB_NAME'],
        [],
        15
    ])->share();
    $services->set(\Drift\DBAL\ConnectionPool::class)->factory([\Drift\DBAL\ConnectionPool::class,'createConnected'])->args([
        service(\Drift\DBAL\Driver\PostgreSQL\PostgreSQLDriver::class),
        service(\Drift\DBAL\Credentials::class),
        service(\Doctrine\DBAL\Platforms\PostgreSQLPlatform::class)
    ]);

    $services->set('TimeSeriesCredentials',\Drift\DBAL\Credentials::class)->args([
        $_ENV['STATISTICS_DB_HOST'],
        $_ENV['STATISTICS_DB_PORT'],
        $_ENV['STATISTICS_DB_USERNAME'],
        $_ENV['STATISTICS_DB_PASSWORD'],
        $_ENV['STATISTICS_DB_NAME'],
        [],
        15
    ])->share();
    $services->set('TimeSeriesConnectionPool',\Drift\DBAL\ConnectionPool::class)->factory([\Drift\DBAL\ConnectionPool::class,'createConnected'])->args([
        service(\Drift\DBAL\Driver\PostgreSQL\PostgreSQLDriver::class),
        service('TimeSeriesCredentials'),
        service(\Doctrine\DBAL\Platforms\PostgreSQLPlatform::class)
    ]);

    \Prometheus\Storage\Redis::setDefaultOptions([
        'host' => $_ENV['REDIS_HOST'],
        'port' => $_ENV['REDIS_PORT'],
        'timeout' => 0.1,
        'read_timeout' => '10',
        'persistent_connections' => true
    ]);
    $services->set(\Prometheus\Storage\Redis::class)->share();

    $services->load('Clubster\\Nightingale\\', '../src/*')
            ->exclude([
                '../src/Infrastructure/Statistics/Prometheus/CounterMetric.php',
            ]);
    ;
};
