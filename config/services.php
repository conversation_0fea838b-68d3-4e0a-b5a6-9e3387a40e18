<?php
declare(strict_types=1);

use <PERSON><PERSON>\Adapter\Predis\PredisCachePool;
use Mockingbird\Infrastructure\Database\DatabaseAwareInterface;
use Mockingbird\Infrastructure\Listener\ListenerInterface;
use Mockingbird\Infrastructure\Logger\Logger;
use Mockingbird\Infrastructure\Request\Request;
use Mockingbird\Infrastructure\Task\TaskInterface;
use Mockingbird\Infrastructure\Task\TaskStack;
use Mockingbird\Listener\Http\RoutableTaskInterface;
use Mockingbird\Mockingbird;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\DriverManager;
use Predis\Client as RedisClient;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;
use VertigoLabs\LoggerAware\LoggerAwareInterface;
use function Symfony\Component\DependencyInjection\Loader\Configurator\service;

return function(ContainerConfigurator $configurator): void {
    // to set parameters
    //$configurator->parameters();

    $services = $configurator->services()
                             ->defaults()
                             ->autowire()
                             ->autoconfigure()
                             ->public();

    $services->set(Mockingbird::class)
        ->share();
    $services->set(TaskStack::class)
        ->share();
    $services->set(Request::class)
        ->share();

    /// data services
    // postgresql
    $services->set(Connection::class)->factory([DriverManager::class, 'getConnection'])->args([[
        'url'=>$_ENV['DATABASE_URL']
    ]]);

    $services->set(RedisClient::class)->args([
        [
            'scheme' => 'tcp',
            'host'   => $_ENV['REDIS_HOST'],
            'port'   => $_ENV['REDIS_PORT'],
        ], [
            'parameters'=>[
                'database'=>$_ENV['REDIS_DB']
            ]
        ]
    ]);

    $services->set(PredisCachePool::class)->args([service(RedisClient::class)])->share(true);
    $services->set(\MongoDB\Client::class)->args([$_ENV['MONGO_DB_URI']])->share(true);

    $services->instanceof(LoggerAwareInterface::class)
                ->call('setLogger',[service(Logger::class)]);

    $services->instanceof(ContainerAwareInterface::class)
                ->call('setContainer');

    $services->instanceof(DatabaseAwareInterface::class)
                ->call('setConnection',[service(Connection::class)]);

    $services->instanceof(ListenerInterface::class)->autowire(true);

    $services->instanceof(TaskInterface::class)
                ->tag('task');

    $services->instanceof(RoutableTaskInterface::class)
                ->tag('routable-task');

    $services
        ->load('Mockingbird\\', '../src/*')
        ->exclude([
            '../src/Mockingbird.php',
            '../src/Infrastructure/Logger/*.php',
            '../src/Listener/*/Request.php',
            '../src/Listener/Messenger/**/Connection.php',
            '../src/Listener/Messenger/**/Channel.php',
            '../src/**/Exception',
        ]);
};
