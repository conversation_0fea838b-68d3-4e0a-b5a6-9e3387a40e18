services:
  clubster-prometheus:
    image: clubster-prometheus:dev
    container_name: clubster-prometheus
    build:
        context: .
        dockerfile: ${DOCKERFILE:-Dockerfile}
    restart: unless-stopped
    volumes:
      - ./config:/etc/prometheus
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - clubster-net

networks:
  clubster-net:
    name: clubster-net
    driver: bridge

volumes:
  prometheus_data:
    driver: local
    driver_opts:
      type: none
      device: ../infrastructure/volumes
      o: bind
