services:
  clubster-centrifugo:
    image: clubster-centrifugo:dev
    container_name: clubster-centrifugo
    restart: on-failure
    build:
      context: .
      dockerfile: ${DOCKERFILE:-Dockerfile}
    volumes:
      - ./deployment/config.json:/etc/centrifugo/config.json
    ports:
      - '8000:8000'
      - '10001:10001'
    ulimits:
      nofile:
        soft: 65535
        hard: 65535
    healthcheck:
      test: ["CMD", "netstat", "-tuln", "|", "grep", "8000"]
    networks:
      - clubster-net

networks:
  clubster-net:
    name: clubster-net
    driver: bridge
