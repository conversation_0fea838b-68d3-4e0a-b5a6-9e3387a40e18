{"token_hmac_secret_key": "c390137f-eeb4-466e-9e06-4201e4fe41c8", "admin": true, "health": true, "swagger": false, "admin_password": "cc1af229-8b8e-4645-80c7-f3fae76fde65", "admin_secret": "a5e41371-9bf6-42f5-a021-0aa74d24d12f", "api_key": "f31891d5-1854-4326-9e6f-3d12f544e256", "allowed_origins": ["*"], "log_level": "debug", "allow_subscribe_for_client": true, "namespaces": [{"name": "admin", "allow_subscribe_for_client": true}, {"name": "statistics", "allow_subscribe_for_client": true}, {"name": "feed", "allow_subscribe_for_client": true}, {"name": "notifications", "allow_subscribe_for_client": true}, {"name": "test", "allow_subscribe_for_client": true}], "publish": true, "proxy_publish": true, "proxy_subscribe": true, "proxy_connect": true, "proxy_refresh": true, "proxy_connect_endpoint": "grpc://192.168.20.20:30000", "proxy_connect_timeout": "10s", "proxy_publish_endpoint": "grpc://192.168.20.20:30000", "proxy_publish_timeout": "10s", "proxy_subscribe_endpoint": "grpc://192.168.20.20:30000", "proxy_subscribe_timeout": "10s", "proxy_refresh_endpoint": "grpc://192.168.20.20:30000", "proxy_refresh_timeout": "10s", "proxy_sub_refresh_endpoint": "grpc://192.168.20.20:30000", "proxy_sub_refresh_timeout": "1s", "proxy_rpc_endpoint": "grpc://192.168.20.20:30000", "proxy_rpc_timeout": "10s"}