services:
  clubster-grafana:
    image: clubster-grafana:dev
    container_name: clubster-grafana
    build:
        context: .
        args:
          - GF_INSTALL_PLUGINS=golioth-websocket-datasource,grafana-clock-panel,grafana-polystat-panel,simpod-json-datasource,digrich-bubblechart-panel
        dockerfile: ${DOCKERFILE:-Dockerfile}
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    restart: always

    user: "0"
    env_file:
      - ${ENV:-.env}
    depends_on:
      - clubster-prometheus
    networks:
      - clubster-net

# all services use the clubster-net network
networks:
  clubster-net:
    name: clubster-net
    driver: bridge

# if your service needs volumes
volumes:
  grafana_data:
    driver: local
    driver_opts:
      type: none
      device: ../infrastructure/volumes
      o: bind
