{"cluster": "temporal", "service": "temporal-frontend-service", "desiredCount": 1, "taskDefinition": "arn:aws:ecs:us-east-2:559585394309:task-definition/temporal-frontend:4", "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-be8fe3c5", "subnet-571aae1a", "subnet-61cae808"], "securityGroups": ["sg-409c5028"], "assignPublicIp": "ENABLED"}}, "loadBalancers": [{"targetGroupArn": "arn:aws:elasticloadbalancing:us-east-2:559585394309:targetgroup/temporal-frontend-http/04e17b1908f13dc5", "containerName": "temporal-frontend", "containerPort": 7243}, {"targetGroupArn": "arn:aws:elasticloadbalancing:us-east-2:559585394309:targetgroup/temporal-frontend-grpc-tg/0ff01756795cc4fa", "containerName": "temporal-frontend", "containerPort": 7233}], "healthCheckGracePeriodSeconds": 0, "enableExecuteCommand": false}