{"services": [{"serviceArn": "arn:aws:ecs:us-east-2:559585394309:service/temporal/temporal-frontend-service", "serviceName": "temporal-frontend-service", "clusterArn": "arn:aws:ecs:us-east-2:559585394309:cluster/temporal", "loadBalancers": [{"targetGroupArn": "arn:aws:elasticloadbalancing:us-east-2:559585394309:targetgroup/temporal-frontend-http/04e17b1908f13dc5", "containerName": "temporal-frontend", "containerPort": 7243}, {"targetGroupArn": "arn:aws:elasticloadbalancing:us-east-2:559585394309:targetgroup/temporal-frontend-grpc-tg/0ff01756795cc4fa", "containerName": "temporal-frontend", "containerPort": 7233}], "serviceRegistries": [{"registryArn": "arn:aws:servicediscovery:us-east-2:559585394309:service/srv-ez6t2uxbc5ev37oy"}], "status": "ACTIVE", "desiredCount": 1, "runningCount": 1, "pendingCount": 0, "launchType": "FARGATE", "platformVersion": "LATEST", "platformFamily": "Linux", "taskDefinition": "arn:aws:ecs:us-east-2:559585394309:task-definition/temporal-frontend:4", "deploymentConfiguration": {"deploymentCircuitBreaker": {"enable": true, "rollback": true}, "maximumPercent": 200, "minimumHealthyPercent": 100, "alarms": {"alarmNames": [], "enable": false, "rollback": false}}, "deployments": [{"id": "ecs-svc/8768314530016731118", "status": "PRIMARY", "taskDefinition": "arn:aws:ecs:us-east-2:559585394309:task-definition/temporal-frontend:4", "desiredCount": 1, "pendingCount": 0, "runningCount": 1, "failedTasks": 0, "createdAt": "2025-05-05T23:31:06.965000-04:00", "updatedAt": "2025-05-05T23:32:00.641000-04:00", "launchType": "FARGATE", "platformVersion": "1.4.0", "platformFamily": "Linux", "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-be8fe3c5", "subnet-571aae1a", "subnet-61cae808"], "securityGroups": ["sg-409c5028"], "assignPublicIp": "ENABLED"}}, "rolloutState": "COMPLETED", "rolloutStateReason": "ECS deployment ecs-svc/8768314530016731118 completed."}], "roleArn": "arn:aws:iam::559585394309:role/aws-service-role/ecs.amazonaws.com/AWSServiceRoleForECS", "events": [{"id": "e7ecdf35-5306-441b-af9b-e9c0b5e0c64a", "createdAt": "2025-05-05T23:32:00.648000-04:00", "message": "(service temporal-frontend-service) has reached a steady state."}, {"id": "40e48970-6c80-4d5d-aa41-8ebb2d085a55", "createdAt": "2025-05-05T23:32:00.647000-04:00", "message": "(service temporal-frontend-service) (deployment ecs-svc/8768314530016731118) deployment completed."}, {"id": "1e2ec10b-c29c-4fed-be81-296cc83a4bb6", "createdAt": "2025-05-05T23:31:51.271000-04:00", "message": "(service temporal-frontend-service) registered 1 targets in (target-group arn:aws:elasticloadbalancing:us-east-2:559585394309:targetgroup/temporal-frontend-http/04e17b1908f13dc5)"}, {"id": "3e4b06e9-e057-4986-9586-39cd94b06a1c", "createdAt": "2025-05-05T23:31:21.283000-04:00", "message": "(service temporal-frontend-service) has started 1 tasks: (task b10d154bca7a4212967d1523be28c493)."}], "createdAt": "2025-05-05T23:31:06.965000-04:00", "placementConstraints": [], "placementStrategy": [], "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-be8fe3c5", "subnet-571aae1a", "subnet-61cae808"], "securityGroups": ["sg-409c5028"], "assignPublicIp": "ENABLED"}}, "healthCheckGracePeriodSeconds": 0, "schedulingStrategy": "REPLICA", "deploymentController": {"type": "ECS"}, "createdBy": "arn:aws:iam::559585394309:root", "enableECSManagedTags": true, "propagateTags": "NONE", "enableExecuteCommand": false}], "failures": []}