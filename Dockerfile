FROM public.ecr.aws/composer/composer:latest as composer
ARG COMPOSER_ARG=--no-dev
WORKDIR /app
COPY composer.* /app/
RUN set -xe && composer install --ignore-platform-reqs --no-dev --no-interaction --no-scripts
COPY . /app/
RUN composer dump-autoload $COMPOSER_ARG --optimize --classmap-authoritative

FROM public.ecr.aws/docker/library/php:8.2.8-alpine3.18 as base
ARG DEBUG_CLUBSTER_NIGHTINGALE
RUN apk add zstd-dev
ADD https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions /usr/local/bin/
RUN chmod +x /usr/local/bin/install-php-extensions
RUN install-php-extensions pgsql pdo_pgsql mongodb-1.14.0 pcntl redis
RUN if [ "$DEBUG_CLUBSTER_NIGHTINGALE" = "true" ] ; then \
        echo "################################## XDEBUG MODE: DEBUG #########################################" \
        && install-php-extensions xdebug \
        && echo "xdebug.mode = debug" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
        && echo "xdebug.log = /tmp/xdebug.log"  >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
        && echo "xdebug.discover_client_host = true"  >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
         && echo "xdebug.start_with_request = trigger"  >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
        && export PHP_IDE_CONFIG="serverName=clubster-nightingale"; \
    fi

ARG APP_ENV
ARG IMAGE_TAG
WORKDIR /app
COPY . /app/
COPY --from=composer /app/vendor /app/vendor
RUN rm -rf /var/www/.env

FROM base as release
CMD ["php","./bin/nightingale"]
