FROM php:8.0.30-fpm as builder
ARG DEBUG_CLUBSTER_WEB

RUN addgroup --gid 3000 --system nginx
RUN adduser --uid 3000 --system --disabled-login --disabled-password --gid 3000 nginx
RUN apt-get --allow-releaseinfo-change update && apt-get install -y curl iputils-ping zlib1g-dev libpng-dev librabbitmq-dev libssh-dev git libzip-dev libicu-dev libonig-dev unzip
ADD https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions /usr/local/bin/
RUN chmod +x /usr/local/bin/install-php-extensions

RUN install-php-extensions gd zip exif pcntl bcmath opcache intl redis mongodb excimer-1.2.3
RUN if [ "$DEBUG_CLUBSTER_WEB" = "true" ] ; then \
      echo "################################## XDEBUG MODE: DEBUG #########################################" \
      && pecl install xdebug-3.1.5 \
      && docker-php-ext-enable xdebug \
      && echo "xdebug.mode = debug" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
      && echo "xdebug.log = /tmp/xdebug.log"  >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
      && echo "xdebug.discover_client_host = true"  >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
      && echo "xdebug.start_with_request = trigger"  >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
      && export PHP_IDE_CONFIG="serverName=clubster-web"; \
    fi

RUN echo "upload_max_filesize = 550M"  >> /usr/local/etc/php/conf.d/00_custom.ini \
      && echo "post_max_size = 550M"  >> /usr/local/etc/php/conf.d/00_custom.ini \
      && echo "sys_temp_dir = /var/www/var/cache"  >> /usr/local/etc/php/conf.d/00_custom.ini \
      && echo "upload_tmp_dir = /var/www/var/cache"  >> /usr/local/etc/php/conf.d/00_custom.ini \
      && echo "max_execution_time = 360"  >> /usr/local/etc/php/conf.d/00_custom.ini;

RUN mkdir -p /sock
RUN chmod 0777 /sock
COPY deployment/php/clubster-php-fpm.conf /usr/local/etc/php-fpm.d/zzz-custom.conf

FROM public.ecr.aws/k5n3n7k0/clubster/composer:php.8.0.3-composer.2.6.5 as composer
ARG COMPOSER_ARG=--no-dev
WORKDIR /app
COPY . /app/
RUN rm -rf /app/composer.lock
RUN set -xe && composer install --no-dev --no-interaction --no-scripts --ignore-platform-req=ext-mongodb --ignore-platform-req=ext-redis --ignore-platform-req=ext-pcntl
RUN rm -rf ./vendor/nesbot/carbon/tests
RUN rm -rf ./vendor/symfony/dependency-injection/Tests
RUN rm -rf ./vendor/symfony/validator/Tests
RUN rm -rf ./vendor/symfony/console/Tests
RUN rm -rf ./vendor/symfony/http-foundation/Tests
RUN rm -rf ./vendor/symfony/translation/Tests
RUN rm -rf ./vendor/symfony/serializer/Tests
RUN rm -rf ./vendor/symfony/config/Tests
RUN rm -rf ./vendor/symfony/yaml/Tests
RUN rm -rf ./tests
#COPY . /app/
RUN ls -la /app
RUN composer dump-autoload $COMPOSER_ARG --optimize --classmap-authoritative
RUN composer dump-env prod

FROM node:18.18.0-alpine as assets
ARG DEPLOY_ASSETS
ARG ASSETS_BUCKET
ARG AWS_REGION=us-east-2
ARG APP_ENV=prod
WORKDIR /var/www
COPY . /var/www
RUN ls -l /var/www
RUN rm -rf /var/www/public/assets
COPY --from=composer /app/vendor /var/www/vendor
RUN yarn install
# if APP_ENV is not prod run yarn encore dev
RUN if [ "$APP_ENV" = "prod" ] ; then \
      echo "################################## ASSETS MODE: PROD #########################################" \
      && yarn run encore production; \
    else \
      echo "################################## ASSETS MODE: DEV #########################################" \
      && yarn run encore dev; \
    fi

FROM builder as release
WORKDIR /var/www
COPY . /var/www
COPY --from=composer /app/vendor /var/www/vendor
RUN rm -rf /var/www/composer.lock
COPY --from=composer /app/composer.lock /var/www/composer.lock
COPY --from=composer /app/.env.local.php /var/www/.env.local.php
COPY --from=assets /var/www/public /var/www/public
COPY --from=assets /var/www/yarn.lock /var/www
COPY . /var/www
COPY .build_number /var/www
RUN echo $(date +%Y-%m-%dT%H:%M:%S) > /var/www/.build_date

RUN chown -R nginx:nginx /var/www
RUN chmod -R 777 /var/www/var
RUN chmod -R 775 /var/www/bin
USER nginx
RUN mkdir -p /var/www/var/sock

WORKDIR /var/www
VOLUME ["/var/www", "/sock"]
CMD ["php-fpm"]
#ENTRYPOINT ["/var/www/entrypoint.sh"]
