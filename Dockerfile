FROM public.ecr.aws/composer/composer:latest as composer
ARG COMPOSER_ARG=--no-dev
WORKDIR /app
COPY composer.* /app/
RUN set -xe && composer install --ignore-platform-reqs --no-dev --no-interaction --no-scripts
COPY . /app/
RUN composer dump-autoload $COMPOSER_ARG --optimize --classmap-authoritative

# build main app
FROM public.ecr.aws/docker/library/php:8.2.8-alpine3.18 as base
ARG DEBUG_CLUBSTER_MOCKINGBIRD
ADD https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions /usr/local/bin/
RUN chmod +x /usr/local/bin/install-php-extensions
RUN install-php-extensions pgsql pdo_pgsql mongodb-1.14.0 pcntl bcmath gd redis excimer-1.2.3
RUN if [ "$DEBUG_CLUBSTER_MOCKINGBIRD" = "true" ] ; then \
      echo "################################## XDEBUG MODE: DEBUG #########################################" \
      && install-php-extensions xdebug \
      && echo "xdebug.mode = debug" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
      && echo "xdebug.log = /tmp/xdebug.log"  >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
      && echo "xdebug.discover_client_host = true"  >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
      && export PHP_IDE_CONFIG="serverName=clubster-mockingbird"; \
    fi
WORKDIR /usr/src/mockingbird
ARG APP_ENV
COPY cache/ ./cache/
COPY config/ ./config/
COPY src/ ./src/
COPY server.php ./server.php
COPY wren.php ./wren.php
COPY entrypoint.sh ./entrypoint.sh
COPY --from=composer app/vendor ./vendor/

RUN chmod +x ./entrypoint.sh

FROM base as release
EXPOSE 6625/tcp
EXPOSE 80/tcp

CMD ["./entrypoint.sh"]
