#!/bin/bash

echo "Installing Form Builder Dependencies..."

# Install required npm packages
npm install react-dnd react-dnd-html5-backend

echo "Building assets..."

# Build the assets
npm run build

echo "Form Builder installation complete!"
echo ""
echo "Layout:"
echo "- Left column top: Form elements palette"
echo "- Left column bottom: Properties editor"  
echo "- Right column: Form builder grid"
echo ""
echo "Next steps:"
echo "1. Visit /dev/builder in your browser"
echo "2. Drag elements from palette to grid"
echo "3. Click elements to edit properties"
echo ""
echo "Features:"
echo "- Compact UI with FontAwesome icons"
echo "- Two-column layout using content-box classes"
echo "- Dotted grid background for drop zones"
echo "- Fixed state management for proper React re-rendering"
