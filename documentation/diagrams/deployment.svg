<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
    <g>
        <rect x="50" y="50" width="300" height="50" fill="#f0f0f0" stroke="#000" />
        <text x="200" y="80" font-family="Arial" font-size="14" text-anchor="middle">Developer pushes code to Repo</text>

        <rect x="50" y="150" width="300" height="50" fill="#f0f0f0" stroke="#000" />
        <text x="200" y="180" font-family="Arial" font-size="14" text-anchor="middle">Repo triggers a build process</text>

        <rect x="400" y="250" width="300" height="50" fill="#f0f0f0" stroke="#000" />
        <text x="550" y="280" font-family="Arial" font-size="14" text-anchor="middle">Build process builds docker image</text>

        <rect x="400" y="350" width="300" height="50" fill="#f0f0f0" stroke="#000" />
        <text x="550" y="380" font-family="Arial" font-size="14" text-anchor="middle">Build process pushes docker image to ECR</text>

        <rect x="400" y="450" width="300" height="50" fill="#f0f0f0" stroke="#000" />
        <text x="550" y="480" font-family="Arial" font-size="14" text-anchor="middle">Build process tags new image as "Bluejay"</text>

        <rect x="50" y="550" width="300" height="50" fill="#f0f0f0" stroke="#000" />
        <text x="200" y="580" font-family="Arial" font-size="14" text-anchor="middle">Code Pipeline detects new "Bluejay" image</text>

        <rect x="400" y="650" width="300" height="50" fill="#f0f0f0" stroke="#000" />
        <text x="550" y="680" font-family="Arial" font-size="14" text-anchor="middle">Code Pipeline triggers new forced deployment of image to ECS service</text>

        <line x1="200" y1="100" x2="200" y2="150" stroke="#000" marker-end="url(#arrow)" />
        <line x1="200" y1="200" x2="550" y2="250" stroke="#000" marker-end="url(#arrow)" />
        <line x1="550" y1="300" x2="550" y2="350" stroke="#000" marker-end="url(#arrow)" />
        <line x1="550" y1="400" x2="550" y2="450" stroke="#000" marker-end="url(#arrow)" />
        <line x1="550" y1="500" x2="200" y2="550" stroke="#000" marker-end="url(#arrow)" />
        <line x1="200" y1="600" x2="550" y2="650" stroke="#000" marker-end="url(#arrow)" />
    </g>

    <defs>
        <marker id="arrow" markerWidth="10" markerHeight="10" refX="0" refY="3" orient="auto" markerUnits="strokeWidth">
            <path d="M0,0 L0,6 L9,3 z" fill="#000" />
        </marker>
    </defs>
</svg>
