## BUILD
* push to github production
* github release/tag is created
  * github actions build docker image
  * docker image is tagged with git release/tag
  * docker image is tagged as "latest"
* githup action pushes docker image to ECR
* code pipeline is triggered on new ECR image creation
* code pipeline tags image as "bluejay"
* code pipeline deploys image to bluejay
* code pipeline pauses for manual approval for production
* upon manual approval code pipeline tags image as "production"
* code pipeline deploys image to production
