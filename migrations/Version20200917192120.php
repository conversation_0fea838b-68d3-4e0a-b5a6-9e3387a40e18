<?php declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200917192120 extends AbstractMigration
{
    public function up(Schema $schema) : void
    {
        $this->addSql('UPDATE "notifications"."templates" SET "created_by_id" = \'81dc57d2-2bfa-4672-bc27-330e5c5560fc\', "updated_by_id" = \'81dc57d2-2bfa-4672-bc27-330e5c5560fc\', "title" = \'RSVP Promoted - Push\', "description" = \'When an RSVP is Promoted\', "subject" = \'Your RSVP has been moved from Wait List to Attending\', "body_text" = \'Your RSVP has been moved from Wait List to Attending\', "body_html" = \'Your RSVP has been moved from Wait List to Attending\', "transactional" = \'f\', "created_at" = \'2020-07-07 14:40:08\', "updated_at" = \'2020-07-07 14:40:08\', "deleted_at" = NULL, "slug" = \'rsvp:promoted\', "delivery_method" = \'push\' WHERE "id" = \'57792ea4-e2b2-4164-b743-633dfb2d0c08\';');
        $this->addSql('UPDATE "notifications"."templates" SET "created_by_id" = \'81dc57d2-2bfa-4672-bc27-330e5c5560fc\', "updated_by_id" = \'81dc57d2-2bfa-4672-bc27-330e5c5560fc\', "title" = \'Event Restored\', "description" = \'when an event is restored\', "subject" = \'{{event.organization.name}} Restored An Event\', "body_text" = \'{{event.subject}} has been restored

You\'\'re being notified because you had previously RSVP\'\'d to this event. If you would still like to attend this event, please create a new RSVP.

    Please contact {{event.organization.name}} with any questions you have regarding this event.\', "body_html" = \'{% block headline %}
{{event.organization.name}} restored an event
{% endblock %}

{% block body %}
<b>{{event.subject}} has been restored</b>
<br><br>
<p>
    You\'\'re being notified because you had previously RSVP\'\'d to this event. If you would still like to attend this event, please create a new RSVP.
</p>

<p>
    Please contact {{event.organization.name}} with any questions you have regarding this event.
</p>
{% endblock %}\', "transactional" = \'t\', "created_at" = \'2020-01-10 17:34:09\', "updated_at" = \'2020-01-10 17:34:09\', "deleted_at" = NULL, "slug" = \'event:restored\', "delivery_method" = \'email\' WHERE "id" = \'a753853a-2b4b-49f0-9cc3-67529c05ee5f\';
');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }
}
