<?php declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200420181147 extends AbstractMigration
{
    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'postgresql', 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE organizations.contacts ADD suspended BOOLEAN DEFAULT \'false\'');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'postgresql', 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE organization_setting_value DROP CONSTRAINT FK_49AF5CC557A98358');
        $this->addSql('ALTER TABLE user_setting_value DROP CONSTRAINT FK_BA51060557A98358');
        $this->addSql('CREATE TABLE entity_settings."values" (id UUID NOT NULL, setting_id UUID DEFAULT NULL, option_id UUID DEFAULT NULL, value VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_cd5366fa7c41d6f ON entity_settings."values" (option_id)');
        $this->addSql('CREATE INDEX idx_cd5366fee35bd72 ON entity_settings."values" (setting_id)');
        $this->addSql('ALTER TABLE entity_settings."values" ADD CONSTRAINT fk_cd5366fa7c41d6f FOREIGN KEY (option_id) REFERENCES entity_settings.options (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE entity_settings."values" ADD CONSTRAINT fk_cd5366fee35bd72 FOREIGN KEY (setting_id) REFERENCES entity_settings.settings (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('DROP TABLE entity_settings.values');
        $this->addSql('ALTER TABLE organizations.contacts DROP suspended');
        $this->addSql('ALTER TABLE organizations.club_links ALTER sidebar SET DEFAULT \'false\'');
        $this->addSql('ALTER TABLE events ALTER rsvp_require_name SET DEFAULT \'false\'');
        $this->addSql('ALTER TABLE events ALTER rsvp_require_email SET DEFAULT \'false\'');
        $this->addSql('ALTER TABLE events ALTER rsvp_allow_notes SET DEFAULT \'false\'');
        $this->addSql('ALTER TABLE events ALTER rsvp_allow_attendee_notes SET DEFAULT \'false\'');
        $this->addSql('ALTER TABLE events ALTER rsvp_time_slot_per_attendee SET DEFAULT \'false\'');
        $this->addSql('ALTER TABLE organization_setting_value DROP CONSTRAINT fk_49af5cc557a98358');
        $this->addSql('ALTER TABLE organization_setting_value ADD CONSTRAINT fk_49af5cc557a98358 FOREIGN KEY (setting_value_id) REFERENCES entity_settings."values" (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE user_setting_value DROP CONSTRAINT fk_ba51060557a98358');
        $this->addSql('ALTER TABLE user_setting_value ADD CONSTRAINT fk_ba51060557a98358 FOREIGN KEY (setting_value_id) REFERENCES entity_settings."values" (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
    }
}
