<?php declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210503213530 extends AbstractMigration
{
    public function up(Schema $schema) : void
    {
        $this->addSql('INSERT INTO "entity_attributes"."attributes"("id", "title", "description", "required", "value_type", "allow_multiple_values", "default_value", "visibility", "category", "slug", "applicable_entity_types", "_discr") VALUES (\'a9b87d53-d422-492b-b8fe-6170e07cc145\', \'Marital Status\', \'Your Marital Status\', \'f\', \'text\', \'f\', NULL, \'Public\', \'General Information\', \'marital-status\', \'["App\\\\Domain\\\\User\\\\Entity\\\\User"]\', \'attribute\');');
        $this->addSql('INSERT INTO "entity_attributes"."attributes"("id", "title", "description", "required", "value_type", "allow_multiple_values", "default_value", "visibility", "category", "slug", "applicable_entity_types", "_discr") VALUES (\'d9d00b3c-722c-436f-8f5e-482c2ee44f78\', \'High School\', \'The name of your graduating high school\', \'f\', \'text\', \'f\', NULL, \'Public\', \'Education\', \'high-school\', \'["App\\\\Domain\\\\User\\\\Entity\\\\User"]\', \'attribute\');');
        $this->addSql('INSERT INTO "entity_attributes"."attributes"("id", "title", "description", "required", "value_type", "allow_multiple_values", "default_value", "visibility", "category", "slug", "applicable_entity_types", "_discr") VALUES (\'e811c3ff-2284-408b-9ef5-ec76bea44668\', \'College\', \'The name of your college\', \'f\', \'text\', \'f\', NULL, \'Public\', \'Education\', \'college\', \'["App\\\\Domain\\\\User\\\\Entity\\\\User"]\', \'attribute\');');
        $this->addSql('INSERT INTO "entity_attributes"."attributes"("id", "title", "description", "required", "value_type", "allow_multiple_values", "default_value", "visibility", "category", "slug", "applicable_entity_types", "_discr") VALUES (\'0e0e194d-a849-469d-9430-8085ac83b7b7\', \'Current Country\', \'The country you live in\', \'f\', \'text\', \'f\', NULL, \'Public\', \'Contact Information\', \'country\', \'["App\\\\Domain\\\\User\\\\Entity\\\\User"]\', \'attribute\');');
        $this->addSql('INSERT INTO "entity_attributes"."attributes"("id", "title", "description", "required", "value_type", "allow_multiple_values", "default_value", "visibility", "category", "slug", "applicable_entity_types", "_discr") VALUES (\'c1f6bf31-cdca-445f-9ca9-a2814727cbdc\', \'LinkedIn Profile Link\', \'Your LinkedIn Profile Link\', \'f\', \'text\', \'f\', NULL, \'Public\', \'Social Media\', \'linkedin\', \'["App\\\\Domain\\\\User\\\\Entity\\\\User"]\', \'attribute\');');
        $this->addSql('INSERT INTO "entity_attributes"."attributes"("id", "title", "description", "required", "value_type", "allow_multiple_values", "default_value", "visibility", "category", "slug", "applicable_entity_types", "_discr") VALUES (\'357fc148-53ef-4010-ab9a-193d184062ef\', \'Facebook Profile Link\', \'Your Facebook profile link\', \'f\', \'text\', \'f\', NULL, \'Public\', \'Social Media\', \'facebook\', \'["App\\\\Domain\\\\User\\\\Entity\\\\User"]\', \'attribute\');');
        $this->addSql('INSERT INTO "entity_attributes"."attributes"("id", "title", "description", "required", "value_type", "allow_multiple_values", "default_value", "visibility", "category", "slug", "applicable_entity_types", "_discr") VALUES (\'cb66a998-439b-4e1a-b47e-08c7a0cee887\', \'City\', \'The city you live in\', \'f\', \'text\', \'f\', NULL, \'Public\', \'Contact Information\', \'city\', \'["App\\\\Domain\\\\User\\\\Entity\\\\User"]\', \'attribute\');');
        $this->addSql('INSERT INTO "entity_attributes"."attributes"("id", "title", "description", "required", "value_type", "allow_multiple_values", "default_value", "visibility", "category", "slug", "applicable_entity_types", "_discr") VALUES (\'7f598aaf-fb92-47ec-995c-f07b16c14b4b\', \'State/Province\', \'The state or province you live in\', \'f\', \'text\', \'f\', NULL, \'Public\', \'Contact Information\', \'state-province\', \'["App\\\\Domain\\\\User\\\\Entity\\\\User"]\', \'attribute\');');
        $this->addSql('INSERT INTO "entity_attributes"."attributes"("id", "title", "description", "required", "value_type", "allow_multiple_values", "default_value", "visibility", "category", "slug", "applicable_entity_types", "_discr") VALUES (\'8b45a043-f2d8-46a2-a2bf-d87faa20f00c\', \'Cell Phone Number\', \'Your Cell Phone Number\', \'f\', \'text\', \'f\', NULL, \'Private\', \'Contact Information\', \'cell-ohone-number\', \'["App\\\\Domain\\\\User\\\\Entity\\\\User"]\', \'attribute\');');
        $this->addSql('INSERT INTO "entity_attributes"."attributes"("id", "title", "description", "required", "value_type", "allow_multiple_values", "default_value", "visibility", "category", "slug", "applicable_entity_types", "_discr") VALUES (\'72f7b7e6-aef9-42a0-82df-ad74db016381\', \'About Me\', \'A description about yourself\', \'f\', \'textarea\', \'f\', NULL, \'Public\', \'General Information\', \'about-me\', \'["App\\\\Domain\\\\User\\\\Entity\\\\User"]\', \'attribute\');');

        $this->addSql('INSERT INTO "entity_attributes"."options"("id", "attribute_id", "label", "value", "order") VALUES (\'71cfd5ca-7688-40cf-a2dd-b5acbbb903db\', \'a9b87d53-d422-492b-b8fe-6170e07cc145\', \'Single\', \'Single\', 1);');
        $this->addSql('INSERT INTO "entity_attributes"."options"("id", "attribute_id", "label", "value", "order") VALUES (\'97903864-3746-45b8-8578-cce56aa010d9\', \'a9b87d53-d422-492b-b8fe-6170e07cc145\', \'Married\', \'Married\', 3);');
        $this->addSql('INSERT INTO "entity_attributes"."options"("id", "attribute_id", "label", "value", "order") VALUES (\'2bfa6a78-bf86-4da8-a749-19f9b3fb9736\', \'a9b87d53-d422-492b-b8fe-6170e07cc145\', \'In Relationship\', \'In Relationship\', 2);');

        $this->addSql('UPDATE "entity_settings"."settings" SET "title" = \'Enable Mobile Push Notifications From Clubster\', "description" = \'Mobile Push notifications will allow Clubster to send notifications directly to your mobile device.\', "required" = \'f\', "value_type" = \'boolean\', "allow_multiple_values" = \'f\', "default_value" = \'1\', "visibility" = \'Private\', "category" = \'Clubster Notifications\', "slug" = \'enable-mobile-push-notifications\', "applicable_entity_types" = \'["App\\Domain\\User\\Entity\\User"]\' WHERE "id" = \'ff7de0f9-1ba4-447b-b961-362006eadd34\';');
        $this->addSql('UPDATE "entity_settings"."settings" SET "title" = \'Enable Email Notifications From Clubster\', "description" = \'This will allow Clubster to sent notifications to you by email\', "required" = \'f\', "value_type" = \'boolean\', "allow_multiple_values" = \'f\', "default_value" = \'1\', "visibility" = \'Private\', "category" = \'Clubster Notifications\', "slug" = \'enable-email-notifications\', "applicable_entity_types" = \'["App\\Domain\\User\\Entity\\User"]\' WHERE "id" = \'8d5239d4-258b-43e2-a884-f47d89130b34\';');
        $this->addSql('INSERT INTO "entity_settings"."settings"("id", "title", "description", "required", "value_type", "allow_multiple_values", "default_value", "visibility", "category", "slug", "applicable_entity_types") VALUES (\'86f7e6c1-9922-47e4-a19d-58aeaa4ea0b5\', \'Publicly Displayed Name\', \'How your name will be displayed for people who are not on your friends list\', \'f\', \'text\', \'f\', \'FF-LF\', \'Private\', \'Privacy Settings\', \'publicly-displayed-name\', \'["App\\Domain\\User\\Entity\\User"]\');');
        $this->addSql('INSERT INTO "entity_settings"."settings"("id", "title", "description", "required", "value_type", "allow_multiple_values", "default_value", "visibility", "category", "slug", "applicable_entity_types") VALUES (\'203c544e-336d-4f7c-b990-debb6e1ace05\', \'Displayed Name For Friends\', \'How your name is displayed for people on your friends list\', \'f\', \'text\', \'f\', \'FF-LF\', \'Private\', \'Privacy Settings\', \'friends-displayed-name\', \'["App\\Domain\\User\\Entity\\User"]\');');

    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }
}
