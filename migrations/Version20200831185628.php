<?php declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20200831185628 extends AbstractMigration
{
    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'postgresql', 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE events ADD sequence_primary UUID DEFAULT NULL');
        $this->addSql('ALTER TABLE events ADD occurrence_index INT DEFAULT NULL');
        $this->addSql('ALTER TABLE events ADD CONSTRAINT FK_5387574A234F235E FOREIGN KEY (sequence_primary) REFERENCES events (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_5387574A234F235E ON events (sequence_primary)');
        $this->addSql('CREATE INDEX "eventDates" ON "events" ("start_date","end_date","rrule_end");');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'postgresql', 'Migration can only be executed safely on \'postgresql\'.');

        $this->addSql('ALTER TABLE events DROP CONSTRAINT FK_5387574A234F235E');
        $this->addSql('DROP INDEX IDX_5387574A234F235E');
        $this->addSql('ALTER TABLE events DROP sequence_primary');
        $this->addSql('ALTER TABLE events DROP occurrence_index');
    }
}
