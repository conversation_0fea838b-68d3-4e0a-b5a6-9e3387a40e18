services:
  clubster-peregrine:
    image: clubster-peregrine:dev
    build:
      context: .
      dockerfile: Dockerfile
      target: release
      args:
          - DEBUG_CLUBSTER_PEREGRINE=${DEBUG_CLUBSTER_PEREGRINE:-false}
          - APP_ENV=dev
          - IMAGE_TAG=dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:2114/health"]
    env_file:
      - ${ENV:-.env}
    ports:
      - "8088:8088"
      - "2114:2114"
    networks:
      - clubster-net

networks:
  clubster-net:
    name: clubster-net
    driver: bridge
