<?php

namespace Clubster\Falcon\Infrastructure\Model;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Statement;

trait DatabaseModelAware
{
    private Connection $connection;

    private array $normalizedFields;

    public function setConnection(Connection $connection)
    {
        $this->connection = $connection;
    }

    protected function getConnection(): ?Connection
    {
        return $this->connection;
    }

    public function generateData(): array
    {
        $keys = array_keys($this->getFields());
        $values = array_keys($this->getNormalizedFields() );
        return $this->getData(array_combine($keys, $values));
    }

    public function generateSQL(): string
    {
        $outputData = $this->generateData();

        $query = 'INSERT INTO '.$this->table;
        $query .= '('.implode(',',array_keys($outputData)).') VALUES (:'.implode(',:',array_keys($outputData)).')';

        return $query;
    }

    public function prepare(): Statement
    {
        $sql = $this->generateSQL();
        $stmt = $this->getConnection()->prepare($sql);
        $outputData = $this->generateData();
        foreach ($outputData as $field=>$data) {
            $stmt->bindValue($field, $data, $this->getFields()[$field]);
        }

        return $stmt;
    }

    public function getFields(): array
    {
        return $this->fields;
    }

    protected function getNormalizedFields()
    {
        if (empty($this->normalizedFieldNames)) {
            $this->normalizedFields = $this->normalizeDataKeys($this->getFields());
        }

        return $this->normalizedFields;
    }

    public function jsonSerialize(): mixed
    {
        return $this->generateData();
    }

    abstract protected function normalizeDataKeys($data);

}
