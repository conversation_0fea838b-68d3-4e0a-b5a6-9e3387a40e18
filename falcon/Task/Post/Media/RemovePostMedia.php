<?php

namespace Clubster\Falcon\Task\Post\Media;

use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class RemovePostMedia extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $update = $this->connection()->createQueryBuilder();
            return $update->update('posts.media')
                            ->set('updated_by_id',$update->createNamedParameter($this->getData('userId')))
                            ->set('updated_at','NOW()')
                            ->set('deleted_at','NOW()')
                            ->where('id = '.$update->createNamedParameter($this->getData('id')))
                            ->execute();
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'id'=> new Constraints\Required(),
                'userId'=> new Constraints\Required()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
