<?php

namespace Clubster\Falcon\Task\Post\Builder;

use Clubster\Falcon\Infrastructure\Task\CallableTask;
use Exception;
use HtmlSanitizer\Sanitizer;
use Ramsey\Uuid\Uuid;
use Symfony\Component\Validator\Constraints;
use function React\Promise\map;

class ProcessBodyText extends CallableTask
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $inputTitle = $this->getData('title', '');
            $cleanTitle = Sanitizer::create(['max_input_length' => 2000])->sanitize($inputTitle);
            $cleanTitle = html_entity_decode($cleanTitle,ENT_QUOTES);

            $inputBody = $this->getData('body', '');
            $cleanBody = Sanitizer::create(['max_input_length' => 200000])->sanitize($inputBody);
            $cleanBody = html_entity_decode($cleanBody, ENT_QUOTES);

            $cleanBody = trim($cleanBody);

            $dom = new \DOMDocument();
            $dom->loadHTML($cleanBody);

            // find empty <p>, <div>, or <br> tags and remove them
            $xpath = new \DOMXPath($dom);
            $suspectTags = $xpath->query('//p|//div|//br');
            /** @var \DOMElement $tag */
            $tagQualification = [];
            foreach ($suspectTags as $k=>$tag) {
                $htmlentity = htmlentities($tag->nodeValue);
                $tagQualification[$k] = ($htmlentity === '&nbsp;' || $tag->tagName==='br');
//                $tag->parentNode->removeChild($tag);
            }

            // pull out the empty tags from the beginning of the array
            $index = 0;
            while($tagQualification[$index] == true) {
                $suspectTags->item($index)->parentNode->removeChild($suspectTags->item($index));
                $index++;
            }

            $index = count($suspectTags) - 1;
            while($tagQualification[$index] == true) {
                $suspectTags->item($index)->parentNode->removeChild($suspectTags->item($index));
                $index--;
            }

            $cleanBody = $dom->saveHTML();
            $cleanBody = trim(preg_replace('/^<!DOCTYPE.+?>/', '', $cleanBody));
            $cleanBody = preg_replace('/^<html><body>/', '', $cleanBody);
            $cleanBody = preg_replace('/<\/body><\/html>$/', '', $cleanBody);

            $urlRegex = '(?<urls>((https?|ftp|file):\/\/|www\.)[-A-Z0-9+&@#\/%?=~_|!:,.;]*[A-Z0-9+&@#\/%=~_|])';
            $emailRegex = '(?<emails>(?:mailto:)?([A-Z0-9._%+-]+@(?:[A-Z0-9-]+\.)+[A-Z]{2,}))';
            $regex = '/((?<=\s|\n|\r|^)\b'.$urlRegex.')|((?<=\s|\n|\r|^)\b'.$emailRegex.'\b)/i';

            preg_match_all($regex, $cleanBody, $result, PREG_OFFSET_CAPTURE);
            $links = [];
            if (!empty($result)) {
                $urls = array_map(function($url) {
                    $length = strlen($url[0]);
                    if (strpos($url[0], 'http') !== 0) {
                        $url[0] = 'https://'.$url[0];
                    }
                    return [
                        'url' => $url[0],
                        'position' => $url[1],
                        'length' => $length,
                        'type'=>'url'
                    ];
                }, array_filter($result['urls'], fn($url) => !empty($url[0])));

                $emails = array_map(function($email) {
                    return [
                        'url' => $email[0],
                        'position' => $email[1],
                        'length' => strlen($email[0]),
                        'type'=>'email'
                    ];
                }, array_filter($result['emails'], fn($email) => !empty($email[0])));

//                if (count($urls) > 0) {
//                    map($urls, function ($url) {
//                        $url['preview'] = $this->container->get(ProcessLinkPreviews::class)($url);
//                        unset($url[0],$url[1]);
//                        return $url;
//                    })->done(function ($data) use (&$urls) {
//                        $urls = $data;
//                    });
//                }

                $links = array_merge($urls, $emails);
            }


            $deleteLinks = $this->connection()->createQueryBuilder();
            $deleteLinks->delete('posts.links')
                ->where('post_id = '.$deleteLinks->createNamedParameter($this->getData('id')))
                ->execute();

            foreach ($links as $link) {
                $linkInsert = $this->connection()->createQueryBuilder();
                $linkInsert->insert('posts.links');
                $linkInsert->setValue('id', $linkInsert->createNamedParameter((string)Uuid::uuid4()));
                $linkInsert->setValue('post_id', $linkInsert->createNamedParameter($this->getData('id')));
                $linkInsert->setValue('created_by_id', $linkInsert->createNamedParameter($this->getData('userId')));
                $linkInsert->setValue('updated_by_id',$linkInsert->createNamedParameter($this->getData('userId')));
                $linkInsert->setValue('url',$linkInsert->createNamedParameter($link['url']));
                $linkInsert->setValue('offset_start',$linkInsert->createNamedParameter($link['position']));
                $linkInsert->setValue('offset_end',$linkInsert->createNamedParameter(bcadd($link['position'], $link['length'])));
                $linkInsert->setValue('length',$linkInsert->createNamedParameter($link['length']));
                $linkInsert->setValue('created_at', 'NOW()');
                $linkInsert->setValue('updated_at', 'NOW()');
                $linkInsert->setValue('link_type',ucwords($linkInsert->createNamedParameter($link['type'])));
                $linkInsert->execute();
            }


            $this->mergeData([
                'title'=>$cleanTitle,
                'body'=>$cleanBody,
                'links'=>$links
            ]);

            return $this->getData();
        } catch (\Throwable $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'id'=>new Constraints\Required(),
                'title'=>new Constraints\Required(),
                'body'=>new Constraints\Optional()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
