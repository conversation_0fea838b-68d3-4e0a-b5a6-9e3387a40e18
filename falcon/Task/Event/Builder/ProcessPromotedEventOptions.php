<?php

namespace Clubster\Falcon\Task\Event\Builder;

use Exception;
use Clubster\Falcon\Infrastructure\Task\CallableTask;
use HtmlSanitizer\Sanitizer;
use Symfony\Component\Validator\Constraints;

class ProcessPromotedEventOptions extends CallableTask
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {

            $eventId = $this->getData('eventId', '');

            $this->mergeData([
                'eventId'=>$eventId
            ]);

            return $this->getData();
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [

            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
