<?php

namespace Clubster\Falcon\Task\Event\Builder;

use Carbon\Carbon;
use Clubster\Falcon\Task\Utility\NormalizeDateTime;
use Exception;
use Clubster\Falcon\Infrastructure\Task\CallableTask;
use Symfony\Component\Validator\Constraints;

class ProcessEventSchedule extends CallableTask
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $eventSchedule = [
                'allDay' => $this->getData('eventSchedule.allDay', false, false),
                'start' => null,
                'end' => null,
            ];

            /** @var Carbon $start */
            $start = $this->container->get(NormalizeDateTime::class)->setData([
                'date'=> $this->getData('eventSchedule.startDate'),
                'time'=> $this->getData('eventSchedule.startTime', '00:00:00', false),
                'timezone'=> $this->getData('timezone.selected', date_default_timezone_get(), false),
            ])();

            if ($this->getData('eventSchedule.allDay', false, false) == true) {
                $start->setTime(0,0,0);
            }

            $startUtc = Carbon::instance($start);
            $startUtc->setTimezone('UTC');
            $eventSchedule['start_tz'] = $start->toDateTimeString();
            $eventSchedule['start'] = $startUtc->toDateTimeString();

            $endDate = $this->getData('eventSchedule.endDate', false, false);
            $endTime = $this->getData('eventSchedule.endTime', false, false);

            if ($endDate || $endTime) {
                /** @var Carbon $end */
                if (!$endDate) {
                    $endDate = $this->getData('eventSchedule.startDate');
                }
                $end = $this->container->get(NormalizeDateTime::class)->setData([
                    'date' => $endDate,
                    'time' => $endTime,
                    'timezone' => $this->getData('timezone.selected', date_default_timezone_get(), false),
                ])();

                if ($this->getData('eventSchedule.allDay', false, false) == true) {
                    $end->setTime(0,0,0);
                }

                if (empty($endTime)) {
                    $end = $end->endOfDay();
                }

                $endUtc = Carbon::instance($end);
                $endUtc->setTimezone('UTC');
                $eventSchedule['end_tz'] = $end->toDateTimeString();
                $eventSchedule['end'] = $endUtc->toDateTimeString();
            }

            $this->mergeData(['eventSchedule'=>$eventSchedule]);

            return $this->getData();
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
