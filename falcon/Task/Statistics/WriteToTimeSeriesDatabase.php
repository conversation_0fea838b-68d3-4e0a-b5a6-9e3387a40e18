<?php
namespace Clubster\Falcon\Task\Statistics;

use Doctrine\DBAL\Connection;
use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class WriteToTimeSeriesDatabase extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            /** @var Connection $connection */
            $connection = $this->container->get('TimeSeriesConnection');
            $query = $connection->createQueryBuilder();
            $query->insert($this->getData('table'));
            $fields = $this->getRawData('fields');
            foreach ($fields as $field=>$value) {
                $query->setValue($field, $query->createNamedParameter($value));
            }
            return $query->executeQuery();

        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'table'=> new Constraints\Required([
                    new Constraints\NotBlank()
                ]),
                'fields'=> new Constraints\Required()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
