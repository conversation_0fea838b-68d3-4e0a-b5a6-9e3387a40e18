<?php

namespace Clubster\Falcon\Task\User;

use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;
use Clubster\Falcon\Task\Falcon\Cache\InvalidateTag;

class UpdateUser extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $user = $this->container->get(LoadUser::class)->setData([
                'userId' => $this->getData('userId'),
                'disableCache'=>true
            ])();

            $sql = $this->connection()->createQueryBuilder();
            $sql->update('users.users')
                ->set('updated_at', 'NOW()');

            if (!empty($this->getData('firstName', false)) && trim($user['first_name']) !== trim($this->getData('firstName', false))) {
                $sql->set('first_name', $sql->createPositionalParameter(trim($this->getData('firstName'))));
            }
            if (!empty($this->getData('lastName', false)) && trim($user['last_name']) !== trim($this->getData('lastName', false))) {
                $sql->set('last_name', $sql->createPositionalParameter(trim($this->getData('lastName'))));
            }

            if (!empty($this->getData('email',false)) && strtolower(trim($user['email'])) !== strtolower(trim($this->getData('email', false)))) {
                $email = strtolower(trim($this->getData('email')));
                $existingEmailSql = $this->connection()->createQueryBuilder();
                $existingEmail = $existingEmailSql->select('email')
                                    ->from('users.users')
                                    ->where('users.email = '.$existingEmailSql->createPositionalParameter($email))
                                    ->andWhere('users.id != '.$existingEmailSql->createPositionalParameter($this->getData('userId')))
                                    ->setMaxResults(1)
                                    ->executeQuery()
                                    ->fetchAssociative();

                if (!empty($existingEmail)) {
                    throw new Exception('Account With Email Already Exists', 400);
                }

                $sql->set('email', $sql->createPositionalParameter($email));
            }

            if (!empty($sql->getQueryPart('set'))) {
                $sql->where('users.id = '.$sql->createPositionalParameter($this->getData('userId')));

                $sql->executeQuery();

                $this->container->get(InvalidateTag::class)->setData(['tag'=>'users-'.$this->getData('userId')])();
            }

            return [
                'status'=>true
            ];

        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'userId'=> new Constraints\Required(),
                'firstName'=> new Constraints\Optional(),
                'lastName'=> new Constraints\Optional(),
                'email'=> new Constraints\Optional(),
                'password'=> new Constraints\Optional(),
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
