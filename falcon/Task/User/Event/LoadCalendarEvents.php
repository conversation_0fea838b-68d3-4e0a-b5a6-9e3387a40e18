<?php

namespace Clubster\Falcon\Task\User\Event;

use Carbon\Carbon;
use Clubster\Falcon\Task\Event\FormatEventForCalendar;
use Clubster\Falcon\Task\User\Organizations\LoadConnectedOrganizationIds;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;
use function Doctrine\DBAL\Query\QueryBuilder;
use function React\Promise\all;
use function React\Promise\map;

class LoadCalendarEvents extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $connectedOrganizationIds = $this->container->get(LoadConnectedOrganizationIds::class)->setData([
                'user'=> $this->getData('user')
            ])();

            $query = $this->connection()->createQueryBuilder();
            $status = 'published';
            $start = (new Carbon($this->getData('startDate',null)))->toDateTimeString();
            $end = $this->getData('endDate', false)?(new Carbon($this->getData('endDate')))->toDateTimeString():(new Carbon())->addMonth()->toDateTimeString();

            $subquery = $this->connection()->createQueryBuilder();
            $subquery->select('rsvp.event_id')
                        ->from('event_rsvp', 'rsvp')
                        ->where('rsvp.user_id = '.$query->createPositionalParameter($this->getData('user'), ParameterType::STRING))
                        ->andWhere('rsvp.status IN (\'pending\', \'approved\')')
                        ->andWhere('rsvp.deleted_at IS NULL OR rsvp.deleted_at > CURRENT_TIMESTAMP')
                        ->groupBy('rsvp.event_id');

            $fields = [
                'e.id',
                'p.subject',
                'p.body',
                'e.start_date',
                'e.end_date',
                'e.all_day',
                'p.timezone',
                'o.id as organization_id',
                'o.slug as organization_slug',
                'o.name as organization_name',
                'oaif.uri as organization_avatar_image_uri',
                'po.id as organization_parentOrganization_id',
                'po.slug as organization_parentOrganization_slug',
                'po.name as organization_parentOrganization_name',
                'poaif.uri as organization_parentOrganization_avatar_image_uri',
                '(rsvp.event_id IS NOT NULL) as attending_status'
            ];
            $query->select(...$fields)
                ->from('events','e')
                ->innerJoin('e','posts','p','p.id = e.id ANd (p.deleted_at IS NULL OR p.deleted_at > CURRENT_TIMESTAMP)')
                ->innerJoin('e','organizations.organizations','o','o.id = e.organization_id')
                ->leftJoin('o','organizations.organization_images','oi','oi.id = o.avatar_id')
                ->leftJoin('oi','files','oaif','oaif.id = oi.image_id')
                ->leftJoin('e','organizations.organizations','po','po.id = e.organization_id')
                ->leftJoin('po','organizations.organization_images','poi','poi.id = po.avatar_id')
                ->leftJoin('poi','files','poaif','poaif.id = poi.image_id')
                ->leftJoin('e',sprintf('(%s)',$subquery->getSQL()), 'rsvp', 'rsvp.event_id = e.id')
                ->where('p.status = '.$query->createPositionalParameter($status,ParameterType::STRING))
                ->andWhere('e.organization_id in ('.$query->createPositionalParameter($connectedOrganizationIds,Connection::PARAM_STR_ARRAY).')')
                ->andWhere($query->expr()->or(
                    'e.start_date BETWEEN '.$query->createPositionalParameter($start, ParameterType::STRING).' AND '.$query->createPositionalParameter($end, ParameterType::STRING),
                    'e.end_date BETWEEN '.$query->createPositionalParameter($start, ParameterType::STRING).' AND '.$query->createPositionalParameter($end, ParameterType::STRING),
                ));

            $events = $this->connection()->executeQuery($query->getSQL(), $query->getParameters(), $query->getParameterTypes())->fetchAllAssociative();

            $formatEvent = $this->container->get(FormatEventForCalendar::class);
            all(map($events, function ($event) use ($formatEvent) {
                return $formatEvent->setData(['event'=>$event])();
            }))->then(function($formattedEvents) use (&$events) {
                $events = $formattedEvents;
            });

            return $events;

        } catch (Exception $exception) {
            throw $exception;
        }
    }


    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'user'=>new Constraints\Required(),
                'organization'=> new Constraints\Optional(),
                'startDate'=> new Constraints\Optional(),
                'endDate' => new Constraints\Optional()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
