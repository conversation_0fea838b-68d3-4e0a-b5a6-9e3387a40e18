<?php

namespace Clubster\Falcon\Task\User\Friend\Subscription;

use Doctrine\DBAL\ParameterType;
use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Ramsey\Uuid\Nonstandard\Uuid;
use Symfony\Component\Validator\Constraints;

class CreateSubscription extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $subscriptionSql = '
                INSERT INTO users.subscriptions
                (
                     id,
                     user_id,
                     friend_id,
                     created_by_id,
                     updated_by_id,
                     allow_push,
                     allow_feed,
                     created_at,
                     updated_at,
                     deleted_at
                )
                VALUES 
                (
                     ?,
                     ?,
                     ?,
                     ?,
                     ?,
                     ?,
                     ?,
                     NOW(),
                     NOW(),
                     null
                )
                ON CONFLICT ON CONSTRAINT user_friend_subscription_unique DO UPDATE SET
                    updated_at = NOW(),
                    updated_by_id = ?,
                    deleted_at = null
                ';

            $this->connection()->executeStatement($subscriptionSql,
                [
                    (string)Uuid::uuid4(),
                    $this->getData('userId'),
                    $this->getData('friendId'),
                    $this->getData('userId'),
                    $this->getData('userId'),
                    true,
                    true,
                    $this->getData('userId'),
                ],
                [
                    ParameterType::STRING,
                    ParameterType::STRING,
                    ParameterType::STRING,
                    ParameterType::STRING,
                    ParameterType::STRING,
                    ParameterType::BOOLEAN,
                    ParameterType::BOOLEAN,
                    ParameterType::STRING,
                ]
            );
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'userId' => new Constraints\Required(),
                'friendId' => new Constraints\Required(),
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
