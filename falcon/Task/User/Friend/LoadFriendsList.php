<?php

namespace Clubster\Falcon\Task\User\Friend;

use Clubster\Falcon\Task\User\LoadUser;
use Clubster\Falcon\ValueObjects\UserRelationship;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class LoadFriendsList extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $friendSql = $this->connection()->createQueryBuilder();
            $userId = $this->getData('userId');
            //            ->leftJoin('friends', 'messaging.participants', 'participants', '(participants.user_id = friends.user_id OR participants.user_id = friends.) AND participants.deleted_at IS NULL')
            $messagingQuery = $this->connection()->createQueryBuilder();
            $messagingQuery->select('channel_id', 'array_agg(user_id) as participants')
                ->from('messaging.participants')
                ->groupBy('channel_id')
                ->where('participants.deleted_at IS NULL')
                ->having('array_agg(user_id) && \'{"'.implode('","',[$userId]).'"}\'');

            $messageCountQuery = $this->connection()->createQueryBuilder();
            $messageCountQuery->select(['messages.channel_id','count(*) as message_count'])
                ->from('messaging.messages', 'messages')
                ->leftJoin('messages','messaging.message_views','message_views', 'message_views.message_id = messages.id AND message_views.participant_id = '.$messageCountQuery->createNamedParameter($userId, ParameterType::STRING))
                ->where('messages.channel_id in (select messaging.channel_id from ('.sprintf('%s', $messagingQuery->getSQL()).') messaging )')
                ->andWhere('message_views.message_id IS NULL')
                ->groupBy('messages.channel_id');

            $friendSql->select([
                'friends.user_id',
                'friends.friend_id',
                'friends.id',
                'friends.status',
                'friends.created_at',
                'friends.updated_at',
                'friends.deleted_at',

                'created_by.first_name as created_by_first_name',
                'created_by.last_name as created_by_last_name',
                'created_by."id" as created_by_id',
                'created_by.avatar_id as created_by_avatar_id',
                'created_by_files.uri as created_by_avatar_uri',

                'friend_friend.first_name as friend_first_name',
                'friend_friend.last_name as friend_last_name',
                'friend_friend."id" as friend_id',
                'friend_friend.avatar_id as friend_avatar_id',
                'friend_friend_files.uri as friend_avatar_uri',

                'friend_user.first_name as friend_user_first_name',
                'friend_user.last_name as friend_user_last_name',
                'friend_user."id" as friend_user_id',
                'friend_user.avatar_id as friend_user_avatar_id',
                'friend_user_files.uri as friend_user_avatar_uri',

                'subscription.allow_push',
                'subscription.allow_feed',

                'messaging.channel_id as messaging_channel_id',
                'messageCount.message_count as message_count'
            ])

                ->from('users.friends','friends')
                ->innerJoin('friends','users.users',"created_by",'friends.user_id = created_by.id')
                ->leftJoin('created_by','users.user_images', 'created_by_user_images', 'created_by.avatar_id = created_by_user_images."id" AND (created_by_user_images.deleted_at IS NULL OR created_by_user_images.deleted_at > (NOW() AT TIME ZONE \'utc\'))')
                ->leftJoin('created_by_user_images','"public".files', 'created_by_files', 'created_by_user_images.image_id = created_by_files."id" AND (created_by_files.deleted_at IS NULL OR created_by_files.deleted_at > (NOW() AT TIME ZONE \'utc\'))')

                ->innerJoin('friends','users.users',"friend_friend",'friends.friend_id = friend_friend.id')
                ->leftJoin('friend_friend','users.user_images', 'friend_friend_user_images', 'friend_friend.avatar_id = friend_friend_user_images."id" AND (friend_friend_user_images.deleted_at IS NULL OR friend_friend_user_images.deleted_at > (NOW() AT TIME ZONE \'utc\'))')
                ->leftJoin('friend_friend_user_images','"public".files', 'friend_friend_files', 'friend_friend_user_images.image_id = friend_friend_files."id" AND (friend_friend_files.deleted_at IS NULL OR friend_friend_files.deleted_at > (NOW() AT TIME ZONE \'utc\'))')

                ->innerJoin('friends','users.users',"friend_user",'friends.user_id = friend_user.id')
                ->leftJoin('friend_user','users.user_images', 'friend_user_user_images', 'friend_user.avatar_id = friend_user_user_images."id" AND (friend_user_user_images.deleted_at IS NULL OR friend_user_user_images.deleted_at > (NOW() AT TIME ZONE \'utc\'))')
                ->leftJoin('friend_user_user_images','"public".files', 'friend_user_files', 'friend_user_user_images.image_id = friend_user_files."id" AND (friend_user_files.deleted_at IS NULL OR friend_user_files.deleted_at > (NOW() AT TIME ZONE \'utc\'))')

                ->leftJoin('friends', 'users.subscriptions', 'subscription', 'subscription.user_id = '.$friendSql->createNamedParameter($userId).' AND ( subscription.friend_id = friends.id OR subscription.friend_id = friends.created_by_id ) AND subscription.deleted_at IS NULL')
                ->leftJoin(
                    fromAlias: 'friends',
                    join: \sprintf('(%s)', $messagingQuery->getSQL()),
                    alias: 'messaging',
                    condition: '(messaging.participants <@ array[friends.friend_id,friends.user_id]) AND (messaging.participants @> array[friends.friend_id,friends.user_id])')
                ->leftJoin(
                    fromAlias: 'friends',
                    join: \sprintf('(%s)', $messageCountQuery->getSQL()),
                    alias: 'messageCount',
                    condition: '(messageCount.channel_id = messaging.channel_id)')
                ->where(
                    $friendSql->expr()->or(
                        'friends.friend_id = '.$friendSql->createNamedParameter($userId),
                        'friends.user_id = '.$friendSql->createNamedParameter($userId)
                    ),
//                    'friends.status != '.$friendSql->createNamedParameter('declined'),
                    'friends.deleted_at IS NULL')
            ;

            if ($this->getData('acceptedOnly', false)) {
                $friendSql->andWhere('friends.status = '.$friendSql->createNamedParameter('accepted'));
            }

            if ($this->getData('search', false)) {
                $search = '%' . $this->getData('search') . '%';
                $searchFields = $this->getData('searchFields', ['email','first_name','last_name']);
                $searchFFExpr = [];
                $searchFUExpr = [];
                foreach ($searchFields as $searchField) {
                    $searchFFExpr[] = sprintf('friend_friend.%s ilike %s',$searchField, $friendSql->createNamedParameter($search));
                    $searchFUExpr[] = sprintf('friend_user.%s ilike %s',$searchField, $friendSql->createNamedParameter($search));
                }

                $friendSql->andWhere(
                    $friendSql->expr()->or(
                        $friendSql->expr()->and(
                            'friend_friend.id != '.$friendSql->createNamedParameter($userId),
                            $friendSql->expr()->or(...$searchFFExpr)
                        ),
                        $friendSql->expr()->and(
                            'friend_user.id != '.$friendSql->createNamedParameter($userId),
                            $friendSql->expr()->or(...$searchFUExpr)
                        ),
                    )
                );
            }

            $loadUser = $this->container->get(LoadUser::class);
            $friendshipRequests = array_map(function ($friendship) use ($loadUser, $userId) {
                $friendPrefix = 'created_by_';
                if ($friendship['created_by_id'] === $userId){
                    $friendPrefix = 'friend_';
                }
                return [
                    'id' => $friendship['id'],
                    'status' => $friendship['status'],
                    'createdAt' => $friendship['created_at'],
                    'updatedAt' => $friendship['updated_at'],

                    'subscription'=>[
                        'allow_push'=>$friendship['allow_push'],
                        'allow_feed'=>$friendship['allow_feed'],
                    ],

                    'friend'=>$loadUser->setData(['userId'=>$friendship[$friendPrefix.'id'], 'forceRelationship'=>UserRelationship::RELATIONSHIP_FRIEND_ACCEPTED])(),
                    'messaging'=>[
                        'channel_id'=>$friendship['messaging_channel_id']??null,
                        'unread_count'=>$friendship['message_count']??0
                    ],
                    'created_by'=>$loadUser->setData(['userId'=>$friendship['created_by_id'], 'forceRelationship'=>UserRelationship::RELATIONSHIP_FRIEND_ACCEPTED])(),
                ];
            }, $friendSql->execute()->fetchAllAssociative());

            usort($friendshipRequests, function($a,$b) {
                return strtolower(trim($a['friend']['last_name']??'').trim($a['friend']['first_name']??'')) <=> strtolower(trim($b['friend']['last_name']??'').trim($b['friend']['first_name']??''));
            });

            $friendshipRequests = array_combine(array_column($friendshipRequests,'id'),$friendshipRequests);

            switch ($this->getData('order', 'display')) {
                case 'name':
                    usort($friendshipRequests, static fn ($a,$b) => strnatcasecmp(trim($a['friend']['last_name']??'').trim($a['friend']['first_name']??''),trim($b['friend']['last_name']??'').trim($b['friend']['first_name']??'')));
                    break;
                case 'status':
                    usort($friendshipRequests, static fn ($a,$b) => strnatcasecmp(trim($a['status']), trim($b['status'])));
                    break;
                case 'created':
                    usort($friendshipRequests, static fn ($a,$b) => new \DateTime($a['createdAt']) <=> new \DateTime($b['createdAt']));
                    break;
                case 'display':
                    // split into two arrays, one for accepted, one for pending
                    $accepted = [];
                    $pending = [
                        'sent' => [],
                        'received'=>[]
                    ];
                    foreach ($friendshipRequests as $friendshipRequest) {
                        if ($friendshipRequest['status'] === 'accepted') {
                            $accepted[] = $friendshipRequest;
                        } else {
                            if ($friendshipRequest['created_by']['id'] == $userId) {
                                $pending['sent'][] = $friendshipRequest;
                            } else {
                                $pending['received'][] = $friendshipRequest;
                            }
                        }
                    }

                    \Safe\usort($pending['sent'], static fn ($a,$b) => new \DateTime($a['createdAt']) <=> new \DateTime($b['createdAt']));
                    \Safe\usort($pending['received'], static fn ($a,$b) => new \DateTime($a['createdAt']) <=> new \DateTime($b['createdAt']));
                    \Safe\usort($accepted, static fn ($a,$b) => new \DateTime($a['createdAt']) <=> new \DateTime($b['createdAt']));

                    $friendshipRequests = array_merge($pending['sent'],$pending['received'],$accepted);
                    break;
                default:
                    $sortField = $this->getData('order');
                    usort($friendshipRequests, static function ($a, $b) use ($sortField) {
                        if (!array_key_exists($sortField, $a) || !array_key_exists($sortField, $b)) {
                            return 0;
                        }
                        return strnatcasecmp(trim($a[$sortField]), trim($b[$sortField]));
                    });
            }

            if (strtolower($this->getData('orderDirection', 'asc')) === 'desc') {
                $friendshipRequests = array_reverse($friendshipRequests);
            }

            return $friendshipRequests;
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'userId'=>new Constraints\Required(),
                'search'=>new Constraints\Optional(),
                'acceptedOnly'=>new Constraints\Optional()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
