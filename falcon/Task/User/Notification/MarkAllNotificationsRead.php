<?php

namespace Clubster\Falcon\Task\User\Notification;

use Clubster\Falcon\Model\Notification\Notification;
use Clubster\Falcon\Task\Falcon\Cache\InvalidateTag;
use Doctrine\DBAL\ParameterType;
use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class MarkAllNotificationsRead extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $updateQuery = $this->connection()->createQueryBuilder();
            $updateQuery->update('notifications.internal','internal')
                ->set('read',$updateQuery->createPositionalParameter(true, ParameterType::BOOLEAN))
                ->set('read_at',$updateQuery->createPositionalParameter(new \DateTime(), 'datetime'))
                ->where('internal.recipient_id = '.$updateQuery->createPositionalParameter($this->getData('userId')))
                ->andWhere('internal.status = '.$updateQuery->createPositionalParameter(Notification::STATUS_SENT))
                ->andWhere('internal.read_at IS NULL')
                ->andWhere('internal.send_at <= (NOW() AT TIME ZONE \'utc\')')
                ->andWhere('internal.deleted_at >= (NOW() AT TIME ZONE \'utc\') OR internal.deleted_at IS NULL');

            $this->container->get(InvalidateTag::class)->setData([
                'tag'=>'user-'. $this->getData('userId').'-notifications'
            ])();
            return $updateQuery->execute();
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'userId'=>new Constraints\Required(),
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
