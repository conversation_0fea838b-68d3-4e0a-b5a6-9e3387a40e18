<?php

namespace Clubster\Falcon\Task\Organization\Member\Invitation;

use Clubster\Falcon\Task\Organization\Member\LoadMember;
use Clubster\Falcon\Task\Organization\Member\Subscription\CreateSubscription;
use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class AcceptPreviouslyDeclinedInvitation extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $this->connection()->beginTransaction();
            $invitationSql = $this->connection()->createQueryBuilder();
            $invitationSql->select('member_id')
                        ->from('organizations.member_invitations')
                        ->where('member_invitations.id = '.$invitationSql->createNamedParameter($this->getData('invitationId')));
            $invitation = $invitationSql->executeQuery()->fetchAssociative();

            $updateInvitation = $this->connection()->createQueryBuilder();
            $updateInvitation->update('organizations.member_invitations')
                ->set('status',$updateInvitation->createNamedParameter('accepted'))
                ->where('id = '. $updateInvitation->createNamedParameter($this->getData('invitationId')))
                ->andWhere('status = '. $updateInvitation->createNamedParameter('declined'))
                ->executeQuery();

            $this->container->get(ClearPreviousInvitations::class)->setData([
                'memberId'=>$invitation['member_id'],
                'excludedInvitation'=>$this->getData('invitationId'),
                'status'=>'declined'
            ])();

//            $deleteInvitations = $this->connection()->createQueryBuilder();
//            $deleteInvitations->update('organizations.member_invitations')
//                ->set('deleted_at', $deleteInvitations->createNamedParameter('NOW()'))
//                ->where('member_invitations.member_id = '.$deleteInvitations->createNamedParameter($invitation['member_id']))
//                ->andWhere('member_invitations.id != '.$deleteInvitations->createNamedParameter($this->getData('invitationId')))
//                ->andWhere('status = '. $deleteInvitations->createNamedParameter('declined'))
//                ->executeQuery();

            $updateMember = $this->connection()->createQueryBuilder();
            $updateMember->update('organizations.members')
                ->set('status',$updateMember->createNamedParameter('accepted'))
                ->where('id = '.$updateMember->createNamedParameter($invitation['member_id']))
                ->executeQuery();

            $member = $this->container->get(LoadMember::class)->setData(['memberId'=> $invitation['member_id']])();

            $subscription = $this->container->get(CreateSubscription::class)->setData([
                'userId'=>$member['user_id'],
                'organizationId'=>$member['organization_id'],
                'forceSubscription' => true
            ])();

            $this->falcon->emit(__CLASS__, $this->getData());
//            $this->connection()->rollBack();
            $this->connection()->commit();
            return $this->getData();

        } catch (Exception $exception) {
            $this->connection()->rollBack();
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'invitationId'=> new Constraints\Required()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
