<?php

namespace Clubster\Falcon\Task\Organization\Member\Invitation;

use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class RemovePreviouslyDeclinedInvitation extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $this->connection()->beginTransaction();
            $invitationSql = $this->connection()->createQueryBuilder();
            $invitationSql->select('member_id')
                ->from('organizations.member_invitations')
                ->where('member_invitations.id = '.$invitationSql->createNamedParameter($this->getData('invitationId')));
            $invitation = $invitationSql->executeQuery()->fetchAssociative();

            $deleteInvitations = $this->connection()->createQueryBuilder();
            $deleteInvitations->update('organizations.member_invitations')
                ->set('deleted_at', $deleteInvitations->createNamedParameter('NOW()'))
                ->where('member_invitations.member_id = '.$deleteInvitations->createNamedParameter($invitation['member_id']))
                ->andWhere('status = '. $deleteInvitations->createNamedParameter('declined'))
                ->executeQuery();

            $this->connection()->commit();
            return $this->getData();
        } catch (Exception $exception) {
            $this->connection()->rollBack();
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'invitationId'=>new Constraints\Required()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
