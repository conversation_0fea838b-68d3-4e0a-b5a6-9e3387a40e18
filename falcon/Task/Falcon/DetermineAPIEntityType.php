<?php
namespace Clubster\Falcon\Task\Falcon;

use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class DetermineAPIEntityType extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            switch (strtolower(trim($this->getData('entityType')))) {
                case 'user':
                case 'app\\domain\\user\\entity\\user':
                    return 'App\\Domain\\User\\Entity\\User';
                case 'organization':
                case 'app\\domain\\organization\\entity\\organization':
                    return 'App\\Domain\\Organization\\Entity\\Organization';
                case 'personalgroup':
                case 'app\\domain\\group\\entity\\personalgroup':
                    return 'App\\Domain\\Group\\Entity\\PersonalGroup';
                case 'group':
                case 'app\\domain\\group\\entity\\group':
                    return 'App\\Domain\\Group\\Entity\\Group';
                case 'club':
                case 'app\\domain\\club\\entity\\club':
                    return 'App\\Domain\\Club\\Entity\\Club';
                default:
                    throw new \InvalidArgumentException(sprintf('The entity type "%s" is not mapped', $this->getData('entityType')));
            }
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'entityType' => new Constraints\Required()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
