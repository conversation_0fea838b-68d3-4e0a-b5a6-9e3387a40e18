<?php
namespace Clubster\Falcon\Task\Falcon;

use Carbon\Carbon;
use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use MongoDB\Client;
use Symfony\Component\Validator\Constraints;

class DeferTaskExecution extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            // a deferred task is a task that is not executed immediately, but is
            // scheduled to be ran at a later time
            // this happens by inserting the record into mongo db in the "deferred_tasks" collection
            // the record contains the task name and the payload and an optional schedule time
            // the task is then picked up by the "deferred_tasks" worker and executed
            $task = $this->getData('task');
            $payload = $this->getData('payload', []);

            $this->falcon->getLogger()->info('Deferring task execution', ['task' => $task, 'payload' => $payload]);

            // if schedule is set, convert it to a datetime object if it's not already
            $schedule = $this->getData('schedule', null);
            if ($schedule !== null) {
                $schedule = $this->getData('schedule');
                if ($schedule instanceof \DateTime) {
                    // convert to carbon
                    $schedule = Carbon::instance($schedule);
                }
                if (!($schedule instanceof \DateTime) && !($schedule instanceof Carbon)) {
                    $schedule = new Carbon($schedule);
                }
            }

            $mongo = $this->container->get(Client::class);
            if (empty($mongo)) {
                throw new Exception('Mongo client not found');
            }

            // call the cli script /app/pushoff.php with the task and payload as arguments

            $command = 'php /app/pushoff.php ' . $task . ' \'' . json_encode($payload) . '\' ' . $this->getRequest()->getRequestUser().' > /dev/null 2>&1 &';
            $this->falcon->getLogger()->info('Executing command', ['command' => $command]);
            exec($command);


//            $document = $mongo->selectCollection($_ENV['MONGO_DB_NAME'], 'deferred_tasks')->insertOne([
//                'task' => $task,
//                'payload' => $payload,
//                'schedule' => $schedule,
//                'userId' => $this->getRequest()->getRequestUser(),
//                'seen'=>false,
//                'created_at' => (new Carbon())->toAtomString(),
//            ]);

            return [
//                'deferred_task_id' => (string)$document->getInsertedId(),
                'task' => $task,
                'accepted' => true,
            ];
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'task' => new Constraints\NotBlank(),
                'payload' => new Constraints\Optional(new Constraints\Type('array')),
                'schedule' => new Constraints\Optional(),
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
