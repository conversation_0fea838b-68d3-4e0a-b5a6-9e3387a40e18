resolver 127.0.0.1;

upstream dynamic {
    server unix:/sock/docker.sock;
}

server {
    listen 80 default_server;
    server_name _;
    root /var/www/public;
    index   index.php;

    gzip on;
    gzip_min_length 100;
    gzip_buffers 4 32k;
    gzip_types text/plain application/javascript text/xml text/css;
    gzip_vary on;
    add_header X-WWW-Handler $CLUBSTER_WEB_INTERNAL_DNS;

    # Add CORS headers
    set $cors_origin "";
    if ($http_origin ~* (https?://.*\.clubster\.com)) {
        set $cors_origin $http_origin;
    }
    add_header Access-Control-Allow-Origin $cors_origin;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
    add_header Access-Control-Allow-Headers "Origin, Authorization, Accept, Content-Type";

    client_max_body_size 500M;
    client_body_timeout 240s;

    location / {
        # try to serve file directly, fallback to app.php
        try_files $uri /index.php$is_args$args;
    }

    location ~* \.(?:ico|css|js|gif|jpe?g|png|woff|woff2|ttf|eot|svg|otf|mp4|webm|ogg|mp3|wav|flac|aac)$ {
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
        expires max;
        access_log off;
    }

    # PROD
    location ~ ^/index\.php(/|$) {
        client_max_body_size 500M;
        client_body_timeout 240s;
        fastcgi_pass dynamic;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        fastcgi_param HTTPS true;
        fastcgi_buffers  16 16k;
        fastcgi_buffer_size  32k;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        fastcgi_param   REMOTE_ADDR $http_x_real_ip;
        set_real_ip_from 127.0.0.1/32;
        real_ip_header X-Forwarded-For;
        real_ip_recursive on;
        internal;
    }

    location ~ \.php$ {
        #return 404;
        client_max_body_size 500M;
        client_body_timeout 240s;
        fastcgi_pass dynamic;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        fastcgi_param HTTPS true;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        fastcgi_param   REMOTE_ADDR $http_x_real_ip;
        fastcgi_buffers  16 16k;
        fastcgi_buffer_size  32k;
        set_real_ip_from 127.0.0.1/32;
        real_ip_header X-Forwarded-For;
        real_ip_recursive on;
    }

    # Turn off logging for favicons and robots.txt
    location ~ ^/android-chrome-|^/apple-touch-|^/browserconfig.xml$|^/coast-|^/favicon.ico$|^/favicon-|^/firefox_app_|^/manifest.json$|^/manifest.webapp$|^/mstile-|^/open-graph.png$|^/twitter.png$|^/yandex- {
            log_not_found off;
            access_log off;
    }

    location = /robots.txt {
            log_not_found off;
            access_log off;
    }
}
